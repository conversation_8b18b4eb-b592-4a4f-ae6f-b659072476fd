package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "VPC关系表")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVPCRelationBean.class})
public class SpVPCRelationBean extends SpRecordBean {

    @ApiModelProperty(value = "VpcID")
    private Integer vpcId;

    @ApiModelProperty(value = "VPC名称")
    private String vpcName;
    
    @ApiModelProperty(value = "VPC显示名称")
    private String vpcDisplayName;

    @ApiModelProperty(value = "OVDC网络ID")
    private Integer ovdcNetworkId;

    @ApiModelProperty(value = "OVDC网络名称")
    private String ovdcNetworkName;

    @ApiModelProperty(value = "OVDC网络显示名称")
    private String ovdcNetworkDisplayName;

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public String getVpcName() {
		return vpcName;
	}

	public void setVpcName(String vpcName) {
		this.vpcName = vpcName;
	}

	public String getVpcDisplayName() {
		return vpcDisplayName;
	}

	public void setVpcDisplayName(String vpcDisplayName) {
		this.vpcDisplayName = vpcDisplayName;
	}

	public Integer getOvdcNetworkId() {
		return ovdcNetworkId;
	}

	public void setOvdcNetworkId(Integer ovdcNetworkId) {
		this.ovdcNetworkId = ovdcNetworkId;
	}

	public String getOvdcNetworkName() {
		return ovdcNetworkName;
	}

	public void setOvdcNetworkName(String ovdcNetworkName) {
		this.ovdcNetworkName = ovdcNetworkName;
	}

	public String getOvdcNetworkDisplayName() {
		return ovdcNetworkDisplayName;
	}

	public void setOvdcNetworkDisplayName(String ovdcNetworkDisplayName) {
		this.ovdcNetworkDisplayName = ovdcNetworkDisplayName;
	}


}
