package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "配额明细")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderQuotaDetailBean.class})
public class UpOrderQuotaDetailBean extends SpRecordBean {

	@ApiModelProperty(value = "子协议号")
	private String subCode;
	
	@ApiModelProperty(value = "类型")
	private ProductType type;
	
	@ApiModelProperty(value = "类型")
	private QuotaCatalog catalog;
	
	@ApiModelProperty(value = "产品编码")
	private String productCode;
	
	@ApiModelProperty(value = "下属协议")
	private UpOrderQuotaDetailBean subQuotaDetail;
	
	@ApiModelProperty(value = "状态")
	private QuotaDetailStatus quotaDetailStatus;
	
	@ApiModelProperty(value = "数值")
	private String value;

	@ApiModelProperty(value = "租户ID")
	private Integer spOrgId;

	@ApiModelProperty(value = "region")
	private SpRegionBean region;

	@ApiModelProperty(value = "cpu")
	private Integer cpu;

	@ApiModelProperty(value = "内存")
	private Integer memoryG;

	@ApiModelProperty(value = "存储")
	private Integer diskG;

	@ApiModelProperty(value = "磁盘类型")
	private Integer diskType;

	@ApiModelProperty(value = "自定义")
	private Boolean isCustom;

	private UpProductSystemEnums.QuotaDetailChannel channel;

	public String getSubCode() {
		return subCode;
	}

	public void setSubCode(String subCode) {
		this.subCode = subCode;
	}

	public ProductType getType() {
		return type;
	}

	public void setType(ProductType type) {
		this.type = type;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public UpOrderQuotaDetailBean getSubQuotaDetail() {
		return subQuotaDetail;
	}

	public void setSubQuotaDetail(UpOrderQuotaDetailBean subQuotaDetail) {
		this.subQuotaDetail = subQuotaDetail;
	}

	public QuotaCatalog getCatalog() {
		return catalog;
	}

	public void setCatalog(QuotaCatalog catalog) {
		this.catalog = catalog;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public QuotaDetailStatus getQuotaDetailStatus() {
		return quotaDetailStatus;
	}

	public void setQuotaDetailStatus(QuotaDetailStatus quotaDetailStatus) {
		this.quotaDetailStatus = quotaDetailStatus;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public SpRegionBean getRegion() {
		return region;
	}

	public void setRegion(SpRegionBean region) {
		this.region = region;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemoryG() {
		return memoryG;
	}

	public void setMemoryG(Integer memoryG) {
		this.memoryG = memoryG;
	}

	public Integer getDiskG() {
		return diskG;
	}

	public void setDiskG(Integer diskG) {
		this.diskG = diskG;
	}

	public Integer getDiskType() {
		return diskType;
	}

	public void setDiskType(Integer diskType) {
		this.diskType = diskType;
	}

	public Boolean getCustom() {
		return isCustom;
	}

	public void setCustom(Boolean custom) {
		isCustom = custom;
	}

	public UpProductSystemEnums.QuotaDetailChannel getChannel() {
		return channel;
	}

	public void setChannel(UpProductSystemEnums.QuotaDetailChannel channel) {
		this.channel = channel;
	}
}
