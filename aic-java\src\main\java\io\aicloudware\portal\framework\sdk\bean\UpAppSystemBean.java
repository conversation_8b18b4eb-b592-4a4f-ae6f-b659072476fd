package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpAppSystemStatus;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "应用系统")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpAppSystemBean.class})
public class UpAppSystemBean extends RecordBean {

    @ApiModelProperty(value = "所有者用户ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者用户名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "申请原因")
    private String comment;

    private Integer regionId;

    private String regionName;

    private Date recordTm;

    private UpAppSystemBean[] children;

    private Integer parentId;

    private String parentName;

    @ApiModelProperty(value = "应用系统状态")
    private UpAppSystemStatus appSystemStatus;

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Date getRecordTm() {
        return recordTm;
    }

    public void setRecordTm(Date recordTm) {
        this.recordTm = recordTm;
    }

    public UpAppSystemBean[] getChildren() {
        return children;
    }

    public void setChildren(UpAppSystemBean[] children) {
        this.children = children;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public UpAppSystemStatus getAppSystemStatus() {
        return appSystemStatus;
    }

    public void setAppSystemStatus(UpAppSystemStatus appSystemStatus) {
        this.appSystemStatus = appSystemStatus;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }
}
