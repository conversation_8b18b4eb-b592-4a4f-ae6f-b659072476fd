package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.bean.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public interface IUpAppSystemService {

    void save(UpAppSystemBean bean);

    void saveUsers(UpAppSystemUserRelationListBean listBean);

    RecordBean[] listRelationByAppSystemId(Integer id);

    void disable(Integer id);

    UpAppSystemBean[] listByOwnerId(Integer id);

    void sync();

    void enable(Integer id);
}
