package io.aicloudware.portal.framework.sdk.bean;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpOperationLevel;
import io.aicloudware.portal.framework.sdk.contants.UpOperationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "操作日志")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpOperationLogBean extends RecordBean {

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    private String groupName;

    @ApiModelProperty(value = "应用系统ID")
    private Integer appSystemId;

    @ApiModelProperty(value = "应用系统名称")
    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "操作员ID")
    private Integer ownerId;

    @ApiModelProperty(value = "操作员名称")
    private String ownerName;

    @ApiModelProperty(value = "操作员姓名")
    private String ownerDisplayName;

    @ApiModelProperty(value = "远程IP")
    private String remoteIp;

    @ApiModelProperty(value = "操作类型")
    private UpOperationType operationType;

    @ApiModelProperty(value = "操作级别")
    private UpOperationLevel operationLevel;

    @ApiModelProperty(value = "操作状态")
    private UpOperationStatus operationStatus;

    @ApiModelProperty(value = "目标对象")
    private String targetTable;

    @ApiModelProperty(value = "目标对象ID")
    private Integer targetId;

    @ApiModelProperty(value = "目标对象名称")
    private String targetName;

    @ApiModelProperty(value = "操作时间")
    private Date requestTm;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public UpOperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(UpOperationType operationType) {
        this.operationType = operationType;
    }

    public UpOperationLevel getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(UpOperationLevel operationLevel) {
        this.operationLevel = operationLevel;
    }

    public UpOperationStatus getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(UpOperationStatus operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getTargetTable() {
        return targetTable;
    }

    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable;
    }

    public Integer getTargetId() {
        return targetId;
    }

    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public Date getRequestTm() {
        return requestTm;
    }

    public void setRequestTm(Date requestTm) {
        this.requestTm = requestTm;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

}
