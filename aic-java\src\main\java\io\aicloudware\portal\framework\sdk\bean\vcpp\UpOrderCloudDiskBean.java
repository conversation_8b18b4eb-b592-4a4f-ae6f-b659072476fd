package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudStorageChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderCloudDiskBean.class})
public class UpOrderCloudDiskBean extends SpRecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "云盘类型")
	private DiskType diskType;
	
	@ApiModelProperty(value = "付费方式")
	private PaymentType paymentType;
	
	@ApiModelProperty(value = "计费方式")
	private CloudStorageChargeType chargeType;
	
	@ApiModelProperty(value = "容量")
	private Integer diskGB;
	
	@ApiModelProperty(value = "磁盘序号")
	private Integer diskNumber;
	
	@ApiModelProperty(value = "绑定云服务器")
	private Integer cloudServerId;
	
	@ApiModelProperty(value = "所有人ID")
	private Integer ownerId;
	
	@ApiModelProperty(value = "类型")
	private SpVmDiskType type;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "云盘产品配置ID")
	private Integer diskConfigId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public CloudStorageChargeType getChargeType() {
		return chargeType;
	}

	public void setChargeType(CloudStorageChargeType chargeType) {
		this.chargeType = chargeType;
	}

	public Integer getDiskGB() {
		return diskGB;
	}

	public void setDiskGB(Integer diskGB) {
		this.diskGB = diskGB;
	}

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public SpVmDiskType getType() {
		return type;
	}

	public void setType(SpVmDiskType type) {
		this.type = type;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getDiskConfigId() {
		return diskConfigId;
	}

	public void setDiskConfigId(Integer diskConfigId) {
		this.diskConfigId = diskConfigId;
	}

	public Integer getDiskNumber() {
		return diskNumber;
	}

	public void setDiskNumber(Integer diskNumber) {
		this.diskNumber = diskNumber;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public DiskType getDiskType() {
		return diskType;
	}

	public void setDiskType(DiskType diskType) {
		this.diskType = diskType;
	}

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	

}
