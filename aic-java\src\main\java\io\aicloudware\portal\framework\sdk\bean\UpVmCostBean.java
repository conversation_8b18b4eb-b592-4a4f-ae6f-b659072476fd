package io.aicloudware.portal.framework.sdk.bean;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机费用")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpVmCostBean extends RecordBean {

    @ApiModelProperty(value = "预留ID")
    private Integer reservationId;

    @ApiModelProperty(value = "预留名称")
    private String reservationName;

    @ApiModelProperty(value = "预留显示名称")
    private String reservationDisplayName;

    @ApiModelProperty(value = "应用系统ID")
    private Integer appSystemId;

    @ApiModelProperty(value = "应用系统名称")
    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    private String groupName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "日期")
    private Integer date;

    @ApiModelProperty(value = "年度")
    private Integer fiscalYear;

    @ApiModelProperty(value = "CPU(核)")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存(GB)")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘(GB)")
    private Integer diskGB;

    @ApiModelProperty(value = "CPU价格/核")
    private Double cpuPrice;

    @ApiModelProperty(value = "内存价格/GB")
    private Double memoryPrice;

    @ApiModelProperty(value = "磁盘价格/GB")
    private Double diskPrice;

    @ApiModelProperty(value = "CPU(元)")
    private Double cpuMoney;

    @ApiModelProperty(value = "内存(元)")
    private Double memoryMoney;

    @ApiModelProperty(value = "磁盘(元)")
    private Double diskMoney;

    @ApiModelProperty(value = "合计费用")
    private Double totalMoney;

    @ApiModelProperty(value = "虚机数量")
    private Integer vmNum;

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public Integer getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(Integer fiscalYear) {
        this.fiscalYear = fiscalYear;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public Double getCpuPrice() {
        return cpuPrice;
    }

    public void setCpuPrice(Double cpuPrice) {
        this.cpuPrice = cpuPrice;
    }

    public Double getMemoryPrice() {
        return memoryPrice;
    }

    public void setMemoryPrice(Double memoryPrice) {
        this.memoryPrice = memoryPrice;
    }

    public Double getDiskPrice() {
        return diskPrice;
    }

    public void setDiskPrice(Double diskPrice) {
        this.diskPrice = diskPrice;
    }

    public Double getCpuMoney() {
        return cpuMoney;
    }

    public void setCpuMoney(Double cpuMoney) {
        this.cpuMoney = cpuMoney;
    }

    public Double getMemoryMoney() {
        return memoryMoney;
    }

    public void setMemoryMoney(Double memoryMoney) {
        this.memoryMoney = memoryMoney;
    }

    public Double getDiskMoney() {
        return diskMoney;
    }

    public void setDiskMoney(Double diskMoney) {
        this.diskMoney = diskMoney;
    }

    public Double getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(Double totalMoney) {
        this.totalMoney = totalMoney;
    }

    public Integer getVmNum() {
        return vmNum;
    }

    public void setVmNum(Integer vmNum) {
        this.vmNum = vmNum;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getReservationId() {
        return reservationId;
    }

    public void setReservationId(Integer reservationId) {
        this.reservationId = reservationId;
    }

    public String getReservationName() {
        return reservationName;
    }

    public void setReservationName(String reservationName) {
        this.reservationName = reservationName;
    }

    public String getReservationDisplayName() {
        return reservationDisplayName;
    }

    public void setReservationDisplayName(String reservationDisplayName) {
        this.reservationDisplayName = reservationDisplayName;
    }

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

}
