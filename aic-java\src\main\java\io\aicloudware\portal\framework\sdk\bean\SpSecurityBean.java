package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpAFDeployStatus;
import io.aicloudware.portal.framework.sdk.contants.SpDeployStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "安全组件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpSecurityBean.class})
public class SpSecurityBean extends SpRecordBean {

    @ApiModelProperty(value = "UUID")
    private String spUuid;
    
    @ApiModelProperty(value = "所有者")
	private Integer ownerId;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "产品编码")
	private String productCode;
	
	@ApiModelProperty(value = "类型")
	private ProductVmSetType securityType;
	
	@ApiModelProperty(value = "url")
	private String url;
	
	@ApiModelProperty(value = "deployStatus")
	private SpDeployStatus deployStatus;

	@ApiModelProperty(value = "afDeployStatus")
	private SpAFDeployStatus afDeployStatus;
	
	public String getSpUuid() {
		return spUuid;
	}

	public void setSpUuid(String spUuid) {
		this.spUuid = spUuid;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public ProductVmSetType getSecurityType() {
		return securityType;
	}

	public void setSecurityType(ProductVmSetType securityType) {
		this.securityType = securityType;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public SpDeployStatus getDeployStatus() {
		return deployStatus;
	}

	public void setDeployStatus(SpDeployStatus deployStatus) {
		this.deployStatus = deployStatus;
	}

	public SpAFDeployStatus getAfDeployStatus() {
		return afDeployStatus;
	}

	public void setAfDeployStatus(SpAFDeployStatus afDeployStatus) {
		this.afDeployStatus = afDeployStatus;
	}
}
