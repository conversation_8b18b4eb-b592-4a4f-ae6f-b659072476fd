package io.aicloudware.portal.api_vcpp.service.quota;

import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

import java.util.List;

public interface IUpOrderQuotaService {

	UpOrderQuotaBean[] queryQuota(String type, QuotaCatalog catalog);
	
	void finishQuotaDetail(Integer quotaDetailId);

	void deployQuotaDetail(Integer quotaDetailId);

	void deployQuotaDetail(UpOrderQuotaDetail quotaDetail);

	void openQuotaDetail(Integer quotaDetailId);

//	UpOrderQuotaBean add(UpOrderQuotaBean bean);

	UpOrderQuotaBean add(UpOrderQuotaDetailBean bean);

	void deleteQuotaDetail(Integer id);

	List<UpOrderQuotaDetail> queryQuotaDetail(SpOrg spOrg, ProductType type, QuotaCatalog catalog);

	UpOrderQuotaDetail getQuotaDetail(SpOrg spOrg, SpRegionEntity region, String doorOrderItemId);
}