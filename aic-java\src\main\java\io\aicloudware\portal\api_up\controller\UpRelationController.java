package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpRelation;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.bean.UpRelationResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/relation")
@Api(value = "/relation", description = "关系", position = 550)
public class UpRelationController extends BaseUpController<UpRelation, UpRelationBean, UpRelationResultBean> {

//    @Autowired
//    private IUpRelationService relationService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRelationResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryRelation(@ApiParam(value = "查询条件") @RequestBody UpRelationSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRelationBean.class)})
//    @ResponseBody
//    public ResponseBean getRelation(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRelationBean.class)})
//    @ResponseBody
//    public ResponseBean addRelation(@ApiParam(value = "实例对象") @RequestBody UpRelationBean bean) {
//        UpRelationListBean listBean = new UpRelationListBean();
//        listBean.setDataList(new UpRelationBean[]{bean});
//        relationService.createRelation(listBean);
//        return ResponseBean.success(bean);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRelationListBean.class)})
//    @ResponseBody
//    public ResponseBean addRelation(@ApiParam(value = "实例对象") @RequestBody UpRelationListBean bean) {
//        relationService.createRelation(bean);
//        return ResponseBean.success(bean);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRelation(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        relationService.deleteRelation(Arrays.asList(id));
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRelation(@ApiParam(value = "对象ID列表") @RequestBody SpSimpleOperateBean bean) {
//        relationService.deleteRelation(Arrays.asList(bean.getIdList()));
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    // =================================================================================================================
//
//    @RequestMapping(value = "/warrant", method = RequestMethod.POST)
//    @ApiOperation(notes = "/warrant", httpMethod = "POST", value = "对象直接授权")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean add(@ApiParam(value = "实例对象") @RequestBody UpWarrantListBean bean) {
//        relationService.directlyWarrant(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
}
