package io.aicloudware.portal.api_vcpp.service.bill;

import io.aicloudware.portal.api_vcpp.entity.UpCouponItem;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.api_vcpp.entity.UpServiceUsageRecord;
import io.aicloudware.portal.framework.sdk.bean.UpServiceUsageRecordBean;
import io.aicloudware.portal.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Comparator;

@Service
@Transactional
public class UpServiceUsageRecordService extends BaseService implements IUpServiceUsageRecordService {

    @Override
    public List<UpServiceUsageRecordBean> getChildList(Integer id) {
        UpServiceUsageRecord record = dao.load(UpServiceUsageRecord.class, id);
        if (record == null) {
            return new ArrayList<>(); // 如果记录未找到，返回空数组
        }
        UpServicePlan servicePlan = record.getUpServicePlan();
        if (servicePlan == null || servicePlan.getPrice() == null) {
             // 处理服务计划或价格缺失的情况，可以抛出异常或返回空
            return new ArrayList<>();
        }

        List<UpCouponItem> discountItems = new ArrayList<>();
//        if (record.getServiceDiscount() != null && record.getServiceDiscount().getServiceDiscountItems() != null) {
//            discountItems.addAll(record.getServiceDiscount().getServiceDiscountItems());
//            // 确保 discountItems 按月份升序排序，这是逻辑所要求的
//            discountItems.sort(Comparator.comparingInt(UpCouponItem::getMonth));
//        }


        Date now = new Date();
        Date startTime = record.getServicePlanStartTime();

        if (startTime == null || startTime.after(now)) {
             // 如果开始时间为空或在未来，则尚无计费记录
            return new ArrayList<>();
        }

        Date endTime = record.getServicePlanEndTime() == null ? now : record.getServicePlanEndTime();

        // 如果结束时间早于开始时间，说明数据有问题，返回空
        if (endTime.before(startTime)) {
            return new ArrayList<>();
        }

        List<UpServiceUsageRecordBean> resultList = new ArrayList<>();
        Calendar currentStartCal = Calendar.getInstance();
        currentStartCal.setTime(startTime);

        Calendar nextMonthCal = Calendar.getInstance();
        nextMonthCal.setTime(startTime);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        BigDecimal basePrice = servicePlan.getPrice();
        int monthCounter = 0; // 月份计数器，第一个月从 1 开始

        while (currentStartCal.getTime().before(endTime)) {
            monthCounter++;
            // nextMonthCal.setTime(currentStartCal.getTime()); // 这行不再需要，因为endCal是独立计算的
            // nextMonthCal.add(Calendar.MONTH, 1); // 这行不再需要
            // nextMonthCal.add(Calendar.DATE, -1); // 原计算方式：结束日期是下个月开始日期的前一天，这会导致跨月问题，例如1月3日开始，结束日期会是2月2日

            // 确定此计费周期的结束日期（不能超过总的 endTime）
            // Date currentEndDate = nextMonthCal.getTime(); // 原计算方式的结束日期
            // 为了确保结束日期是开始日期的一个月后对应的那一天减1，我们直接在开始日期的基础上加一个月再减一天
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(currentStartCal.getTime());
            endCal.add(Calendar.MONTH, 1);
            endCal.add(Calendar.SECOND, -1);
            Date currentEndDate = endCal.getTime();


            if (currentEndDate.after(endTime)) {
                currentEndDate = endTime; // 如果计算出的结束日期超过了总结束时间，则使用总结束时间
            }
             // 如果计算出的结束日期甚至早于开始日期（可能发生在最后一个不足一个月的周期且endTime恰好是某月第一天），则直接使用endTime
            if (currentEndDate.before(currentStartCal.getTime())) {
                 currentEndDate = endTime;
            }


            // 查找当前月份适用的折扣
            int currentDiscount = 100; // 默认：无折扣
            for (int i = discountItems.size() - 1; i >= 0; i--) {
                UpCouponItem item = discountItems.get(i);
                // month 是从第几个月开始应用折扣
                if (item.getMonth() != null && monthCounter >= item.getMonth()) {
                    currentDiscount = item.getDiscount() != null ? item.getDiscount() : 100;
                    break; // 找到了最新的适用折扣
                }
            }

            // 计算折扣乘数
            BigDecimal discountMultiplier = BigDecimal.valueOf(currentDiscount).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP); // 使用4位小数以保证精度

            BigDecimal calculatedPrice; // 在此声明，因为满月和不足月的计算方式不同

            // 计算此周期的理论结束时间（如果是一个完整的月）
            Calendar theoreticalFullMonthEndCal = Calendar.getInstance();
            theoreticalFullMonthEndCal.setTime(currentStartCal.getTime());
            theoreticalFullMonthEndCal.add(Calendar.MONTH, 1);
            // 注意：为了计算时长，我们不减去一天

            // 检查这是否是最后一个周期，并且它是否比一个完整的月周期短
            // currentEndDate 是此周期的实际结束时间（可能是月底或总结束时间）
            // theoreticalFullMonthEndCal.getTime() 是下一个月周期的开始时间
            boolean isLastPartialMonth = currentEndDate.equals(endTime) && endTime.before(theoreticalFullMonthEndCal.getTime());

            if (isLastPartialMonth) {
                // --- 最后时段不足一个月：按分钟比例计算 ---
                long periodStartTimeMillis = currentStartCal.getTimeInMillis();
                long periodEndTimeMillis = endTime.getTime(); // 使用总的结束时间
                long theoreticalFullMonthEndTimeMillis = theoreticalFullMonthEndCal.getTimeInMillis();

                // 理论上完整月份的持续时间（分钟）
                long fullMonthDurationMinutes = (theoreticalFullMonthEndTimeMillis - periodStartTimeMillis) / (60 * 1000);
                // 此部分月份实际使用的持续时间（分钟）
                long actualDurationMinutes = (periodEndTimeMillis - periodStartTimeMillis) / (60 * 1000);

                // 计算应用折扣后的 *完整月份* 的价格（保持精度）
                BigDecimal fullMonthPrice = basePrice.multiply(discountMultiplier);

                if (fullMonthDurationMinutes > 0 && actualDurationMinutes >= 0) {
                    // 使用 BigDecimal 进行精确的比例计算
                    BigDecimal proportion = BigDecimal.valueOf(actualDurationMinutes)
                                                      .divide(BigDecimal.valueOf(fullMonthDurationMinutes), 10, RoundingMode.HALF_UP); // 提高除法精度
                    calculatedPrice = fullMonthPrice.multiply(proportion).setScale(4, RoundingMode.HALF_UP); // 最终价格四舍五入到2位小数
                } else {
                    // 避免除以零或负时长；如果完整月份时长无效或实际时长为负，则价格为零
                    calculatedPrice = BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
                }

            } else {
                // --- 完整月份 或 最后一个月正好在周期边界结束 ---
                calculatedPrice = basePrice.multiply(discountMultiplier).setScale(4, RoundingMode.HALF_UP); // 标准计算
            }

            // --- 填充 Bean ---
            // (注意：日期格式化现在只用于可能的日志或描述，Bean 中存储 Date 对象)
            // SimpleDateFormat descriptionSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm"); // 如果需要，可以为描述添加时间

            // 创建并添加 bean
            UpServiceUsageRecordBean bean = new UpServiceUsageRecordBean();
            bean.setInstanceId(record.getInstanceId());
            bean.setInstanceName(record.getInstanceName());
            bean.setActualCost(calculatedPrice); // 使用最终计算出的价格
            bean.setOrderId(record.getOrderId());
            bean.setOwnerId(record.getOwner().getId());
            bean.setOwnerDisplayName(record.getOwner().getDisplayName());
            bean.setOwnerName(record.getOwner().getName());
            bean.setServicePlanStartTime(currentStartCal.getTime()); // 当前周期的开始时间
            bean.setServicePlanEndTime(currentEndDate);           // 当前周期的结束时间 (可能是部分月份的结束时间)
            bean.setServicePlanType(record.getServicePlanType());
            bean.setDiscount(currentDiscount);
            bean.setOriginalCost(basePrice);
            bean.setBillingCycle(monthCounter);
//            bean.set
            // 如果需要，可以设置描述字段
            // String description = String.format("%s ~ %s，产生计费 %.2f RMB",
            //        descriptionSdf.format(currentStartCal.getTime()),
            //        descriptionSdf.format(currentEndDate),
            //        calculatedPrice);
            // bean.setDescription(description);
            resultList.add(bean);

            // 移动到下个月的开始日期
            currentStartCal.add(Calendar.MONTH, 1);

             // 安全中断：如果开始日期没有前进（理论上不太可能，除非月份计算有问题），退出以防无限循环
            // if (currentStartCal.getTime().equals(endCal.getTime())) { // 比较下一次的开始和本次的结束没有意义
            //      break;
            // }

             // 如果下一次迭代的开始日期已经晚于或等于结束日期，则中断
             if (!currentStartCal.getTime().before(endTime)) {
                 break;
             }
        }
//        BigDecimal totalCost = resultList.stream().map(UpServiceUsageRecordBean::getActualCost).reduce(BigDecimal.ZERO, BigDecimal::add);
//        record.setActualCost(totalCost.setScale(2, RoundingMode.HALF_UP));
//        dao.update(record);
        return resultList;
    }
}
