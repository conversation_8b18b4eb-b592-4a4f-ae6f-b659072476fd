package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpFormDefinitionBean;

public interface IUpFormDefinitionService {

	void add(UpFormDefinitionBean bean);

	void update(UpFormDefinitionBean bean);

	void delete(Integer id);

	UpFormDefinitionBean[] list(UpFormDefinitionBean params);

	UpFormDefinitionBean getById(Integer id);

	UpFormDefinitionBean[] query(SearchBean<UpFormDefinitionBean> searchBean);
}
