package io.aicloudware.portal.framework.dao;

import com.google.common.collect.ImmutableMap;
import io.aicloudware.portal.api_up.entity.UpVmRecord;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Repository
public class StaticsDao extends BaseDao implements IStaticsDao {

    @Autowired
    private IQueryDao queryDao;

    @Override
    public void deleteStaticsData(Integer fromDate) {
        String hql = "delete from UpVmUsage t where t.date >= :fromDate";
        queryDao.doExecuteHql(hql, MapUtil.of("fromDate", fromDate));
        hql = "delete from VcHostUsage t where t.date >= :fromDate";
        queryDao.doExecuteHql(hql, MapUtil.of("fromDate", fromDate));
        hql = "delete from UpVmCost t where t.date >= :fromDate";
        queryDao.doExecuteHql(hql, MapUtil.of("fromDate", fromDate));
    }

    @Override
    public Integer getStaticsFromDate() {
        String hql = "select max(date) from UpVmUsage";
        List<Integer> dateList = queryDao.queryHql(hql, null);
        Integer fromDate = Utility.toZero(ListUtil.first(dateList));
        hql = "select max(date) from VcHostUsage";
        dateList = queryDao.queryHql(hql, null);
        fromDate = Math.min(fromDate, Utility.toZero(ListUtil.first(dateList)));
        hql = "select max(date) from UpVmCost";
        dateList = queryDao.queryHql(hql, null);
        fromDate = Math.min(fromDate, Utility.toZero(ListUtil.first(dateList)));
        if (Utility.isNotZero(fromDate)) {
            Calendar calendar = FormatUtil.parseCalendar(fromDate);
            calendar.add(Calendar.DATE, 1);
            fromDate = FormatUtil.formatCalendar(calendar);
            deleteStaticsData(fromDate);
        } else {
            deleteStaticsData(fromDate);
            hql = "select min(createDt) from SpVm where status != :status";
            Date date = ListUtil.first(queryDao.queryHql(hql, ImmutableMap.of("status", RecordStatus.deleted)));
            if (date != null) {
                fromDate = FormatUtil.formatDate(FormatUtil.formatDate(date));
            } else {
                fromDate = FormatUtil.formatCalendar(Calendar.getInstance());
            }
        }
        return fromDate;
    }

    @Override
    public List<UpVmRecord> getVmRecordList(Integer toDate) {
        String hql = "from UpVmRecord where recordDt <= :toDate order by vm asc, recordDt desc";
        List<UpVmRecord> vmRecordList = queryDao.queryHql(hql, ImmutableMap.of("toDate", toDate));
        for (int i = 0; i < vmRecordList.size(); i++) {
            Integer vmId = vmRecordList.get(i).getVm().getId();
            for (int j = vmRecordList.size() - 1; j > i; j--) {
                if (vmRecordList.get(j).getVm().getId().equals(vmId)) {
                    vmRecordList.remove(j);
                }
            }
        }
        return vmRecordList;
    }
}
