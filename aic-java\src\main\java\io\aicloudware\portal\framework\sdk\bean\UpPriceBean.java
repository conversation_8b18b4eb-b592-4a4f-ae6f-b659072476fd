package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "资源价格")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpPriceBean.class})
public class UpPriceBean extends RecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 60)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 70)
    private String upTenantName;

    @ApiModelProperty(value = "预留ID")
    private Integer reservationId;

    @ApiModelProperty(value = "预留名称")
    private String reservationName;

    @ApiModelProperty(value = "预留显示名称")
    private String reservationDisplayName;

    @ApiModelProperty(value = "蓝图ID")
    private Integer blueprintId;

    @ApiModelProperty(value = "蓝图名称")
    private String blueprintName;

    @ApiModelProperty(value = "蓝图显示名称")
    private String blueprintDisplayName;

    @ApiModelProperty(value = "起始日期")
    private Integer startDt;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "CPU单价（每核每天）")
    private Double cpuNum;

    @ApiModelProperty(value = "内存单价（每GB每天）")
    private Double memoryGB;

    @ApiModelProperty(value = "存储单价（每GB每天）")
    private Double diskGB;

    @ApiModelProperty(value = "IP单价（每个每天）")
    private Double ipNum;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getReservationId() {
        return reservationId;
    }

    public void setReservationId(Integer reservationId) {
        this.reservationId = reservationId;
    }

    public String getReservationName() {
        return reservationName;
    }

    public void setReservationName(String reservationName) {
        this.reservationName = reservationName;
    }

    public Integer getBlueprintId() {
        return blueprintId;
    }

    public void setBlueprintId(Integer blueprintId) {
        this.blueprintId = blueprintId;
    }

    public String getBlueprintName() {
        return blueprintName;
    }

    public void setBlueprintName(String blueprintName) {
        this.blueprintName = blueprintName;
    }

    public Integer getStartDt() {
        return startDt;
    }

    public void setStartDt(Integer startDt) {
        this.startDt = startDt;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Double getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Double cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Double getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Double memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Double getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Double diskGB) {
        this.diskGB = diskGB;
    }

    public Double getIpNum() {
        return ipNum;
    }

    public void setIpNum(Double ipNum) {
        this.ipNum = ipNum;
    }

    public String getReservationDisplayName() {
        return reservationDisplayName;
    }

    public void setReservationDisplayName(String reservationDisplayName) {
        this.reservationDisplayName = reservationDisplayName;
    }

    public String getBlueprintDisplayName() {
        return blueprintDisplayName;
    }

    public void setBlueprintDisplayName(String blueprintDisplayName) {
        this.blueprintDisplayName = blueprintDisplayName;
    }

}
