package io.aicloudware.portal.framework.dao;

import io.aicloudware.portal.framework.entity.IEntity;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public final class QueryDao extends BaseDao implements IQueryDao {

    @Override
    public <T> List<T> queryHql(String hql, Map<String, Object> paramMap) {
        return queryHqlList(hql, paramMap);
    }

    @Override
    public int doExecuteHql(String hql, Map<String, Object> paramMap) {
        return executeHql(hql, paramMap);
    }

    @Override
    public <T> List<T> querySql(String sql, Map<String, Object> paramMap) {
        return querySqlList(sql, paramMap);
    }

    @Override
    public <T extends IEntity> List<T> querySql(String sql, Map<String, Object> paramMap, Class<T> returnType) {
        return querySqlList(sql, paramMap, returnType);
    }

    @Override
    public int doExecuteSql(String sql, Map<String, Object> paramMap) {
        return executeSql(sql, paramMap);
    }

    @Override
    public void execute(Work work) {
        executeBatch(work);
    }
}
