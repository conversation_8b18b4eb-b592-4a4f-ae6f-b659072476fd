package io.aicloudware.portal.api_up.controller;


import io.aicloudware.portal.api_up.entity.UpVmUsage;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/vm_usage")
@Api(value = "/vm_usage", description = "虚机资源使用", position = 510)
public class UpVmUsageController extends BaseEntityController<UpVmUsage, UpVmUsageBean, UpVmUsageResultBean> {

//    @Autowired
//    private IUpVmUsageService vmUsageService;
//
//    @RequestMapping(value = "/statics_date", method = RequestMethod.POST)
//    @ApiOperation(notes = "/statics_date", httpMethod = "POST", value = "按日期统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmUsageResultBean.class)})
//    @ResponseBody
//    public ResponseBean statics_history(@ApiParam(value = "查询条件") @RequestBody UpVmUsageSearchBean searchBean) {
//        return ResponseBean.success(vmUsageService.getStaticsDate(searchBean));
//    }
//
//    @RequestMapping(value = "/statics_group", method = RequestMethod.POST)
//    @ApiOperation(notes = "/statics_group", httpMethod = "POST", value = "按部门统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmUsageResultBean.class)})
//    @ResponseBody
//    public ResponseBean statics_group(@ApiParam(value = "查询条件") @RequestBody UpVmUsageSearchBean searchBean) {
//        return ResponseBean.success(vmUsageService.getStaticsGroup(searchBean));
//    }
//
//    @RequestMapping(value = "/statics_app_system", method = RequestMethod.POST)
//    @ApiOperation(notes = "/statics_app_system", httpMethod = "POST", value = "按部门统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmUsageResultBean.class)})
//    @ResponseBody
//    public ResponseBean statics_app_system(@ApiParam(value = "查询条件") @RequestBody UpVmUsageSearchBean searchBean) {
//        return ResponseBean.success(vmUsageService.getStaticsAppSystem(searchBean));
//    }
//
//    @RequestMapping(value = "/statics_vm", method = RequestMethod.POST)
//    @ApiOperation(notes = "/statics_vm", httpMethod = "POST", value = "按部门统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmUsageResultBean.class)})
//    @ResponseBody
//    public ResponseBean statics_vm(@ApiParam(value = "查询条件") @RequestBody UpVmUsageSearchBean searchBean) {
//        return ResponseBean.success(vmUsageService.getStaticsVm(searchBean));
//    }
}
