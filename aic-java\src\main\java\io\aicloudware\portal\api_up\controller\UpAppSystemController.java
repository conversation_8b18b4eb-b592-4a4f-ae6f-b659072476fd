package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_up.entity.UpAppSystemUserRelation;
import io.aicloudware.portal.api_up.service.IUpAppSystemService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/app_system")
@Api(value = "/app_system", description = "应用系统", position = 508)
public class UpAppSystemController extends BaseUpController<UpAppSystem, UpAppSystemBean, UpAppSystemResultBean> {

    @Autowired
    private IUpAppSystemService upAppSystemService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpAppSystemUserRelationSearchBean searchBean) {
        UpAppSystemUserRelation entity = BeanCopyUtil.copy(searchBean.getBean(), UpAppSystemUserRelation.class);
        entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
        //企业项目和region没关系
//        entity.setRegion(ThreadCache.getRegion());
        entity.setUser(ThreadCache.getUser());
        if (StringUtils.isNotEmpty(entity.getName())) {
            UpAppSystemUserRelationBean fuzzyBean = new UpAppSystemUserRelationBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
        try {
            UpAppSystemUserRelationBean[] entityList = this.commonService.query(searchBean, entity, UpAppSystemUserRelationBean.class);
            UpAppSystemUserRelationResultBean result = new UpAppSystemUserRelationResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/listByOwnerId/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/listByOwnerId/{id}", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean listByOwnerId(@PathVariable Integer id) {
        return ResponseBean.success(upAppSystemService.listByOwnerId(id));
    }

    @RequestMapping(value = "/listRelationByAppSystemId/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/listRelationByAppSystemId/{id}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean listRelationByAppSystemId(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        return ResponseBean.success(upAppSystemService.listRelationByAppSystemId(id));
    }

    @RequestMapping(value = "/disable/{id}", method = RequestMethod.PUT)
    @ApiOperation(notes = "/disable/{id}", httpMethod = "PUT", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean disable(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        upAppSystemService.disable(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/enable/{id}", method = RequestMethod.PUT)
    @ApiOperation(notes = "/enable/{id}", httpMethod = "PUT", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean enable(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        upAppSystemService.enable(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(notes = "/save", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean save(@ApiParam(value = "查询条件") @RequestBody UpAppSystemBean bean) {
        upAppSystemService.save(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/save_users", method = RequestMethod.POST)
    @ApiOperation(notes = "/save_users", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean saveUsers(@ApiParam(value = "查询条件") @RequestBody UpAppSystemUserRelationListBean bean) {
        upAppSystemService.saveUsers(bean);
        return ResponseBean.success(true);
    }

}
