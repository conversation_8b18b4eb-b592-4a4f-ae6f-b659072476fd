package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_up.entity.UpAppSystemUserRelation;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpAppSystemStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEpsOperation;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpAppSystemService extends BaseService implements IUpAppSystemService {

    @Autowired
    private ISpRegionService spRegionService;

    @Override
    public void save(UpAppSystemBean bean) {
        if(bean.getId() == null){
            AssertUtil.check(dao.list(UpAppSystem.class,
                    MapUtil.of("name",bean.getName(),"owner.id",ThreadCache.getUserId(),
//                            "region",ThreadCache.getRegion(),
                            "org.id",ThreadCache.getOrgId())).size() == 0,
                    "项目名称已存在");
            SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
            UpAppSystem entity = JointEcloudEpsOperation.getInstance(org.getUsername(), org.getPassword()).
                    createEnterpriseProject(bean.getName(), bean.getComment());

//            entity.setRegion(ThreadCache.getRegion());
            entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
            entity.setOwner(ThreadCache.getUser());
            dao.insert(entity);

            UpAppSystemUserRelation relation = BeanCopyUtil.copy(bean, UpAppSystemUserRelation.class);
            relation.setAppSystem(entity);
            relation.setOrg(new SpOrg(ThreadCache.getOrgId()));
//            relation.setRegion(ThreadCache.getRegion());
            relation.setName(entity.getName());
            relation.setUser(ThreadCache.getUser());
            dao.insert(relation);

        }else{
            UpAppSystem entity = dao.load(UpAppSystem.class, bean.getId());
            entity.setName(StringUtils.isNotEmpty(bean.getName())?bean.getName():entity.getName());
            entity.setComment(StringUtils.isNotEmpty(bean.getComment())?bean.getComment():entity.getComment());
            entity.setOwner(bean.getOwnerId()!=null?new UpUser(bean.getOwnerId()):entity.getOwner());
            dao.update(entity,"name","comment","owner");
        }
    }

    @Override
    public void saveUsers(UpAppSystemUserRelationListBean listBean) {
        // beans
        List<UpAppSystemUserRelationBean> params = Arrays.asList(listBean.getDataList());
        List<Integer> beanOwnerIds = params.stream().map(UpAppSystemUserRelationBean::getUserId).collect(Collectors.toList());

        // db relations
        UpAppSystem dbAppSystem = dao.load(UpAppSystem.class, listBean.getAppSystemId());
        List<UpAppSystemUserRelation> dbRelationList = dbAppSystem.getRelations();

        // remove db datas
        List<Integer> deleteRelationIdList = dbRelationList.stream().filter(relation -> !beanOwnerIds.contains(relation.getUser().getId())).map(UpAppSystemUserRelation::getId).collect(Collectors.toList());
        dao.delete(UpAppSystemUserRelation.class, deleteRelationIdList);

        // add
        List<Integer> dbOwnerIds = dbRelationList.stream().map(UpAppSystemUserRelation::getUser).map(UpUser::getId).collect(Collectors.toList());
        List<UpAppSystemUserRelation> addRelationBeans = params.stream()
                .filter(bean -> !dbOwnerIds.contains(bean.getUserId()))
                .map(bean -> {
                    UpAppSystemUserRelation entity = BeanCopyUtil.copy(bean, UpAppSystemUserRelation.class);
                    entity.setAppSystem(dbAppSystem);
                    entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
//                    entity.setRegion(ThreadCache.getRegion());
                    entity.setName(dbAppSystem.getName());
                    return entity;
                }).collect(Collectors.toList());
        dao.insert(addRelationBeans);
    }

    @Override
    public UpAppSystemUserRelationBean[] listRelationByAppSystemId(Integer id) {
        UpAppSystem entity = dao.load(UpAppSystem.class, id);
        AssertUtil.check(entity.getRelations().stream().filter(
                relation -> relation.getUser().getId().equals(ThreadCache.getUserId())).count() > 0,
                "无权限访问");
        return BeanCopyUtil.copy2BeanList(entity.getRelations(), UpAppSystemUserRelationBean.class);
    }

    @Override
    public void disable(Integer id) {
        UpAppSystem entity = dao.load(UpAppSystem.class, id);
        AssertUtil.check(entity.getOwner().getId().equals(ThreadCache.getUserId()),
                "无权限访问");
        JointEcloudEpsOperation.getInstance(entity.getOrg().getUsername(), entity.getOrg().getPassword())
                .disableEnterpriseProject(entity.getUid());
        entity.setAppSystemStatus(UpAppSystemStatus.suspend);
        dao.update(entity);
    }

    @Override
    public void enable(Integer id) {
        UpAppSystem entity = dao.load(UpAppSystem.class, id);
        AssertUtil.check(entity.getOwner().getId().equals(ThreadCache.getUserId()),
                "无权限访问");
        JointEcloudEpsOperation.getInstance(entity.getOrg().getUsername(), entity.getOrg().getPassword())
                        .enableEnterpriseProject(entity.getUid());
        entity.setAppSystemStatus(UpAppSystemStatus.active);
        dao.update(entity);
    }

    @Override
    public UpAppSystemBean[] listByOwnerId(Integer id) {
        List<UpAppSystem> appSystemList = dao.list(UpAppSystem.class, MapUtil.of("owner.id", id, "org.id", ThreadCache.getOrgId()));
        return BeanCopyUtil.copy2BeanList(appSystemList, UpAppSystemBean.class);
    }

    @Override
    public void sync() {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        // TODO ecloud configure set --cli-mode=AKSK --cli-region=CIDC-RP-11 --cli-secret-key=xxxx --cli-access-key=xxxx
        List<UpAppSystem> serverAppSystems = JointEcloudEpsOperation.getInstance(org.getUsername(), org.getPassword()).listEnterpriseProjects();
        List<UpAppSystem> dataAppSystems = dao.list(UpAppSystem.class, MapUtil.of("org.id", ThreadCache.getOrgId(), "status", RecordStatus.active));
        Comparator<UpAppSystem> comparator = Comparator.comparing(UpAppSystem::getUid);
        List<UpAppSystem> dataToAdd = ListUtil.getDifference(serverAppSystems, dataAppSystems, comparator);
        List<UpAppSystem> dataToDelete = ListUtil.getDifference(dataAppSystems, serverAppSystems, comparator);
        List<UpAppSystem> dataToUpdate = ListUtil.getIntersection(dataAppSystems, serverAppSystems, comparator);
        List<UpUser> userList = dao.list(UpUser.class, MapUtil.of("name", "admin", "status", RecordStatus.active));
        AssertUtil.check(userList.size() == 1, "admin用户不存在");
        dataToAdd.forEach(appSystem -> {
            SpRegionEntity region = new SpRegionEntity(spRegionService.CIDCRP11().getId());
            appSystem.setOwner(userList.get(0));
            appSystem.setOrg(org);
            appSystem.setRegion(region);
            dao.insert(appSystem);

            UpAppSystemUserRelation relation = new UpAppSystemUserRelation();
            relation.setAppSystem(appSystem);
            relation.setOrg(new SpOrg(ThreadCache.getOrgId()));
            relation.setRegion(region);
            relation.setName(appSystem.getName());
            relation.setUser(userList.get(0));
            dao.insert(relation);
        });
        dataToDelete.forEach(appSystem -> {
            dao.delete(UpAppSystem.class, appSystem.getId());
            dao.list(UpAppSystemUserRelation.class, MapUtil.of("appSystem.id", appSystem.getId())).forEach(relation -> {
                dao.delete(UpAppSystemUserRelation.class, relation.getId());
            });
        });
        dataToUpdate.forEach(appSystem -> {
            appSystem.setOrg(org);
            appSystem.setRegion(new SpRegionEntity(spRegionService.CIDCRP11().getId()));
            dao.update(appSystem);
        });
    }
}
