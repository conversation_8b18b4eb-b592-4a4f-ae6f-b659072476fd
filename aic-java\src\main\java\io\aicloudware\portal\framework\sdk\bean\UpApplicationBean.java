package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationPage;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "申请单")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpApplicationBean.class})
public class UpApplicationBean extends RecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "页面")
    private UpApplicationPage page;

    @ApiModelProperty(value = "预留ID")
    private Integer reservationId;

    @ApiModelProperty(value = "预留名称")
    private String reservationName;

    @ApiModelProperty(value = "预留显示名称")
    private String reservationDisplayName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    private String groupName;

    @ApiModelProperty(value = "应用系统ID")
    private Integer appSystemId;

    @ApiModelProperty(value = "应用系统名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "类型")
    private UpApplicationType type;

    @ApiModelProperty(value = "申请时间")
    private Date createTm;

    @ApiModelProperty(value = "请求操作扩展信息")
    private String operateInfo;

    @ApiModelProperty(value = "申请原因")
    private String reason;

    @ApiModelProperty(value = "申请说明")
    private String comment;

    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "状态")
    private UpApplicationStatus applicationStatus;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public UpApplicationPage getPage() {
        return page;
    }

    public void setPage(UpApplicationPage page) {
        this.page = page;
    }

    public Integer getReservationId() {
        return reservationId;
    }

    public void setReservationId(Integer reservationId) {
        this.reservationId = reservationId;
    }

    public String getReservationName() {
        return reservationName;
    }

    public void setReservationName(String reservationName) {
        this.reservationName = reservationName;
    }

    public String getReservationDisplayName() {
        return reservationDisplayName;
    }

    public void setReservationDisplayName(String reservationDisplayName) {
        this.reservationDisplayName = reservationDisplayName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public UpApplicationType getType() {
        return type;
    }

    public void setType(UpApplicationType type) {
        this.type = type;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

    public String getOperateInfo() {
        return operateInfo;
    }

    public void setOperateInfo(String operateInfo) {
        this.operateInfo = operateInfo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public UpApplicationStatus getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(UpApplicationStatus applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

}
