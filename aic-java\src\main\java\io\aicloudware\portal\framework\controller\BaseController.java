package io.aicloudware.portal.framework.controller;


import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.Logger;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public abstract class BaseController {

    protected final Logger logger = Logger.getLogger(getClass());

    @Autowired
    protected ICommonService commonService;

}
