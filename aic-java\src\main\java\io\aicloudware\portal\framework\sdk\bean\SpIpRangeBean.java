package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "IP地址范围")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpIpRangeBean extends SpRecordBean {


    @ApiModelProperty(value = "IP地址范围描述")
    private String description;

    @ApiModelProperty(value = "起始IP")
    private String ipBegin;

    @ApiModelProperty(value = "结束IP")
    private String ipEnd;

    @ApiModelProperty(value = "网络配置文件ID")
    private Integer networkProfileId;

    @ApiModelProperty(value = "网络配置文件名称")
    private String networkProfileName;

    @ApiModelProperty(value = "网络配置文件显示名称")
    private String networkProfileDisplayName;

    @ApiModelProperty(value = "IP地址使用状况列表")
    private SpIpUsageBean[] ipUsageList;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIpBegin() {
        return ipBegin;
    }

    public void setIpBegin(String ipBegin) {
        this.ipBegin = ipBegin;
    }

    public String getIpEnd() {
        return ipEnd;
    }

    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }

    public Integer getNetworkProfileId() {
        return networkProfileId;
    }

    public void setNetworkProfileId(Integer networkProfileId) {
        this.networkProfileId = networkProfileId;
    }

    public String getNetworkProfileName() {
        return networkProfileName;
    }

    public void setNetworkProfileName(String networkProfileName) {
        this.networkProfileName = networkProfileName;
    }

    public SpIpUsageBean[] getIpUsageList() {
        return ipUsageList;
    }

    public void setIpUsageList(SpIpUsageBean[] ipUsageList) {
        this.ipUsageList = ipUsageList;
    }

    public String getNetworkProfileDisplayName() {
        return networkProfileDisplayName;
    }

    public void setNetworkProfileDisplayName(String networkProfileDisplayName) {
        this.networkProfileDisplayName = networkProfileDisplayName;
    }

}
