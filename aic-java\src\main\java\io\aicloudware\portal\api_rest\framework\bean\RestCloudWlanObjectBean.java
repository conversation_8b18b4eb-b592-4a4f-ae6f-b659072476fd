package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VPC信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestCloudWlanObjectBean.class })
public class RestCloudWlanObjectBean extends BaseRestBean {

	@ApiModelProperty(value = "用户ID")
	private String doorOrderItemId;
	
	@ApiModelProperty(value = "用户ID")
	private RestVpcBean[] vpcs;

	public String getDoorOrderItemId() {
		return doorOrderItemId;
	}

	public void setDoorOrderItemId(String doorOrderItemId) {
		this.doorOrderItemId = doorOrderItemId;
	}

	public RestVpcBean[] getVpcs() {
		return vpcs;
	}

	public void setVpcs(RestVpcBean[] vpcs) {
		this.vpcs = vpcs;
	}
}
