package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpRole;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpRoleBean;
import io.aicloudware.portal.framework.sdk.bean.UpRoleResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/role")
@Api(value = "/role", description = "角色", position = 530)
public class UpRoleController extends BaseUpController<UpRole, UpRoleBean, UpRoleResultBean> {

//    @Autowired
//    private IUpRoleService roleService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRoleResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryRole(@ApiParam(value = "查询条件") @RequestBody UpRoleSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRoleBean.class)})
//    @ResponseBody
//    public ResponseBean getRole(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRoleBean.class)})
//    @ResponseBody
//    public ResponseBean addRole(@ApiParam(value = "实例对象") @Valid @RequestBody UpRoleBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRoleListBean.class)})
//    @ResponseBody
//    public ResponseBean addRole(@ApiParam(value = "实例对象") @Valid @RequestBody UpRoleListBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRoleBean.class)})
//    @ResponseBody
//    public ResponseBean updateRole(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                   @ApiParam(value = "实例对象") @Valid @RequestBody UpRoleBean bean,
//                                   BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRoleListBean.class)})
//    @ResponseBody
//    public ResponseBean updateRole(@ApiParam(value = "实例对象") @Valid @RequestBody UpRoleListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRole(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRole(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
