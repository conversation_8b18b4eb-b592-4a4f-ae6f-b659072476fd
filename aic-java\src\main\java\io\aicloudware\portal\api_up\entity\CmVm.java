package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.contants.SpEcStatus;
import io.aicloudware.portal.framework.sdk.contants.SpRecycleStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmProvisionType;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplateMachine;
import io.aicloudware.portal.platform_vcd.entity.SpOVDC;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import java.util.Date;
import java.util.List;

@MappedSuperclass
public abstract class CmVm<N extends CmVmNetwork, D extends CmVmDisk> extends BaseSpEntity<SpVmBean> implements IDataScopeEntity<SpVmBean> {
    public CmVm() {
    }

    public CmVm(Integer id) {
        super(id);
    }

    @EntityProperty(isNullCopy = false)
    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @Column(name = "resource_region")
    private String resourceRegion;

    @JoinColumn(name = "reservation_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOVDC reservation;

    @JoinColumn(name = "blueprint_machine_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVappTemplateMachine blueprintMachine;

    @Column(name = "vm_comment")
    private String vmComment;

    @Column(name = "cpu_num", nullable = false)
    private Integer cpuNum;

    @Column(name = "memory_gb", nullable = false)
    private Integer memoryGB;

    @Column(name = "disk_gb", nullable = false)
    private Integer diskGB;

    //for vRA
    @Column(name = "catalog_resource_id")
    private String catalogResourceId;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "host_name")
    private String hostName;

    @Column(name = "os_user")
    private String osUser;

    @Column(name = "flavor_id")
    private String flavorId;

    @Column(name = "os_password")
    private String osPassword;

    @Column(name = "folder_path")
    private String folderPath;

    @Column(name = "comments")
    private String comments;

    @Column(name = "domain")
    private String domain;

    @Column(name = "power_status")
    @Enumerated(EnumType.STRING)
    private SpVmPowerStatus powerStatus;

    @Column(name = "ec_status")
    @Enumerated(EnumType.STRING)
    private SpEcStatus ecStatus;

    @Column(name = "recycle_status")
    @Enumerated(EnumType.STRING)
    private SpRecycleStatus recycleStatus;

    @Column(name = "provision_type")
    @Enumerated(EnumType.STRING)
    private SpVmProvisionType provisionType;

    @JoinColumn(name = "script_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpScript script;

    @Column(name = "has_snapshot")
    private Boolean hasSnapshot;

    @Column(name = "snapshot_name")
    private String snapshotName;

    @Column(name = "snapshot_create_tm")
    private Date snapshotCreateTm;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "vm")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<N> networkList;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "vm")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<D> diskList;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpVappTemplateMachine getBlueprintMachine() {
        return blueprintMachine;
    }

    public void setBlueprintMachine(SpVappTemplateMachine blueprintMachine) {
        this.blueprintMachine = blueprintMachine;
    }

    public String getVmComment() {
        return vmComment;
    }

    public void setVmComment(String vmComment) {
        this.vmComment = vmComment;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getCatalogResourceId() {
        return catalogResourceId;
    }

    public void setCatalogResourceId(String catalogResourceId) {
        this.catalogResourceId = catalogResourceId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getOsUser() {
        return osUser;
    }

    public void setOsUser(String osUser) {
        this.osUser = osUser;
    }

    public String getOsPassword() {
        return osPassword;
    }

    public void setOsPassword(String osPassword) {
        this.osPassword = osPassword;
    }

    public String getFolderPath() {
        return folderPath;
    }

    public void setFolderPath(String folderPath) {
        this.folderPath = folderPath;
    }

    public Boolean getHasSnapshot() {
        return hasSnapshot;
    }

    public void setHasSnapshot(Boolean hasSnapshot) {
        this.hasSnapshot = hasSnapshot;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public SpVmPowerStatus getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(SpVmPowerStatus powerStatus) {
        this.powerStatus = powerStatus;
    }

    public SpVmProvisionType getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(SpVmProvisionType provisionType) {
        this.provisionType = provisionType;
    }

    public UpScript getScript() {
        return script;
    }

    public void setScript(UpScript script) {
        this.script = script;
    }

    public String getSnapshotName() {
        return snapshotName;
    }

    public void setSnapshotName(String snapshotName) {
        this.snapshotName = snapshotName;
    }

    public Date getSnapshotCreateTm() {
        return snapshotCreateTm;
    }

    public void setSnapshotCreateTm(Date snapshotCreateTm) {
        this.snapshotCreateTm = snapshotCreateTm;
    }

    public List<N> getNetworkList() {
        return networkList;
    }

    public void setNetworkList(List<N> networkList) {
        this.networkList = networkList;
    }

    public List<D> getDiskList() {
        return diskList;
    }

    public void setDiskList(List<D> diskList) {
        this.diskList = diskList;
    }

    public SpOVDC getReservation() {
        return reservation;
    }

    public void setReservation(SpOVDC reservation) {
        this.reservation = reservation;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }

    public SpEcStatus getEcStatus() {
        return ecStatus;
    }

    public void setEcStatus(SpEcStatus ecStatus) {
        this.ecStatus = ecStatus;
    }

    public SpRecycleStatus getRecycleStatus() {
        return recycleStatus;
    }

    public void setRecycleStatus(SpRecycleStatus recycleStatus) {
        this.recycleStatus = recycleStatus;
    }

    public String getFlavorId() {
        return flavorId;
    }

    public void setFlavorId(String flavorId) {
        this.flavorId = flavorId;
    }
}
