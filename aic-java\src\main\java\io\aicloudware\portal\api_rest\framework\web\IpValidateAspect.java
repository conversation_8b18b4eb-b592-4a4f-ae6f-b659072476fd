package io.aicloudware.portal.api_rest.framework.web;

import io.aicloudware.portal.api_rest.framework.annotation.IpValidate;
import io.aicloudware.portal.api_rest.framework.exception.IllegalRequestException;
import io.aicloudware.portal.framework.utility.IpUtil;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Aspect
@Component
public class IpValidateAspect {
	
	@Value("#{'${ip_white_list}'.split(',')}")
	private List<String> ipWhiteList;
    
//	@Pointcut("@annotation(io.aicloudware.portal.api_rest.framework.annotation.IpValidate)")
//	public void pointcut() {}
//
	@Before("@annotation(ipValidate)")
	public void checkIpAddress(IpValidate ipValidate) {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		if(!IpUtil.checkIpAddress(request, ipWhiteList)) {
			throw new IllegalRequestException();
		}
	}
	
}
