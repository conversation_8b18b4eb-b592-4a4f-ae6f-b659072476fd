package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机备份列表查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVmBackupSearchBean extends SearchBean<SpVmBackupBean> {

    @ApiModelProperty(value = "虚机ID列表", hidden = true)
    private Integer[] vmIdList;

    @ApiModelProperty(value = "申请单ID列表")
    private Integer[] applicationIdList;

    public Integer[] getVmIdList() {
        return vmIdList;
    }

    public void setVmIdList(Integer[] vmIdList) {
        this.vmIdList = vmIdList;
    }

    public Integer[] getApplicationIdList() {
        return applicationIdList;
    }

    public void setApplicationIdList(Integer[] applicationIdList) {
        this.applicationIdList = applicationIdList;
    }
}
