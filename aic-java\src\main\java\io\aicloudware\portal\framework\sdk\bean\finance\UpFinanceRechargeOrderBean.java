package io.aicloudware.portal.framework.sdk.bean.finance;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PayChannel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "充值订单")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpFinanceRechargeOrderBean extends RecordBean {

	@ApiModelProperty(value = "订单号")
	private String orderNum;
	
	@ApiModelProperty(value = "金额")
	private BigDecimal amount;
	
	@ApiModelProperty(value = "渠道")
	private PayChannel payChannel;

	@ApiModelProperty(value = "支付二维码")
	private String url;
	
	@ApiModelProperty(name = "是否成功付款")
	private Boolean isSuccess;

	public String getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(String orderNum) {
		this.orderNum = orderNum;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public PayChannel getPayChannel() {
		return payChannel;
	}

	public void setPayChannel(PayChannel payChannel) {
		this.payChannel = payChannel;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Boolean getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(Boolean isSuccess) {
		this.isSuccess = isSuccess;
	}
	
}
