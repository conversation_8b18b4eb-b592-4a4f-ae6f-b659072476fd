package io.aicloudware.portal.api_rest.service;

import io.aicloudware.portal.api_rest.framework.bean.RestMessageBean;
import io.aicloudware.portal.api_rest.framework.entity.*;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageStatus;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageType;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.ICloudDao;
import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.apache.commons.net.util.SubnetUtils.SubnetInfo;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class RestMessageService extends BaseService implements IRestMessageService {

	private final static Logger logger = Logger.getLogger(RestMessageService.class);

	@Value("${cloud_net_url}")
	private String cloudNetUri;

	@Value("${crm_url}")
	private String crmUri;
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Autowired
    protected ICloudDao cloudDao;
	
	@Override
	public void sendMessage() {
//		List<SpBareMetal> bareMetalList = dao.list(SpBareMetal.class, "isMessage", false);
		List<SpBareMetal> bareMetalList = dao.list(SpBareMetal.class, "isMessage", false);
		for(SpBareMetal entity : bareMetalList){
			createBareMetalMessage(entity);
		}

		HttpClient client = getRestClient();
		List<RestMessage> messages = this.queryDao.queryHql("from RestMessage where status = 'active' and messageStatus = :messageStatus and createTm > :createTm", 
				MapUtil.of("messageStatus", RestMessageStatus.unsend, "createTm", DateUtils.addHours(new Date(), -14)));
		for(RestMessage entity : messages) {
			handleMessageCallBack(client, entity);
		}
		
		List<RestMonitor> monitors = this.queryDao.queryHql("from RestMonitor where status = 'active' and messageStatus = :messageStatus and createTm > :createTm", 
				MapUtil.of("messageStatus", RestMessageStatus.unsend, "createTm", DateUtils.addHours(new Date(), -14)));
		for(RestMonitor entity : monitors) {
			handleMonitorCallBack(client, entity);
		}
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void handleMessageCallBack(HttpClient client, RestMessage entity) {
		String callbackMessage = "";
		if(entity.getChannel() == UpProductSystemEnums.QuotaDetailChannel.crm){
			callbackMessage = "CRM MESSAGE CALLBACK: " + post(client, entity, crmUri);
		}else if(entity.getChannel() == UpProductSystemEnums.QuotaDetailChannel.cloud_net){
			callbackMessage = "CLOUDNET MESSAGE CALLBACK: " + post(client, entity, cloudNetUri);
		}else {
			callbackMessage = "CRM MESSAGE CALLBACK: " + post(client, entity, crmUri);
			callbackMessage += "\n";
			callbackMessage += "CLOUDNET MESSAGE CALLBACK: " + post(client, entity, cloudNetUri);
		}
		entity.setCallbackContent(callbackMessage);
		entity.setMessageStatus(RestMessageStatus.send);
		this.dao.update(entity, "messageStatus", "callbackContent");
	}

	private String post(HttpClient client, RestMessage entity, String uri){
		String postResource = null;
		try {
			postResource = postResource(client, uri + entity.getType().getUri(), new JSONObject(BeanCopyUtil.copy(entity, RestMessageBean.class)).toString());
//				JSONObject result = new JSONObject(postResource);
//				if(result.getString("resultCode").equals("0")) {
//				entity.setMessageStatus(RestMessageStatus.send);
//				}
			logger.info("SEND MESSAGE: messageId:" + entity.getId() + ", CALLBACK: " + postResource);
//			this.dao.update(entity, "messageStatus", "callbackContent");
		}catch(Exception e) {
			logger.error("SEND MESSAGE: messageId:" + entity.getId() + ", CALLBACK: " + postResource, e);
//			entity.setCallbackContent("callback:"+ postResource + ", error Message:" + e.getMessage());
//			this.dao.update(entity, "callbackContent");
			postResource = postResource + ", error Message:" + e.getMessage();
//			entity.setMessageStatus(RestMessageStatus.send);
//			this.dao.update(entity, "messageStatus", "callbackContent");
		}
		return postResource;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void handleMonitorCallBack(HttpClient client, RestMonitor entity) {
		String callBackString = null;
		try {
			JSONObject obj = new JSONObject(BeanCopyUtil.copy(entity, RestMessageBean.class));
			callBackString = postResource(client, cloudNetUri + entity.getType().getUri(), obj.toString());
			logger.info("postBody:" + obj.toString());
			JSONObject result = new JSONObject(callBackString);
			entity.setCallbackContent(result.toString());
//			if(result.getString("resultCode").equals("0")) {
				entity.setMessageStatus(RestMessageStatus.send);
//			}
			logger.info("SEND MONITOR: monitorId:" + entity.getId() + ", CALLBACK: " + callBackString);
			this.dao.update(entity, "messageStatus", "callbackContent");
		}catch(Exception e) {
			logger.error("SEND MONITOR: monitorId:" + entity.getId() + ", CALLBACK: " + callBackString, e);
			entity.setCallbackContent("callback:"+ callBackString + ", error Message:" + e.getMessage());
//			this.dao.update(entity, "callbackContent");
			
			entity.setMessageStatus(RestMessageStatus.send);
			this.dao.update(entity, "messageStatus", "callbackContent");
		}
	}
	
	@Override
	public void createVPCMessage(SpVPC vpc, List<SpOVDCNetwork> ovdcNetworkList) {
		SpVPC entity = dao.load(SpVPC.class, vpc.getId());
		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		RestMessage message = new RestMessage();
		message.setMessageStatus(RestMessageStatus.unsend);
		message.setType(RestMessageType.message);
		message.setName("VPC-"+entity.getName());
		message.setChannel(UpProductSystemEnums.QuotaDetailChannel.all);
		message.setCustomNo(entity.getSpOrg().getCustomNo());
		message.setMessageType("1"); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
		message.setResultCode(1); // 1=成功 -1 =失败
		
		RestResourceMessage resourceMessage = new RestResourceMessage();
		resourceMessage.setName("VPC-"+entity.getName());
		resourceMessage.setUserId(user.getSsoUserId());
		
		resourceMessage.setResourcePoolId(entity.getRegion().getCode() + "01");
		resourceMessage.setCityId(entity.getRegion().getCode());
		if(vpc.getOperateType() == UpOperateType.add) {
			resourceMessage.setOperationType("1"); //1=新增，2=删除 3=规格变更
		}else if(vpc.getOperateType() == UpOperateType.update || vpc.getOperateType() == UpOperateType.delete) {
			resourceMessage.setOperationType("3"); //1=新增，2=删除 3=规格变更
		}
		resourceMessage.setInstId(entity.getSpUuid());
		resourceMessage.setInstName(entity.getName());
		
		RestVpcInfo vpcInfo = new RestVpcInfo();
		vpcInfo.setVpcId(entity.getId().toString());
		vpcInfo.setName("VPC-"+entity.getName());
		List<RestSubnet> subnets = new ArrayList<>();
		for(SpOVDCNetwork ovdcNetwork : ovdcNetworkList) {
			SpIpScope ipScope = ovdcNetwork.getIpScopeList().get(0);
			SubnetInfo info = new SubnetUtils(ipScope.getGateway(), ipScope.getNetMask()).getInfo();
			RestSubnet subnet = new RestSubnet();
			subnet.setName(ovdcNetwork.getName());
			if(vpc.getOperateType() == UpOperateType.delete) {
				subnet.setOperationType("2");
			}else {
				subnet.setOperationType("1");
			}
			String cidr = info.getCidrSignature();
			String netcidr = cidr.replaceAll(ipScope.getGateway(), info.getNetworkAddress());
			subnet.setSubnetSegment(netcidr);
			subnet.setVpcInfo(vpcInfo);
			subnets.add(subnet);
		}
		
		resourceMessage.setVpcInfo(vpcInfo);
		message.setResourceMessage(resourceMessage);
		dao.insert(vpcInfo);
		dao.insert(subnets);
		dao.insert(resourceMessage);
		dao.insert(message);
	}
	
	@Override
	public void createOrgMessage(String customNo, String messageType, Boolean isSuccess, String remark) {
		RestMessage message = new RestMessage();
		message.setMessageStatus(RestMessageStatus.unsend);
		message.setType(RestMessageType.message);
		message.setName(customNo);
		message.setCustomNo(customNo);
		message.setChannel(UpProductSystemEnums.QuotaDetailChannel.all);
		message.setMessageType(messageType); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
		message.setResultCode(isSuccess ? 1 : -1); // 1=成功 -1 =失败
		message.setFailReason(remark);
		dao.insert(message);
	}
	
	@Override
	public void deleteInstanceMessage(Class clz, Integer id) {
		logger.info("deleteInstanceMessage clz:" + clz + " id:" + id);
		BaseSpEntity entity = (BaseSpEntity) this.dao.load(clz, id);
		RestResourceMessage resourceMessage = new RestResourceMessage();
		SpRegionEntity region = clz == SpVPC.class ? entity.getRegion() : null;
		UpOrderQuotaDetail quotaDetail = null;
		if(clz != SpVPC.class) {
			UpOrder order = null;
			if(clz == SpLoadBalancerVirtualServer.class) {
				try {
					String[] ods = entity.getName().split("-OD");
					String od = ods[ods.length-1];
					Integer orderLoadBalanceId = Integer.valueOf(od.split("-")[0]);
					order = this.dao.load(UpOrderLoadBalance.class, orderLoadBalanceId).getOrder();
				}catch(Exception e) {
					logger.error("deleteInstanceMessage clz:" + clz + " id:" + id, e);
				}
			}else {
				order = entity.getOrder();
			}
			if(order == null || order.getQuotaDetail() == null) {
				return;
			}
			quotaDetail = order.getQuotaDetail();
			UpUser user = order.getOwner();
			if(StringUtils.isEmpty(user.getSsoUserId())) {
				return;
			}
			resourceMessage.setUserId(user.getSsoUserId());
			resourceMessage.setMainAgreementId(order.getQuota().getCode());
			resourceMessage.setDoorOrderItemId(quotaDetail.getSubCode());
			quotaService.openQuotaDetail(quotaDetail.getId());
			region = getSourceRegion(quotaDetail);
		}
		resourceMessage.setName(entity.getName());
		resourceMessage.setInstId(entity.getSpUuid());
		resourceMessage.setInstName(entity.getName());
		resourceMessage.setResourcePoolId(region.getCode() + "01");
		resourceMessage.setCityId(region.getCode());
		resourceMessage.setOperationType("2");
		this.dao.insert(resourceMessage);
		
		RestMessage message = new RestMessage();
		message.setMessageStatus(RestMessageStatus.unsend);
		message.setType(RestMessageType.message);
		message.setName(entity.getName());
		message.setChannel(quotaDetail == null ? UpProductSystemEnums.QuotaDetailChannel.all : quotaDetail.getChannel());
		message.setCustomNo(entity.getSpOrg().getCustomNo());
		message.setMessageType("1"); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
		message.setResultCode(1); // 1=成功 -1 =失败
		message.setResourceMessage(resourceMessage);
		this.dao.insert(message);
	}
	
	@Override
	public void updateInstanceMessage(SpRegionEntity region, BaseSpEntity entity, Boolean isSuccess, String content, String ssoUserId, String code, String subCode, UpProductSystemEnums.QuotaDetailChannel channel) {
		RestMessage message = new RestMessage();
		message.setMessageStatus(RestMessageStatus.unsend);
		message.setType(RestMessageType.message);
		message.setName(entity.getName());
		message.setChannel(channel);
		message.setCustomNo(entity.getSpOrg().getCustomNo());
		message.setMessageType("1"); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
		if(isSuccess) {
			message.setResultCode(1); // 1=成功 -1 =失败
		}else {
			message.setResultCode(-1); // 1=成功 -1 =失败
			message.setFailReason(content);
		}
		RestResourceMessage resourceMessage = new RestResourceMessage();
		resourceMessage.setUserId(ssoUserId);
		resourceMessage.setMainAgreementId(code);
		resourceMessage.setDoorOrderItemId(subCode);
		resourceMessage.setInstId(entity.getSpUuid());
		resourceMessage.setResourcePoolId((region == null ? entity.getRegion().getCode() : region.getCode()) + "01");
		resourceMessage.setCityId(region == null ? entity.getRegion().getCode() : region.getCode());
		resourceMessage.setOperationType("3");
		resourceMessage.setName(entity.getName());
		message.setResourceMessage(resourceMessage);
		this.dao.insert(resourceMessage);
		this.dao.insert(message);
	}
	
	@Override
	public void createOrderMessage(UpOrder order) {
		if(order == null) {
			return;
		}
		order = dao.load(UpOrder.class, order.getId());
		logger.info("MESSAGE: Create order message -> orderId: "+ order.getId() + " orderType: " + order.getType());
		
		RestMessage message = new RestMessage();
		if(order.getQuotaDetail() != null){
			message.setChannel(order.getQuotaDetail().getChannel());
		}else{
			message.setChannel(UpProductSystemEnums.QuotaDetailChannel.all);
		}
		try {
			message.setMessageType("1"); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
			RestResourceMessage resourceMessage = buildResourceMessage(order);
			if(resourceMessage == null){
				return;
			}
			message.setResourceMessage(resourceMessage);
			dao.insert(resourceMessage);
		}catch(Exception e){
			logger.warn("MESSAGE: Create order message failed -> orderId: " + order.getId() + " orderType: " + order.getType() + ", info: " + e.getMessage());
			return;
		}
		
		SpOrg org = order.getSpOrg();
		message.setMessageStatus(RestMessageStatus.unsend);
		message.setType(RestMessageType.message);
		message.setName(order.getName());
		message.setCustomNo(org.getCustomNo());
		message.setResultCode(1); // 1=成功 -1 =失败
		dao.insert(message);
	}
	
	@Override
	public void createMontorMessage(String customNo, String spUuid, String title, String content) {
		RestMonitor monitor = new RestMonitor();
		monitor.setName(title);
		monitor.setMessageStatus(RestMessageStatus.unsend);
		monitor.setType(RestMessageType.montor);
		monitor.setCustomNo(customNo);
		monitor.setInstId(spUuid);
		monitor.setWarntitle(title);
		monitor.setWarnContent(content);
		this.dao.insert(monitor);
	}

	@Override
	public Boolean isWebsiteApprove(String customNo, String ip) {
		String resultCode = null;
		String recordResult = null;
		JSONObject result = null;
		try {
			JSONObject json = new JSONObject();
			json.put("customNo", customNo);
			json.put("ip", ip);
			String callBack = postResource(getRestClient(), cloudNetUri + RestMessageType.websiteApprove.getUri(), json.toString());
			logger.debug("WEBSITE APPROVE: customNo:" + customNo + ", ip:" + ip + ", callback:" + callBack);
			result = new JSONObject(callBack);
			resultCode = result.getString("resultCode");
		} catch (Exception e) {
			AssertUtil.check(false, "获取备案信息失败，请联系管理员");
			logger.error( "WEBSITE APPROVE failed. CustomNo:" + customNo + ", ip: " +ip + " | " + e.getMessage());
		}
		if("0".equals(resultCode)) {
			try {
				recordResult = result.getJSONObject("resultData").getString("recordResult");
			} catch (JSONException e) {
				AssertUtil.check(false, "获取备案信息失败，请联系管理员");
				logger.error( "WEBSITE APPROVE failed. CustomNo:" + customNo + ", ip: " +ip + " | " + e.getMessage());
			}
			if("2".equals(recordResult)) {
				return true;
			}else if("-1".equals(recordResult) || "1".equals(recordResult)) {
				AssertUtil.check(false, ip + " 正在备案，请稍后再试");
			}else if("0".equals(recordResult) || "-2".equals(recordResult)) {
				AssertUtil.check(false, ip + " 未备案或者备案失败");
			}
		}
		return false;
	}

	@SuppressWarnings("rawtypes")
	private RestResourceMessage buildResourceMessage(UpOrder order) {
		UpUser user = order.getOwner();
		if("sso".equals(user.getType())){
			user = dao.list(UpUser.class, MapUtil.of("org", order.getSpOrg(), "type", "sso")).get(0);
		}
		RestResourceMessage resourceMessage = new RestResourceMessage();
		resourceMessage.setName(order.getName());
		resourceMessage.setUserId(user.getSsoUserId());

		OrderType type = order.getType();
		if(type == null) {
			throw new RuntimeException("orderType is null");
		}
		
		UpOrderQuotaDetail quotaDetail = null;
		if (type == OrderType.new_cloud_server) {
			resourceMessage.setOperationType("1"); //1=新增，2=删除 3=规格变更
			resourceMessage = buildVmMessage(order, resourceMessage);
			quotaDetail = order.getQuotaDetail();
		}else if(type == OrderType.new_redis
					|| type == OrderType.new_rds_mysql || type == OrderType.new_rds_oracle
					|| type == OrderType.new_esk) {
			resourceMessage.setOperationType("1"); //1=新增，2=删除 3=规格变更
			SpVapp vapp = this.dao.list(SpVapp.class, "order", order).get(0);
			resourceMessage.setInstId(vapp.getSpUuid());
			resourceMessage.setInstName(vapp.getName());
			quotaDetail = order.getQuotaDetail();
		}else if(type.toString().toLowerCase().indexOf("update") >= 0) {
			resourceMessage.setOperationType("3"); //1=新增，2=删除 3=规格变更
			if(type == OrderType.cloud_server_update) {
				UpOrderCloudServer orderEntity = this.dao.list(UpOrderCloudServer.class, "order", order).get(0);
				SpVm vm = orderEntity.getVm();
				quotaDetail = order.getQuotaDetail();
				resourceMessage.setInstId(vm.getSpUuid());
				resourceMessage.setInstName(vm.getName());
			}
		}else if (type == OrderType.private_image) {
			quotaDetail = order.getQuotaDetail();
			UpOrderPrivateImage orderImage = this.dao.list(UpOrderPrivateImage.class, "order", order).get(0);
			resourceMessage.setInstId(orderImage.getSpOrg().getId() + "-" + orderImage.getName());
			resourceMessage.setInstName(orderImage.getSpOrg().getId() + "-" + orderImage.getName());
			resourceMessage.setOperationType("1");
		}else if(type.toString().toLowerCase().indexOf("delete") >= 0) {
			resourceMessage.setOperationType("2"); //1=新增，2=删除 3=规格变更
			if(type == OrderType.cloud_server_delete) {
				UpOrderCloudServer orderEntity = this.dao.list(UpOrderCloudServer.class, "order", order).get(0);
				SpVm vm = orderEntity.getVm();
				quotaDetail = vm.getOrder().getQuotaDetail();
				resourceMessage.setInstId(vm.getSpUuid());
				resourceMessage.setInstName(vm.getName());
			}else if(type == OrderType.cloud_disk_delete) {
				UpOrderCloudDisk orderEntity = this.dao.list(UpOrderCloudDisk.class, "order", order).get(0);
				SpVm vm = this.dao.load(SpVm.class, orderEntity.getCloudServerId());
				List<SpVmDisk> disks = queryDao.queryHql("from SpVmDisk where spUuid = :spUuid order by id desc", MapUtil.of("spUuid", vm.getSpUuid() + "~" + orderEntity.getDiskNumber()));
//				SpVmDisk disk = cloudDao.loadBySpUuid(SpVmDisk.class, vm.getSpUuid() + "~" + orderEntity.getDiskNumber());
				SpVmDisk disk = disks.get(0);
				quotaDetail = disk.getOrder().getQuotaDetail();
				resourceMessage.setInstId(disk.getSpUuid());
				resourceMessage.setInstName(disk.getDiskLabel());
			}else if(type == OrderType.private_image_delete) {
				UpOrderPrivateImage orderImage = this.dao.list(UpOrderPrivateImage.class, "order", order).get(0);
				resourceMessage.setInstId(orderImage.getName());
				resourceMessage.setInstName(orderImage.getName());
				
				List<UpOrderQuotaDetail> quotaDetails = dao.list(UpOrderQuotaDetail.class, MapUtil.of("spOrg", order.getSpOrg(), "type", ProductType.IMAGE, "catalog", QuotaCatalog.NEW, "quotaDetailStatus", QuotaDetailStatus.finish));
				if(quotaDetails.size() == 0 ) {
					throw new RuntimeException("Image QuotaDetail is not found, orderId:" + order.getId());
				}
		        quotaDetail = quotaDetails.get(0);
			}else if(type == OrderType.object_storage_bucket_delete) {
				UpOrderObjectStorageBucket orderBucket = this.dao.list(UpOrderObjectStorageBucket.class, "order", order).get(0);
				SpObjectStorageBucket bucket = orderBucket.getSpObjectStorageBucket();
				quotaDetail = bucket.getOrder().getQuotaDetail();
				resourceMessage.setInstId(bucket.getSpUuid());
				resourceMessage.setInstName(bucket.getName());
			}
		}else if (type == OrderType.new_load_balance) {
			quotaDetail = order.getQuotaDetail();
			resourceMessage = buildLBMessage(order, resourceMessage);
		}else {
			Class clz = null;
			if (type == OrderType.new_cloud_disk) {
				clz = SpVmDisk.class;
			} else if (type == OrderType.new_elasticIp) {
				clz = SpElasticIp.class;
			}else if (type == OrderType.new_redis) {
				clz = SpRedis.class;
			}else if (type == OrderType.new_rds_mysql) {
				clz = SpVm.class;
			}else if (type == OrderType.new_rds_oracle) {
				clz = SpVm.class;
			}else if (type == OrderType.new_esk) {
				clz = SpVm.class;
			}else if (type == OrderType.file_storage) {
				clz = SpFileStorage.class;
			}else if (type == OrderType.object_storage_bucket || type == OrderType.object_storage_bucket_change || type == OrderType.object_storage_bucket_delete) {
				clz = SpObjectStorageBucket.class;
			}else if (type == OrderType.veritas_backup_policy) {
				clz = SpVmDisk.class;
			}
			if(clz == null) {
				throw new RuntimeException(type + " is not supported");
			}

			resourceMessage = buildBySpEntityMessage(clz, order, resourceMessage);
			resourceMessage.setOperationType("1"); //1=新增，2=删除 3=规格变更
			quotaDetail = order.getQuotaDetail();
		}
		
		if(quotaDetail == null) {
			logger.info("OrderId: " + order.getId() + ", Quota not found");
			return null;
		}
		resourceMessage.setResourcePoolId(getSourceRegion(quotaDetail).getCode() + "01");
		resourceMessage.setCityId(getSourceRegion(quotaDetail).getCode());
		resourceMessage.setMainAgreementId(quotaDetail.getQuota().getCode());
		resourceMessage.setDoorOrderItemId(quotaDetail.getSubCode());
		
		if(resourceMessage.getOperationType().equals("1")) {
			quotaService.finishQuotaDetail(quotaDetail.getId());
		}else if(resourceMessage.getOperationType().equals("2")){
			quotaService.openQuotaDetail(quotaDetail.getId());
		}else if(resourceMessage.getOperationType().equals("3")) {
			quotaService.deleteQuotaDetail(quotaDetail.getId());
		}
		return resourceMessage;
	}
	
	private RestResourceMessage buildVmMessage(UpOrder order, RestResourceMessage resourceMessage) {
		SpVm vm = this.dao.list(SpVm.class, "order", order).get(0);
		resourceMessage.setInstId(vm.getSpUuid());
		resourceMessage.setInstName(vm.getName());
		resourceMessage.setIp(vm.getIpAddress());
		UpOrderCloudServer orderServer = this.dao.list(UpOrderCloudServer.class, "order", order).get(0);
		resourceMessage.setVpcId(orderServer.getVpcId().toString());
		SpOVDCNetwork ovdcNetwork = dao.load(SpOVDCNetwork.class, orderServer.getNetworkId());
		SpIpScope ipScope = ovdcNetwork.getIpScopeList().get(0);
		logger.info("buildVmMessage ipAddress:" + vm.getIpAddress() + " NetMask:" + ipScope.getNetMask());
		SubnetInfo info = new SubnetUtils(ipScope.getGateway(), ipScope.getNetMask()).getInfo();
		String cidr = info.getCidrSignature();
		String netcidr = cidr.replaceAll(ipScope.getGateway(), info.getNetworkAddress());
		resourceMessage.setSubnetSegment(netcidr);
		return resourceMessage;
	}
	
	private RestResourceMessage buildLBMessage(UpOrder order, RestResourceMessage resourceMessage) {
		
		UpOrderLoadBalance orderLB = this.dao.list(UpOrderLoadBalance.class, "order", order).get(0);
		
		List<Object[]> datas = queryDao.querySql("select name,sp_uuid from sp_load_balancer_virtual_server where status = 'active' and name like '%-OD" + orderLB.getId() + "-'", null);
		if(datas.size() == 0) {
			throw new RuntimeException("LoadBalancer not found.");
		}
		Object[] data = datas.get(0);
		resourceMessage.setInstId(data[1].toString());
		resourceMessage.setInstName(data[0].toString());
		resourceMessage.setOperationType("1");
		return resourceMessage;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private RestResourceMessage buildBySpEntityMessage(Class clz, UpOrder order, RestResourceMessage resourceMessage) {
		BaseSpEntity entity = (BaseSpEntity) this.dao.list(clz, "order", order).get(0);
		resourceMessage.setInstId(entity.getSpUuid());
		resourceMessage.setInstName(entity.getName());
		return resourceMessage;
	}
	
	private String postResource(HttpClient client, String path, String body) {
        logger.info("RestApi post request Url:"+ path);
        logger.info("RestApi post request Body:"+body);
        HttpPost request = new HttpPost(path);
        request.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
//	  	request.addHeader("X-Target-Device", uri);
	  	StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);    
	  	stringEntity.setContentEncoding("UTF-8");
	  	stringEntity.setContentType("application/json");
	  	request.setEntity(stringEntity);
	  	HttpResponse response;
		try {
			response = client.execute(request);

		} catch (IOException e) {
			throw new RuntimeException("RestApi Failed to get response from RestApi.",e);
		}
		if (response.getStatusLine().getStatusCode()>=400) {
			throw new RuntimeException("RestApi Response status code: "+response.getStatusLine().getStatusCode() + " Reason: "+response.getStatusLine().getReasonPhrase());
		}
		HttpEntity entity = response.getEntity();
		try {
			return EntityUtils.toString(entity);
		} catch (UnsupportedOperationException e) {
			throw new RuntimeException("RestApi Read response body UnsupportedOperationException.", e);
		} catch (IOException e) {
			throw new RuntimeException("RestApi Read response body io error.", e);
		}
		
    }

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void createBareMetalMessage(SpBareMetal entity){
		if(entity.getQuotaDetail() == null){
			return;
		}
		UpOrderQuotaDetail quotaDetail = entity.getQuotaDetail();
		if(quotaDetail.getQuotaDetailStatus() == QuotaDetailStatus.finish || !entity.getSpOrg().getId().equals(quotaDetail.getSpOrg().getId())){
			return;
		}
		try {
			UpOrderQuota quota = quotaDetail.getQuota();
			RestResourceMessage resourceMessage = new RestResourceMessage();
			resourceMessage.setName(entity.getName());
			resourceMessage.setCityId(getSourceRegion(quotaDetail).getCode());
			resourceMessage.setResourcePoolId(getSourceRegion(quotaDetail).getCode() + "01");
			resourceMessage.setUserId(quota.getOwner().getSsoUserId());
			resourceMessage.setInstId("BareMetal-" + entity.getId());
			resourceMessage.setInstName(entity.getName());
			resourceMessage.setMainAgreementId(quota.getCode());
			resourceMessage.setDoorOrderItemId(quotaDetail.getSubCode());
			resourceMessage.setOperationType("1");
			dao.insert(resourceMessage);
			RestMessage message = new RestMessage();
			message.setResourceMessage(resourceMessage);
			message.setMessageType("1"); // 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
			message.setMessageStatus(RestMessageStatus.unsend);
			message.setType(RestMessageType.message);
			message.setName(entity.getName());
			message.setCustomNo(entity.getSpOrg().getCustomNo());
			message.setChannel(entity.getQuotaDetail().getChannel());
			message.setResultCode(1); // 1=成功 -1 =失败
			dao.insert(message);
			quotaService.finishQuotaDetail(quotaDetail.getId());
			entity.setMessage(true);
			dao.update(entity, "isMessage");
		}catch(Exception e){
			logger.error(e.getMessage(),e);
		}
	}
    
    static HttpClient getRestClient() {
    	SSLContext sslContext;
		try {
			sslContext = new SSLContextBuilder()
				      .loadTrustMaterial(null, (certificate, authType) -> true).build();
			CloseableHttpClient client = HttpClients.custom()
			  		.setSSLContext(sslContext)
				        .setSSLHostnameVerifier(new NoopHostnameVerifier())
				        .build();
		        return client;
		} catch (KeyManagementException e) {
			throw new RuntimeException("RestApi Failed to create http client.", e);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("RestApi Failed to create http client.", e);
		} catch (KeyStoreException e) {
			throw new RuntimeException("RestApi Failed to create http client.", e);
		}
    }

	private SpRegionEntity getSourceRegion(UpOrderQuotaDetail quotaDetail){
		return quotaDetail.getSourceRegion() == null ? quotaDetail.getRegion() : quotaDetail.getSourceRegion();
	}
}
