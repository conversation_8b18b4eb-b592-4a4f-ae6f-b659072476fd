package io.aicloudware.portal.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "批量简单操作记录")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpSimpleOperateBean.class})
public class SpSimpleOperateBean extends UpApplicationBean {

    @ApiModelProperty(value = "申请单操作类型")
    private UpApplicationType applicationType;

    @ApiModelProperty(value = "批量操作对象ID列表")
    private Integer[] idList;

    public UpApplicationType getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(UpApplicationType applicationType) {
        this.applicationType = applicationType;
    }

    public Integer[] getIdList() {
        return idList;
    }

    public void setIdList(Integer[] idList) {
        this.idList = idList;
    }

}
