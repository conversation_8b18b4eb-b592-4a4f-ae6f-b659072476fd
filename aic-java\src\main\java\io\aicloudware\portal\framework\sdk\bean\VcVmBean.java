package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vCenter虚拟机")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcVmBean extends VcRecordBean {

    @ApiModelProperty(value = "主机ID")
    private Integer hostId;

    @ApiModelProperty(value = "主机名称")
    private String hostName;

    @ApiModelProperty(value = "CPU（核）")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存（GB）")
    private Integer memoryGB;

    @ApiModelProperty(value = "总磁盘（GB）")
    private Integer diskGB;

    @ApiModelProperty(value = "电源状态")
    private String powerStatus;

    @ApiModelProperty(value = "是否纳管")
    private Boolean managed;

    @ApiModelProperty(value = "磁盘列表")
    private VcVmDiskBean[] diskList;

    @ApiModelProperty(value = "网络列表")
    private VcVmNetworkBean[] networkList;

    public Integer getHostId() {
        return hostId;
    }

    public void setHostId(Integer hostId) {
        this.hostId = hostId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(String powerStatus) {
        this.powerStatus = powerStatus;
    }

    public Boolean getManaged() {
        return managed;
    }

    public void setManaged(Boolean managed) {
        this.managed = managed;
    }

    public VcVmDiskBean[] getDiskList() {
        return diskList;
    }

    public void setDiskList(VcVmDiskBean[] diskList) {
        this.diskList = diskList;
    }

    public VcVmNetworkBean[] getNetworkList() {
        return networkList;
    }

    public void setNetworkList(VcVmNetworkBean[] networkList) {
        this.networkList = networkList;
    }
}
