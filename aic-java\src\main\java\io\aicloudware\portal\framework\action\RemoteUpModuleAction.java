package io.aicloudware.portal.framework.action;

import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.bean.UpDataListBean;
import io.aicloudware.portal.framework.bean.UpSimpleOperateBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.remote.RemoteHost;

import java.util.List;

public class RemoteUpModuleAction<B extends RecordBean, L extends UpDataListBean<B>, S extends SearchBean<B>, R extends ResultListBean<B>>
        extends RemoteModuleAction<B, S, R> {

    protected RemoteUpModuleAction(RemoteHost remoteHost, String module) {
        super(remoteHost, module);
    }

    @Override
    protected final Class<R> getResultType() {
        return (Class<R>) types[3];
    }

    public B add(B bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/add", bean);
            return getResultData(responseBean, (Class<B>) bean.getClass());
        });
    }

    public L add(L bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/add_list", bean);
            return getResultData(responseBean, (Class<L>) bean.getClass());
        });
    }

    public B update(B bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPut("/" + getModule() + "/update/" + bean.getId(), bean);
            return getResultData(responseBean, (Class<B>) bean.getClass());
        });
    }

    public L update(L bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/update_list", bean);
            return getResultData(responseBean, (Class<L>) bean.getClass());
        });
    }

    public Boolean delete(Integer id) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonDelete("/" + getModule() + "/delete/" + id);
            return getResultData(responseBean, Boolean.class);
        });
    }

    public Boolean delete(List<Integer> idList) throws SDKException {
        return simpleOperate("delete_list", idList);
    }

    public Boolean simpleOperate(String type, List<Integer> idList) throws SDKException {
        return jsonRemote(() -> {
            UpSimpleOperateBean bean = new UpSimpleOperateBean();
            bean.setIdList(idList.toArray(new Integer[idList.size()]));
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/" + type, bean);
            return getResultData(responseBean, Boolean.class);
        });
    }
}
