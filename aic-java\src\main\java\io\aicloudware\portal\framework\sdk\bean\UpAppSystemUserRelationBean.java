package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "应用系统用户关系")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpAppSystemUserRelationBean.class})
public class UpAppSystemUserRelationBean extends RecordBean {

    @ApiModelProperty(value = "所有者用户ID")
    private Integer userId;

    @ApiModelProperty(value = "所有者用户名称")
    private String userName;

    @ApiModelProperty(value = "所有者显示名称")
    private String userDisplayName;

    private SpRegionBean region;

    private UpAppSystemBean appSystem;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserDisplayName() {
        return userDisplayName;
    }

    public void setUserDisplayName(String userDisplayName) {
        this.userDisplayName = userDisplayName;
    }

    public SpRegionBean getRegion() {
        return region;
    }

    public void setRegion(SpRegionBean region) {
        this.region = region;
    }

    public UpAppSystemBean getAppSystem() {
        return appSystem;
    }

    public void setAppSystem(UpAppSystemBean appSystem) {
        this.appSystem = appSystem;
    }
}
