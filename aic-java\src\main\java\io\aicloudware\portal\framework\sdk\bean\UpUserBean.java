package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.IPassword;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "用户")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpUserBean.class})
public class UpUserBean extends RecordBean implements IPassword, IDisplayName {

	@ApiModelProperty(value = "region")
	private SpRegionBean region;
	
	@ApiModelProperty(value = "姓名", position = 120)
    private String displayName;
	
	@ApiModelProperty(value = "密码")
    private String password;
	
    @ApiModelProperty(value = "邮箱地址", position = 130)
    private String email;

    @ApiModelProperty(value = "手机号", position = 140)
    private String mobile;

    @ApiModelProperty(value = "电话号码", position = 150)
    private String telephone;

    @ApiModelProperty(value = "说明", position = 160)
    private String comment;

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;

    @ApiModelProperty(value = "是否企业用户", position = 180)
    private Boolean tenantAdmin;

	@ApiModelProperty(value = "是否租户管理员", position = 180)
	private Boolean orgAdmin;
    
    @ApiModelProperty(value = "是否注销", position = 190)
    private Boolean isDelete;
    
    @ApiModelProperty(value = "SSO用户ID", position = 200)
    private Integer ucId;
    
    @ApiModelProperty(value = "SSO账号", position = 210)
    private String username;
    
    @ApiModelProperty(value = "是否通过实名认证", position = 220)
    private Boolean isVerify;

    @ApiModelProperty(value = "组织名称")
	private String orgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer orgId;
	
	@ApiModelProperty(value = "用户类别")
	private String type;
	
	@ApiModelProperty(value = "sso token")
	private String ssoToken;
	
	@ApiModelProperty(value = "冻结")
	private Boolean suspend;
	
	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public RecordStatus getStatus() {
		return status;
	}

	public void setStatus(RecordStatus status) {
		this.status = status;
	}

	public Boolean getTenantAdmin() {
		return tenantAdmin;
	}

	public void setTenantAdmin(Boolean tenantAdmin) {
		this.tenantAdmin = tenantAdmin;
	}

	public Boolean getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}

	public Integer getUcId() {
		return ucId;
	}

	public void setUcId(Integer ucId) {
		this.ucId = ucId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Boolean getIsVerify() {
		return isVerify;
	}

	public void setIsVerify(Boolean isVerify) {
		this.isVerify = isVerify;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Integer getOrgId() {
		return orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

	public String getSsoToken() {
		return ssoToken;
	}

	public void setSsoToken(String ssoToken) {
		this.ssoToken = ssoToken;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Boolean getSuspend() {
		return suspend;
	}

	public void setSuspend(Boolean suspend) {
		this.suspend = suspend;
	}

	public SpRegionBean getRegion() {
		return region;
	}

	public void setRegion(SpRegionBean region) {
		this.region = region;
	}

	public Boolean getOrgAdmin() {
		return orgAdmin;
	}

	public void setOrgAdmin(Boolean orgAdmin) {
		this.orgAdmin = orgAdmin;
	}
}
