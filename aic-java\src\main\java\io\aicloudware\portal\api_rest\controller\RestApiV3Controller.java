package io.aicloudware.portal.api_rest.controller;

import io.aicloudware.portal.api_rest.framework.bean.RestV3ResponseBean;
import io.aicloudware.portal.api_rest.service.IRestApiService;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.product.IUpServicePlanService;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanBean;
import io.aicloudware.portal.framework.sdk.bean.V3.SpServerConnectionV3Bean;
import io.aicloudware.portal.framework.sdk.contants.SpServerConnectionType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.platform_vcd.service.ISpServerConnectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * rest api
 *
 * <AUTHOR>
@Controller
@RequestMapping("/rest/v3")
@Api(value = "/rest", description = "REST API")
public class RestApiV3Controller extends BaseController {

    @Autowired
    private IRestApiService restApiService;

    @Autowired
    private IUpServicePlanService upServicePlanService;

    @Autowired
    private ISpServerConnectionService spServerConnectionService;

    @RequestMapping(value = "/enums", method = RequestMethod.GET)
    @ResponseBody
    public RestV3ResponseBean<Map<String, Enum[]>> enums() {
        Map<String, Enum[]> map = new HashMap<>();
        map.put("cloudConnectionType", SpServerConnectionType.values());
        map.put("servicePlanType", UpServicePlanType.values());
        map.put("servicePlanItemType", UpServicePlanItemType.values());
        map.put("servicePlanItemSubType", UpServicePlanItemType.values());
        map.put("orderStatus", UpOrderSystemEnums.OrderStatus.values());
        return RestV3ResponseBean.success(map);
    }

    @RequestMapping(value = "/cloud_connection/list", method = RequestMethod.GET)
    @ResponseBody
    public RestV3ResponseBean<SpServerConnectionV3Bean[]> serverList() {
        return RestV3ResponseBean.success(spServerConnectionService.v3List());
    }

    @RequestMapping(value = "/service_plan/query", method = RequestMethod.POST)
    @ResponseBody
    public RestV3ResponseBean<UpServicePlanBean[]> servicePlanQuery(@ApiParam(value = "实例对象") @Valid @RequestBody UpServicePlanBean bean) {
        return RestV3ResponseBean.success(upServicePlanService.list(bean));
    }

    @RequestMapping(value = "/order/add", method = RequestMethod.POST)
    @ResponseBody
    public RestV3ResponseBean<String> instanceAdd(@ApiParam(value = "实例对象") @Valid @RequestBody RestBean bean) {
        return RestV3ResponseBean.success(UUID.randomUUID().toString());
    }

    @RequestMapping(value = "/order/deployment_status/{uuid}", method = RequestMethod.GET)
    @ResponseBody
    public RestV3ResponseBean<Map<String, String>> deploymentStatus(@ApiParam(value = "对象ID") @PathVariable String uuid) {
        Map<String, String> map = new HashMap<>();
        map.put("uuid", uuid);
        map.put("orderStatus", UpOrderSystemEnums.OrderStatus.deploying.name());
        return RestV3ResponseBean.success(map);
    }

}

class RestBean {
    private String servicePlanCode;
    private Integer amount;
    private String tenantAccount;

    public String getServicePlanCode() {
        return servicePlanCode;
    }

    public void setServicePlanCode(String servicePlanCode) {
        this.servicePlanCode = servicePlanCode;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getTenantAccount() {
        return tenantAccount;
    }

    public void setTenantAccount(String tenantAccount) {
        this.tenantAccount = tenantAccount;
    }
}