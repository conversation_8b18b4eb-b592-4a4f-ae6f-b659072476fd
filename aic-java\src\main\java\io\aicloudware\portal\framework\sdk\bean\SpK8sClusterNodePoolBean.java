package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "K8S集群节点池")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpK8sClusterNodePoolBean.class})
public class SpK8sClusterNodePoolBean extends SpRecordBean {

    private Integer k8sClusterId;

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private String uid;

    @ApiModelProperty(value = "创建时间戳")
    private Date creationTimestamp;

    @ApiModelProperty(value = "更新时间戳")
    private Date updateTimestamp;

    @ApiModelProperty(value = "初始节点数量")
    private Integer initialNodeCount;

    @ApiModelProperty(value = "根卷类型")
    private String rootVolumeType;

    @ApiModelProperty(value = "根卷大小")
    private Integer rootVolumeSize;

    //split with ,
    @ApiModelProperty(value = "数据卷类型")
    private String dataVolumeType;

    @ApiModelProperty(value = "数据卷大小")
    private String dataVolumeSize;

    @ApiModelProperty(value = "规格")
    private String flavor;

    @ApiModelProperty(value = "资源版本")
    private String resourceVersion;

    @ApiModelProperty(value =  "子网ID")
    private Integer subnetId;

    @ApiModelProperty(value = "子网名称")
    private String subnetName;

    @ApiModelProperty(value = "运行时")
    private String runtime;

    @ApiModelProperty(value = "自动扩展启用")
    private Boolean autoscalingEnable;

    @ApiModelProperty(value = "自动扩展最大节点数量")
    private Integer autoscalingMaxNodeCount;

    @ApiModelProperty(value = "自动扩展最小节点数量")
    private Integer autoscalingMinNodeCount;

    @ApiModelProperty(value = "自动扩展优先级")
    private Integer autoscalingPriority;

    @ApiModelProperty(value = "当前节点数量")
    private Integer currentNode;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getCreationTimestamp() {
        return creationTimestamp;
    }

    public void setCreationTimestamp(Date creationTimestamp) {
        this.creationTimestamp = creationTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public Integer getInitialNodeCount() {
        return initialNodeCount;
    }

    public void setInitialNodeCount(Integer initialNodeCount) {
        this.initialNodeCount = initialNodeCount;
    }

    public String getRootVolumeType() {
        return rootVolumeType;
    }

    public void setRootVolumeType(String rootVolumeType) {
        this.rootVolumeType = rootVolumeType;
    }

    public Integer getRootVolumeSize() {
        return rootVolumeSize;
    }

    public void setRootVolumeSize(Integer rootVolumeSize) {
        this.rootVolumeSize = rootVolumeSize;
    }

    public String getDataVolumeType() {
        return dataVolumeType;
    }

    public void setDataVolumeType(String dataVolumeType) {
        this.dataVolumeType = dataVolumeType;
    }

    public String getDataVolumeSize() {
        return dataVolumeSize;
    }

    public void setDataVolumeSize(String dataVolumeSize) {
        this.dataVolumeSize = dataVolumeSize;
    }

    public String getFlavor() {
        return flavor;
    }

    public void setFlavor(String flavor) {
        this.flavor = flavor;
    }

    public String getResourceVersion() {
        return resourceVersion;
    }

    public void setResourceVersion(String resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    public Integer getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(Integer subnetId) {
        this.subnetId = subnetId;
    }

    public String getSubnetName() {
        return subnetName;
    }

    public void setSubnetName(String subnetName) {
        this.subnetName = subnetName;
    }

    public String getRuntime() {
        return runtime;
    }

    public void setRuntime(String runtime) {
        this.runtime = runtime;
    }

    public Boolean getAutoscalingEnable() {
        return autoscalingEnable;
    }

    public void setAutoscalingEnable(Boolean autoscalingEnable) {
        this.autoscalingEnable = autoscalingEnable;
    }

    public Integer getAutoscalingMaxNodeCount() {
        return autoscalingMaxNodeCount;
    }

    public void setAutoscalingMaxNodeCount(Integer autoscalingMaxNodeCount) {
        this.autoscalingMaxNodeCount = autoscalingMaxNodeCount;
    }

    public Integer getAutoscalingMinNodeCount() {
        return autoscalingMinNodeCount;
    }

    public void setAutoscalingMinNodeCount(Integer autoscalingMinNodeCount) {
        this.autoscalingMinNodeCount = autoscalingMinNodeCount;
    }

    public Integer getAutoscalingPriority() {
        return autoscalingPriority;
    }

    public void setAutoscalingPriority(Integer autoscalingPriority) {
        this.autoscalingPriority = autoscalingPriority;
    }

    public Integer getK8sClusterId() {
        return k8sClusterId;
    }

    public void setK8sClusterId(Integer k8sClusterId) {
        this.k8sClusterId = k8sClusterId;
    }

    public Integer getCurrentNode() {
        return currentNode;
    }

    public void setCurrentNode(Integer currentNode) {
        this.currentNode = currentNode;
    }
}
