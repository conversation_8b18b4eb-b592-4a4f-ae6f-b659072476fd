package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpCodeMappingBean;
import io.aicloudware.portal.framework.sdk.contants.UpCodeMappingType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name = "up_code_mapping")
@Access(AccessType.FIELD)
public class UpCodeMapping extends BaseUpEntity<UpCodeMappingBean> {

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpCodeMappingType type;

    @Column(name = "description", length = ApiConstants.STRING_MAX_LENGTH)
    private String description;

    public UpCodeMappingType getType() {
        return type;
    }

    public void setType(UpCodeMappingType type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
