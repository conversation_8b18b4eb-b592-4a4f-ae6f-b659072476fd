package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpResourceType;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.validate.*;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "资源配额")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpQuotaBean.class})
public class UpQuotaBean extends RecordBean {

    @ApiModelProperty(value = "所有者用户ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者用户名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "虚机可用数量")
    private SpService service;

    private String serviceText;

    @ApiModelProperty(value = "vm_used_num")
    private SpResourceType resourceType;

    private String resourceTypeText;

    @ApiModelProperty(value = "vm_max_num")
    private Integer quota;

    private Integer assignedQuota;

    @ApiModelProperty(value = "vm_used_num")
    private Integer usedQuota;

    @ApiModelProperty(value = "sp_org_id")
    private Integer orgId;

    @ApiModelProperty(value = "sp_org_name")
    private String orgName;

    @ApiModelProperty(value = "是否为管理员配额")
    private Boolean isAdminQuota;

    @ApiModelProperty(value = "区域")
    private Integer regionId;

    private String regionName;

    private Boolean isUserTemplate;

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public SpResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(SpResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getUsedQuota() {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota) {
        this.usedQuota = usedQuota;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public Boolean getAdminQuota() {
        return isAdminQuota;
    }

    public void setAdminQuota(Boolean isAdminQuota) {
        this.isAdminQuota = isAdminQuota;
    }

    public Boolean getIsUserTemplate() {
        return isUserTemplate;
    }

    public void setIsUserTemplate(Boolean isUserTemplate) {
        isUserTemplate = isUserTemplate;
    }

    public Integer getAssignedQuota() {
        return assignedQuota;
    }

    public void setAssignedQuota(Integer assignedQuota) {
        this.assignedQuota = assignedQuota;
    }

    public String getServiceText() {
        return serviceText;
    }

    public void setServiceText(String serviceText) {
        this.serviceText = serviceText;
    }

    public String getResourceTypeText() {
        return resourceTypeText;
    }

    public void setResourceTypeText(String resourceTypeText) {
        this.resourceTypeText = resourceTypeText;
    }
}
