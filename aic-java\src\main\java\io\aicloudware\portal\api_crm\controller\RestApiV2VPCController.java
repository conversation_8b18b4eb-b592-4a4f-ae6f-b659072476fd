package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.service.ISpVPCService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2/vpc")
@Api(value = "/api/v2/vpc", description = "REST API V2")
public class RestApiV2VPCController extends BaseEntityController<SpVPC, SpVPCBean, SpVPCResultBean> {

    @Autowired
    private ISpVPCService spVPCService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody SpVPCBean bean, HttpServletRequest request) {
        SpVPC spVPC = BeanCopyUtil.copy(bean, SpVPC.class);
        SpOrgBean spOrgBean = commonService.load(SpOrg.class, SpOrgBean.class, ThreadCache.getOrgId());
        SpOrg spOrg = BeanCopyUtil.copy(spOrgBean, SpOrg.class);
        spVPC.setSpOrg(spOrg);
        spVPC.setRegion(ThreadCache.getRegion());
        List<SpOVDCNetwork> ovdcNetworks = new ArrayList<>();

        for (SpOVDCNetworkBean ovdcNetworkBean : bean.getOvdcNetworkList()) {
            SpOVDCNetwork ovdcNetwork = BeanCopyUtil.copy(ovdcNetworkBean, SpOVDCNetwork.class);
            ovdcNetwork.setRegion(ThreadCache.getRegion());
            ovdcNetworks.add(ovdcNetwork);
        }
        return ResponseBean.success(spVPCService.addVPC(spVPC, ovdcNetworks.get(0)).getId());
    }

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public ResponseBean delete(@PathVariable Integer id, HttpServletRequest request) {
        try {
            spVPCService.deleteVPC(id);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBean.error(2, "客户端业务逻辑异常", "该VPC正在被使用，无法删除");
        }
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpVPCSearchBean searchBean) {
        SpVPC entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if(StringUtils.isNotEmpty(entity.getName())) {
            SpVPCBean fuzzyBean = new SpVPCBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
        SpVPCBean[] entityList = spVPCService.searchVPC(entity.getSpOrg(), entity.getRegion(), searchBean).toArray(
                new SpVPCBean[0]
        );
        ResultListBean<SpVPCBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);

//        for (SpVPCBean vpcBean : entityList) {
//            List<SpOVDCNetworkBean> ovdcNetworkBeanList = new ArrayList<>();
//            if (vpcBean.getVpcRelation() != null) {
//                for (SpVPCRelationBean relation :vpcBean.getVpcRelation()) {
//                    SpOVDCNetworkBean bean = spVPCService.getOvdcNetwork(relation.getOvdcNetworkId());
//                    ovdcNetworkBeanList.add(bean);
//                }
//            }
//            vpcBean.setOvdcNetworkList(ovdcNetworkBeanList.toArray(new SpOVDCNetworkBean[0]));
//        }
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }

}
