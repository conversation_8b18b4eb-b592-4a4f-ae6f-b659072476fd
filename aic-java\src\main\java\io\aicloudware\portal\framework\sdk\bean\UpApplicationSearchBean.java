package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationStatus;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "申请单查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApplicationSearchBean extends SearchBean<UpApplicationBean> {

    @ApiModelProperty(value = "申请单状态列表")
    private UpApplicationStatus[] applicationStatusList;

    public UpApplicationStatus[] getApplicationStatusList() {
        return applicationStatusList;
    }

    public void setApplicationStatusList(UpApplicationStatus[] applicationStatusList) {
        this.applicationStatusList = applicationStatusList;
    }
}
