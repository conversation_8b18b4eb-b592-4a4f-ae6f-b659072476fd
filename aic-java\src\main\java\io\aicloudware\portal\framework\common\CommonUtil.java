package io.aicloudware.portal.framework.common;

import java.util.ArrayList;
import java.util.List;

import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.contants.UpRightType;
import io.aicloudware.portal.framework.utility.Utility;

public class CommonUtil {

    public static boolean isHasRight(List<UpRightBean> rightList, UpRightType type) {
        for (UpRightBean bean : rightList) {
            if (bean.getType().equals(type)) {
                return !bean.getType().isDynamic();
            }
        }
        return false;
    }

    public static boolean isHasRight(List<UpRightBean> rightList, String rightTypeGroup) {
        rightTypeGroup = Utility.toEmpty(rightTypeGroup);
        for (UpRightBean bean : rightList) {
            if (rightTypeGroup.equals(bean.getType().getType())) {
                return true;
            }
        }
        return false;
    }

    public static List<Integer> getTargetIdList(List<UpRightBean> rightList, UpRightType type) {
        List<Integer> idList = new ArrayList<>();
        for (UpRightBean bean : rightList) {
            if (bean.getType().isDynamic() && bean.getType().equals(type)) {
                idList.add(bean.getTargetId());
            }
        }
        return idList;
    }

}
