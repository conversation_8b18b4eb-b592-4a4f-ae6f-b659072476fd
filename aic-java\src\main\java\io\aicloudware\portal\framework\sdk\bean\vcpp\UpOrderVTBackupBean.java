package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedFreq;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedRet;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "备份")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderVTBackupBean.class})
public class UpOrderVTBackupBean extends SpRecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "类型")
	private VTBackupType backupType;
	
	@ApiModelProperty(value = "频率")
	private VTBackupSchedFreq schedFreq;
	
	@ApiModelProperty(value = "备份日期")
	private Integer schedDate;
	
	@ApiModelProperty(value = "保留日期")
	private VTBackupSchedRet schedRet;
	
	@ApiModelProperty(value = "目标云主机")
	private Integer vmId;
	
	@ApiModelProperty(value = "目标云主机")
	private String vmName;
	
	@ApiModelProperty(value = "产品配置")
	private Integer configId;

	@ApiModelProperty(value = "所有者")
    private Integer ownerId;
	
	@ApiModelProperty(value = "所有者")
	private String ownerName;

	@ApiModelProperty(value = "订单")
    private Integer orderId;
	
	@ApiModelProperty(value = "租户")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "租户")
	private String spOrgName;

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public VTBackupType getBackupType() {
		return backupType;
	}

	public void setBackupType(VTBackupType backupType) {
		this.backupType = backupType;
	}

	public VTBackupSchedFreq getSchedFreq() {
		return schedFreq;
	}

	public void setSchedFreq(VTBackupSchedFreq schedFreq) {
		this.schedFreq = schedFreq;
	}

	public Integer getSchedDate() {
		return schedDate;
	}

	public void setSchedDate(Integer schedDate) {
		this.schedDate = schedDate;
	}

	public VTBackupSchedRet getSchedRet() {
		return schedRet;
	}

	public void setSchedRet(VTBackupSchedRet schedRet) {
		this.schedRet = schedRet;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public String getVmName() {
		return vmName;
	}

	public void setVmName(String vmName) {
		this.vmName = vmName;
	}

	public Integer getConfigId() {
		return configId;
	}

	public void setConfigId(Integer configId) {
		this.configId = configId;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public String getOwnerName() {
		return ownerName;
	}

	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

}
