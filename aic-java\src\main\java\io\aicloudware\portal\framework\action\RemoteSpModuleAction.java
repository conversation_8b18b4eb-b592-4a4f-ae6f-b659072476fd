package io.aicloudware.portal.framework.action;

import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.bean.SpDataListBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.utility.Utility;

import java.lang.reflect.Array;

public class RemoteSpModuleAction<B extends RecordBean, L extends SpDataListBean<B>, S extends SearchBean<B>, R extends ResultListBean<B>>
        extends RemoteModuleAction<B, S, R> {

    public RemoteSpModuleAction(RemoteHost remoteHost, String module) {
        super(remoteHost, module);
    }

    protected final Class<L> getDataListBeanType() {
        return (Class<L>) types[1];
    }

    @Override
    protected final Class<R> getResultType() {
        return (Class<R>) types[3];
    }

    private L createRequestBean(B bean, String name, String reason, String comment) {
        L requestBean = Utility.newInstance(getDataListBeanType());
        B[] values = (B[]) Array.newInstance(getModuleType(), 1);
        values[0] = bean;
        requestBean.setDataList(values);
        requestBean.setName(name);
        requestBean.setReason(reason);
        requestBean.setComment(comment);
        return requestBean;
    }

    public UpApplicationBean add(B bean, String name, String reason, String comment) throws SDKException {
        return jsonRemote(() -> add(createRequestBean(bean, name, reason, comment)));
    }

    public UpApplicationBean update(B bean, String name, String reason, String comment) throws SDKException {
        return jsonRemote(() -> update(createRequestBean(bean, name, reason, comment)));
    }

    public UpApplicationBean delete(Integer id, String name, String reason, String comment) throws SDKException {
        return jsonRemote(() -> {
            SpSimpleOperateBean bean = new SpSimpleOperateBean();
            bean.setIdList(new Integer[]{id});
            bean.setName(name);
            bean.setReason(reason);
            bean.setComment(comment);
            return delete(bean);
        });
    }

    public UpApplicationBean add(L bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/add_list", bean);
            return getResultData(responseBean, UpApplicationBean.class);
        });
    }

    public UpApplicationBean update(L bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/update_list", bean);
            return getResultData(responseBean, UpApplicationBean.class);
        });
    }

    public UpApplicationBean delete(SpSimpleOperateBean bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/delete_list", bean);
            return getResultData(responseBean, UpApplicationBean.class);
        });
    }
}
