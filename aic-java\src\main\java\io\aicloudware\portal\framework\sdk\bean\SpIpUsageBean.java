package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "IP地址使用状况")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpIpUsageBean extends SpRecordBean {

    @ApiModelProperty(value = "IP地址范围ID")
    private Integer ipScopeId;

    @ApiModelProperty(value = "IP地址范围名称")
    private String ipScopeName;

    @ApiModelProperty(value = "IP地址范围显示名称")
    private String ipScopeDisplayName;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "关联虚机")
    private String assignedVm;
    
    @ApiModelProperty(value = "IP状态")
    private String ipStatus;

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getIpStatus() {
        return ipStatus;
    }

    public void setIpStatus(String status) {
        this.ipStatus = status;
    }

    public String getAssignedVm() {
        return assignedVm;
    }

    public void setAssignedVm(String assignedVm) {
        this.assignedVm = assignedVm;
    }

}
