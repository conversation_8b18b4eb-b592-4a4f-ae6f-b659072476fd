package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.entity.IUpEntity;
import io.aicloudware.portal.framework.sdk.bean.SpVmNetworkBean;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpIpScope;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class CmVmNetwork<V extends CmVm> extends BaseSpEntity<SpVmNetworkBean> implements IUpEntity<SpVmNetworkBean> {

    @JoinColumn(name = "vm_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private V vm;

    @Column(name = "ip_address")
    private String ipAddress;

    @JoinColumn(name = "network_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOVDCNetwork ovdcNetwork;

    @JoinColumn(name = "network_profile_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpIpScope ipScope;

    @Column(name = "nic_number", nullable = false)
    private Integer nicNumber;
    
    @Column(name = "primary_net", nullable = false)
    private Boolean primaryNet;
    
    @Column(name = "connected", nullable = false)
    private Boolean connected;

    public V getVm() {
        return vm;
    }

    public void setVm(V vm) {
        this.vm = vm;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpIpScope getIpScope() {
        return ipScope;
    }

    public void setIpScope(SpIpScope spIpScope) {
        this.ipScope = spIpScope;
    }

    public Integer getNicNumber() {
        return nicNumber;
    }

    public void setNicNumber(Integer nicNumber) {
        this.nicNumber = nicNumber;
    }

	public SpOVDCNetwork getOvdcNetwork() {
		return ovdcNetwork;
	}

	public void setOvdcNetwork(SpOVDCNetwork ovdcNetwork) {
		this.ovdcNetwork = ovdcNetwork;
	}

	public Boolean getPrimaryNet() {
		return primaryNet;
	}

	public void setPrimaryNet(Boolean primaryNet) {
		this.primaryNet = primaryNet;
	}

	public Boolean getConnected() {
		return connected;
	}

	public void setConnected(Boolean connected) {
		this.connected = connected;
	}
    
	

}
