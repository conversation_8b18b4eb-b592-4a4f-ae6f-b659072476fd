package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerNetworkBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;

@Entity
@Table(name = "up_order_cloud_server_network")
@Access(AccessType.FIELD)
public class UpOrderCloudServerNetwork extends UpOrderProduct<UpOrderCloudServerNetworkBean> implements IOrderEntity {

    @Column(name = "nic_number")
    private Integer nicNumber;

    @Column(name = "network_id")
    private Integer networkId;

    @Column(name = "network_adapter_type")
    @Enumerated(EnumType.STRING)
    private UpOrderSystemEnums.NetworkAdapterType networkAdapterType;

    @Column(name = "ip_address_allocation_mode")
    @Enumerated(EnumType.STRING)
    private UpOrderSystemEnums.IpAddressAllocationMode ipAddressAllocationMode;

    @Column(name = "fence_mode")
    @Enumerated(EnumType.STRING)
    private UpOrderSystemEnums.FenceMode fenceMode;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "connected")
    private Boolean connected;

    @Column(name = "cloud_server_id")
    private Integer cloudServerId;

    @JoinColumn(name = "order_cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrderCloudServer orderCloudServer;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    public Integer getNicNumber() {
        return nicNumber;
    }

    public void setNicNumber(Integer nicNumber) {
        this.nicNumber = nicNumber;
    }

    public Integer getNetworkId() {
        return networkId;
    }

    public void setNetworkId(Integer networkId) {
        this.networkId = networkId;
    }

    public UpOrderSystemEnums.NetworkAdapterType getNetworkAdapterType() {
        return networkAdapterType;
    }

    public void setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType networkAdapterType) {
        this.networkAdapterType = networkAdapterType;
    }

    public UpOrderSystemEnums.IpAddressAllocationMode getIpAddressAllocationMode() {
        return ipAddressAllocationMode;
    }

    public void setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode ipAddressAllocationMode) {
        this.ipAddressAllocationMode = ipAddressAllocationMode;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Boolean getConnected() {
        return connected;
    }

    public void setConnected(Boolean connected) {
        this.connected = connected;
    }

    public Integer getCloudServerId() {
        return cloudServerId;
    }

    public void setCloudServerId(Integer cloudServerId) {
        this.cloudServerId = cloudServerId;
    }

    public UpOrderCloudServer getOrderCloudServer() {
        return orderCloudServer;
    }

    public void setOrderCloudServer(UpOrderCloudServer orderCloudServer) {
        this.orderCloudServer = orderCloudServer;
    }

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    @Override
    public SpOrg getSpOrg() {
        return spOrg;
    }

    @Override
    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public UpOrderSystemEnums.FenceMode getFenceMode() {
        return fenceMode;
    }

    public void setFenceMode(UpOrderSystemEnums.FenceMode fenceMode) {
        this.fenceMode = fenceMode;
    }
}
