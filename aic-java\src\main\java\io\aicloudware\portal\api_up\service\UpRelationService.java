package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpRelation;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.bean.UpRelationListBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightListBean;
import io.aicloudware.portal.framework.sdk.bean.UpWarrantBean;
import io.aicloudware.portal.framework.sdk.bean.UpWarrantListBean;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;
import io.aicloudware.portal.framework.executor.IExecutorAR;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Transactional
public class UpRelationService extends BaseService implements IUpRelationService {

    private static final Map<Integer, UpRelationBean> RelationCache = new ConcurrentHashMap<>();

    @Autowired
    private IUpRightService rightService;

    private void initCache() {
        if (RelationCache.isEmpty()) {
            synchronized (getClass()) {
                if (RelationCache.isEmpty()) {
                    for (UpRelation relation : dao.list(UpRelation.class)) {
                        RelationCache.put(relation.getId(), BeanCopyUtil.copy2Bean(relation, UpRelationBean.class));
                    }
                }
            }
        }
    }

    @Override
    public void createRelation(UpRelationListBean bean) {
        initCache();

        for (UpRelationBean relationBean : bean.getDataList()) {
            AssertUtil.check(relationBean.getType() != null, "关系类型必须存在");
            for (UpRelationBean _bean : RelationCache.values()) {
                if (relationBean.equals(_bean)) {
                    relationBean.setId(_bean.getId());
                    break;
                }
            }
            if (Utility.isZero(relationBean.getId())) {
                UpRelation relation = BeanCopyUtil.copy(relationBean, UpRelation.class);
                relation.setName(relation.getType().name());
                dao.insert(relation);
                RelationCache.put(relation.getId(), BeanCopyUtil.copy2Bean(relation, UpRelationBean.class));
            }
        }

        clearCache();
    }

    @Override
    public void deleteRelation(List<Integer> relationIdList) {
        initCache();

        dao.destroy(UpRelation.class, relationIdList);
        for (Integer id : relationIdList) {
            RelationCache.remove(id);
        }

        clearCache();
    }

    @Override
    public void directlyWarrant(UpWarrantListBean bean) {
        List<UpRightBean> rightList = new ArrayList<>(bean.getDataList().length);
        for (UpWarrantBean warrantBean : bean.getDataList()) {
            UpRightBean rightBean = new UpRightBean();
            rightBean.setType(warrantBean.getRightType());
            rightBean.setTargetId(warrantBean.getTargetId());
            rightList.add(rightBean);
        }
        UpRightListBean rightListBean = new UpRightListBean();
        rightListBean.setDataList(rightList.toArray(new UpRightBean[rightList.size()]));
        rightService.createRight(rightListBean);

        List<UpRelationBean> relationList = new ArrayList<>(bean.getDataList().length);
        for (UpWarrantBean warrantBean : bean.getDataList()) {
            UpRelationBean relationBean = new UpRelationBean();
            if (Utility.isNotZero(warrantBean.getUserId())) {
                relationBean.setType(UpRelationType.user_right);
                relationBean.setUserId(warrantBean.getUserId());
            } else if (Utility.isNotZero(warrantBean.getGroupId())) {
                relationBean.setType(UpRelationType.group_right);
                relationBean.setGroupId(warrantBean.getGroupId());
            } else if (Utility.isNotZero(warrantBean.getRoleId())) {
                relationBean.setType(UpRelationType.role_right);
                relationBean.setRoleId(warrantBean.getRoleId());
            }
            for (UpRightBean rightBean : rightList) {
                if (rightBean.getType().equals(warrantBean.getRightType())
                        && rightBean.getTargetId().equals(warrantBean.getTargetId())) {
                    relationBean.setRightId(rightBean.getId());
                    break;
                }
            }
            relationList.add(relationBean);
        }
        UpRelationListBean relationListBean = new UpRelationListBean();
        relationListBean.setDataList(relationList.toArray(new UpRelationBean[relationList.size()]));
        createRelation(relationListBean);
    }

    @Override
    public List<UpRelationBean> getUserRelationList(Integer userId, UpRelationType... typeList) {
        return getRelationList(userId, UpRelationBean::getUserId, typeList);
    }

    @Override
    public List<UpRelationBean> getGroupRelationList(Integer groupId, UpRelationType... typeList) {
        return getRelationList(groupId, UpRelationBean::getGroupId, typeList);
    }

    @Override
    public List<UpRelationBean> getRoleRelationList(Integer roleId, UpRelationType... typeList) {
        return getRelationList(roleId, UpRelationBean::getRoleId, typeList);
    }

    private List<UpRelationBean> getRelationList(Integer id, IExecutorAR<UpRelationBean, Integer> executor, UpRelationType... types) {
        initCache();

        List<UpRelationType> typeList = Arrays.asList(Utility.isEmpty(types) ? UpRelationType.values() : types);
        List<UpRelationBean> beanList = new ArrayList<>();
        for (UpRelationBean bean : RelationCache.values()) {
            if (id.equals(executor.doExecute(bean)) && typeList.contains(bean.getType())) {
                beanList.add(bean);
            }
        }
        return beanList;
    }

    @Override
    public void clearCache() {
        RelationCache.clear();
    }
}
