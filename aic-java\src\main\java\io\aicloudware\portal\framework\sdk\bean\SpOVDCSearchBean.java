package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "预留列表查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpOVDCSearchBean extends SearchBean<SpOVDCBean> {

    @ApiModelProperty(value = "蓝图机器策略ID")
    private Integer blueprintMachineReservationPolicyId;

    public Integer getBlueprintMachineReservationPolicyId() {
        return blueprintMachineReservationPolicyId;
    }

    public void setBlueprintMachineReservationPolicyId(Integer blueprintMachineReservationPolicyId) {
        this.blueprintMachineReservationPolicyId = blueprintMachineReservationPolicyId;
    }
}
