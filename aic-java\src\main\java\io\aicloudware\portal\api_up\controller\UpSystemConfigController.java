package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/system_config")
public class UpSystemConfigController {

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @RequestMapping(value = "/get/ai_url", method = RequestMethod.GET)
    @ApiOperation(notes = "/get/ai_url", httpMethod = "GET", value = "获取配置项")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "", response = String.class)})
    @ResponseBody
    public ResponseBean get()  {
        UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.ai_url);
        return ResponseBean.success(bean);
    }

    @RequestMapping(value = "/save/ai_url", method = RequestMethod.POST)
    @ApiOperation(notes = "/save/ai_url", httpMethod = "POST", value = "更新配置项")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "更新配置项", response = Integer.class)})
    @ResponseBody
    public ResponseBean set(@RequestBody UpSystemConfigBean bean)  {
        UpSystemConfigBean configBean = upSystemConfigService.get(UpSystemConfigKey.ai_url);
        configBean.setValue(bean.getValue());
        UpSystemConfigListBean listBean = new UpSystemConfigListBean();
        listBean.setDataList(new UpSystemConfigBean[]{configBean});
        upSystemConfigService.save(listBean);
        upSystemConfigService.refreshConfig(UpSystemConfigKey.ai_url);
        return ResponseBean.success(true);
    }
}
