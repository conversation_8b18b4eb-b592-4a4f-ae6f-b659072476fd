package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "性能数据")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpPerformanceBean {

    @ApiModelProperty(value = "时间")
    private String datetime;

    @ApiModelProperty(value = "CPU总量")
    private Long cpuTotalHz;

    @ApiModelProperty(value = "CPU使用")
    private Long cpuUsageHz;

    @ApiModelProperty(value = "内存总量")
    private Long memoryTotalB;

    @ApiModelProperty(value = "内存使用")
    private Long memoryUsageB;

    @ApiModelProperty(value = "存储总量")
    private Long diskTotalB;

    @ApiModelProperty(value = "存储使用")
    private Long diskUsageB;

    @ApiModelProperty(value = "网络使用")
    private Long networkUsageBps;

    @ApiModelProperty(value = "性能数据列表")
    private UpPerformanceBean[] dataList;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public Long getCpuTotalHz() {
        return cpuTotalHz;
    }

    public void setCpuTotalHz(Long cpuTotalHz) {
        this.cpuTotalHz = cpuTotalHz;
    }

    public Long getCpuUsageHz() {
        return cpuUsageHz;
    }

    public void setCpuUsageHz(Long cpuUsageHz) {
        this.cpuUsageHz = cpuUsageHz;
    }

    public Long getMemoryTotalB() {
        return memoryTotalB;
    }

    public void setMemoryTotalB(Long memoryTotalB) {
        this.memoryTotalB = memoryTotalB;
    }

    public Long getMemoryUsageB() {
        return memoryUsageB;
    }

    public void setMemoryUsageB(Long memoryUsageB) {
        this.memoryUsageB = memoryUsageB;
    }

    public Long getDiskTotalB() {
        return diskTotalB;
    }

    public void setDiskTotalB(Long diskTotalB) {
        this.diskTotalB = diskTotalB;
    }

    public Long getDiskUsageB() {
        return diskUsageB;
    }

    public void setDiskUsageB(Long diskUsageB) {
        this.diskUsageB = diskUsageB;
    }

    public Long getNetworkUsageBps() {
        return networkUsageBps;
    }

    public void setNetworkUsageBps(Long networkUsageBps) {
        this.networkUsageBps = networkUsageBps;
    }

    public UpPerformanceBean[] getDataList() {
        return dataList;
    }

    public void setDataList(UpPerformanceBean[] dataList) {
        this.dataList = dataList;
    }
}
