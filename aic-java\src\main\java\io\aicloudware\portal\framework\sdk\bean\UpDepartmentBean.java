package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "部门")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpDepartmentBean.class})
public class UpDepartmentBean extends RecordBean implements IDisplayName {

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "部门编码")
    private String departmentCode;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否激活")
    private Boolean isActive;

    @ApiModelProperty(value = "父部门ID")
    private Integer parentDepartmentId;

    @ApiModelProperty(value = "父部门名称")
    private String parentDepartmentName;

    @ApiModelProperty(value = "父部门显示名称")
    private String parentDepartmentDisplayName;

    @ApiModelProperty(value = "子部门列表")
    private UpDepartmentBean[] childDepartments;

    @ApiModelProperty(value = "部门用户关系列表")
    private UpDepartmentUserRelationBean[] departmentUserRelations;

    @ApiModelProperty(value = "用户数量")
    private Integer userCount;

    @ApiModelProperty(value = "子部门数量")
    private Integer childDepartmentCount;

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getParentDepartmentId() {
        return parentDepartmentId;
    }

    public void setParentDepartmentId(Integer parentDepartmentId) {
        this.parentDepartmentId = parentDepartmentId;
    }

    public String getParentDepartmentName() {
        return parentDepartmentName;
    }

    public void setParentDepartmentName(String parentDepartmentName) {
        this.parentDepartmentName = parentDepartmentName;
    }

    public String getParentDepartmentDisplayName() {
        return parentDepartmentDisplayName;
    }

    public void setParentDepartmentDisplayName(String parentDepartmentDisplayName) {
        this.parentDepartmentDisplayName = parentDepartmentDisplayName;
    }

    public UpDepartmentBean[] getChildDepartments() {
        return childDepartments;
    }

    public void setChildDepartments(UpDepartmentBean[] childDepartments) {
        this.childDepartments = childDepartments;
    }

    public UpDepartmentUserRelationBean[] getDepartmentUserRelations() {
        return departmentUserRelations;
    }

    public void setDepartmentUserRelations(UpDepartmentUserRelationBean[] departmentUserRelations) {
        this.departmentUserRelations = departmentUserRelations;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public Integer getChildDepartmentCount() {
        return childDepartmentCount;
    }

    public void setChildDepartmentCount(Integer childDepartmentCount) {
        this.childDepartmentCount = childDepartmentCount;
    }
}
