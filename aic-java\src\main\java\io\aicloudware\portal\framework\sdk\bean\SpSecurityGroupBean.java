package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "安全组")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpSecurityGroupBean.class})
public class SpSecurityGroupBean extends SpRecordBean {
	@ApiModelProperty(value = "描述")
	private String description;
	
	@ApiModelProperty(value = "VPCID")
	private Integer vpcId;
	
	@ApiModelProperty(value = "VPC名称")
	private String vpcName;
	
	@ApiModelProperty(value = "VPC显示名称")
	private String vpcDisplayName;
	
	@ApiModelProperty(value = "安全组ID")
	private Integer securityGroupId;
	
	@ApiModelProperty(value = "安全组名称")
	private String securityGroupName;

	@ApiModelProperty(value = "企业项目ID")
	private String entProjectId;
	
	@ApiModelProperty(value = "安全组显示名称")
	private String securityGroupDisplayName;
	
	@ApiModelProperty(value = "安全组关系列表")
	private SpSecurityGroupRelationBean[] securityGroupRelation;

	public SpSecurityGroupRuleBean[] getSecurityGroupRules() {
		return securityGroupRules;
	}

	public void setSecurityGroupRules(SpSecurityGroupRuleBean[] securityGroupRules) {
		this.securityGroupRules = securityGroupRules;
	}

	@ApiModelProperty(value = "安全组规则列表")
	private SpSecurityGroupRuleBean[] securityGroupRules;

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getSecurityGroupId() {
		return securityGroupId;
	}

	public void setSecurityGroupId(Integer securityGroupId) {
		this.securityGroupId = securityGroupId;
	}

	public String getSecurityGroupName() {
		return securityGroupName;
	}

	public void setSecurityGroupName(String securityGroupName) {
		this.securityGroupName = securityGroupName;
	}

	public String getSecurityGroupDisplayName() {
		return securityGroupDisplayName;
	}

	public void setSecurityGroupDisplayName(String securityGroupDisplayName) {
		this.securityGroupDisplayName = securityGroupDisplayName;
	}

	public SpSecurityGroupRelationBean[] getSecurityGroupRelation() {
		return securityGroupRelation;
	}

	public void setSecurityGroupRelation(SpSecurityGroupRelationBean[] securityGroupRelation) {
		this.securityGroupRelation = securityGroupRelation;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public String getVpcName() {
		return vpcName;
	}

	public void setVpcName(String vpcName) {
		this.vpcName = vpcName;
	}

	public String getVpcDisplayName() {
		return vpcDisplayName;
	}

	public void setVpcDisplayName(String vpcDisplayName) {
		this.vpcDisplayName = vpcDisplayName;
	}

	public String getEntProjectId() {
		return entProjectId;
	}

	public void setEntProjectId(String entProjectId) {
		this.entProjectId = entProjectId;
	}
}
