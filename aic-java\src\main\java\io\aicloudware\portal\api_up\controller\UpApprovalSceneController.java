package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpApprovalScene;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalSceneBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalSceneResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/approval_scene")
@Api(value = "/approval_scene", description = "审批场景设定", position = 612)
public class UpApprovalSceneController extends BaseUpController<UpApprovalScene, UpApprovalSceneBean, UpApprovalSceneResultBean> {
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalSceneResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryApprovalScene(@ApiParam(value = "查询条件") @RequestBody UpApprovalSceneSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalSceneBean.class)})
//    @ResponseBody
//    public ResponseBean getApprovalScene(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalSceneBean.class)})
//    @ResponseBody
//    public ResponseBean addApprovalScene(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalSceneBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalSceneListBean.class)})
//    @ResponseBody
//    public ResponseBean addApprovalScene(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalSceneListBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalSceneBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalScene(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                            @ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalSceneBean bean,
//                                            BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalSceneListBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalScene(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalSceneListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalScene(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalScene(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
