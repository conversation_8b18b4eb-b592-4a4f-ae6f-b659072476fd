package io.aicloudware.portal.framework.sdk.bean.product;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "Cce产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductCceSetBean extends RecordBean {
	
	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "CPU单位数")
	private Integer nodeUnit;

	@ApiModelProperty(value = "control_node_unit")
	private Integer controlNodeUnit;

	@ApiModelProperty(value = "info")
	private String info;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getNodeUnit() {
		return nodeUnit;
	}

	public void setNodeUnit(Integer nodeUnit) {
		this.nodeUnit = nodeUnit;
	}

	public Integer getControlNodeUnit() {
		return controlNodeUnit;
	}

	public void setControlNodeUnit(Integer controlNodeUnit) {
		this.controlNodeUnit = controlNodeUnit;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}
}
