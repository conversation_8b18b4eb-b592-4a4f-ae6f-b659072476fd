package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.IPassword;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "端点")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpEndpointBean.class})
public class SpEndpointBean extends SpRecordBean implements IPassword {

    @ApiModelProperty(value = "端点地址")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String serverUrl;

    @ApiModelProperty(value = "用户名")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String username;

    @ApiModelProperty(value = "密码")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String password;

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
