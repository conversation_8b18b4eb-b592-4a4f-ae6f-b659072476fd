package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisNodeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Table(name = "up_order_redis")
@Access(AccessType.FIELD)
public class UpOrderRedis extends UpOrderProduct<UpOrderCloudServerBean> implements IOrderEntity {

//    @JoinColumn(name = "cloud_server_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//    private SpVm vm;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
    
//    @JoinColumn(name = "vapp_template_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//    private SpVappTemplate template;
//
//    @JoinColumn(name = "vapp_template_machine_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//    private SpVappTemplateMachine templateMachine;
    
    @Column(name = "redis_type")
    @Enumerated(EnumType.STRING)
	private RedisType redisType;
    
    @Column(name = "redis_node_type")
    @Enumerated(EnumType.STRING)
    private RedisNodeType redisNodeType;
	
	@Column(name = "sharding")
	private Integer sharding;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "sp_vapp_id")
	@ManyToOne(fetch = FetchType.LAZY)
	@Where(clause = "status!='deleted'")
	private SpVapp spVapp;
	
	@Column(name = "password")
	private String password;

//	public SpVm getVm() {
//		return vm;
//	}
//
//	public void setVm(SpVm vm) {
//		this.vm = vm;
//	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}
	
//	public SpVappTemplateMachine getTemplateMachine() {
//		return templateMachine;
//	}
//
//	public void setTemplateMachine(SpVappTemplateMachine templateMachine) {
//		this.templateMachine = templateMachine;
//	}

	public RedisType getRedisType() {
		return redisType;
	}

	public void setRedisType(RedisType redisType) {
		this.redisType = redisType;
	}

	public RedisNodeType getRedisNodeType() {
		return redisNodeType;
	}

	public void setRedisNodeType(RedisNodeType redisNodeType) {
		this.redisNodeType = redisNodeType;
	}

	public Integer getSharding() {
		return sharding;
	}

	public void setSharding(Integer sharding) {
		this.sharding = sharding;
	}

//	public SpVappTemplate getTemplate() {
//		return template;
//	}
//
//	public void setTemplate(SpVappTemplate template) {
//		this.template = template;
//	}

	public SpVapp getSpVapp() {
		return spVapp;
	}

	public void setSpVapp(SpVapp spVapp) {
		this.spVapp = spVapp;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
	
}
