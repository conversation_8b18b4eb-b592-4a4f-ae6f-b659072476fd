package io.aicloudware.portal.api_rest.framework.enums;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;

public enum RestCustomQuotaProductCode {
    ecs_standard(UpProductSystemEnums.ProductType.SERVER),
    ebs_standard(UpProductSystemEnums.ProductType.STORAGE),
    eip_standard(UpProductSystemEnums.ProductType.ELASTIC_IP),
    efs_standard(UpProductSystemEnums.ProductType.FILE_STORAGE),
    oss_standard(UpProductSystemEnums.ProductType.OBJECT_STORAGE)
    ;

    private final UpProductSystemEnums.ProductType productType;

    private RestCustomQuotaProductCode(UpProductSystemEnums.ProductType productType) {
        this.productType = productType;
    }

    public UpProductSystemEnums.ProductType getProductType(){
        return this.productType;
    }

    public static Boolean isMatcher(String productCode){
        try {
            RestCustomQuotaProductCode t = RestCustomQuotaProductCode.valueOf(productCode);
            return true;
        }catch(Exception e){
            return false;
        }
    }
}

class Test{
    public static void main(String[] args) {
        System.out.println(RestCustomQuotaProductCode.isMatcher("ebs_standard500"));
    }
}