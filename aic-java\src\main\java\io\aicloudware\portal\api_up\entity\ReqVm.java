package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Set;

@Entity
@Table(name = "req_vm")
@Access(AccessType.FIELD)
public class ReqVm extends CmVm<ReqVmNetwork, ReqVmDisk> implements IRequestEntity<SpVmBean> {
    public ReqVm() {
    }

    public ReqVm(Integer id) {
        super(id);
    }

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "req_deployment_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private ReqDeployment reqDeployment;

    @Column(name = "orig_id")
    private Integer origId;

    @Column(name = "operate_type")
    @Enumerated(EnumType.STRING)
    private UpOperateType operateType;

    @Column(name = "instance_num", nullable = false)
    private Integer instanceNum;

    @Column(name = "json_init_config", length = ApiConstants.STRING_MAX_LENGTH)
    private String jsonInitConfig;

    @Column(name = "json_install_config", length = ApiConstants.STRING_MAX_LENGTH)
    private String jsonInstallConfig;

    @Column(name = "json_app_config", length = ApiConstants.STRING_MAX_LENGTH)
    private String jsonAppConfig;

    @Column(name = "json_ex1_config", length = ApiConstants.STRING_MAX_LENGTH)
    private String jsonEx1Config;

    @Column(name = "json_ex2_config", length = ApiConstants.STRING_MAX_LENGTH)
    private String jsonEx2Config;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<SpVmBean> searchBean, Set<String> aliasSet) {
        SpVmSearchBean bean = (SpVmSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getVmIdList())) {
            criteria.add(Restrictions.in("origId", bean.getVmIdList()));
        }
        if (Utility.isNotEmpty(bean.getApplicationIdList())) {
            criteria.add(Restrictions.in("application.id", bean.getApplicationIdList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public ReqDeployment getReqDeployment() {
        return reqDeployment;
    }

    public void setReqDeployment(ReqDeployment reqDeployment) {
        this.reqDeployment = reqDeployment;
    }

    public Integer getOrigId() {
        return origId;
    }

    public void setOrigId(Integer origId) {
        this.origId = origId;
    }

    public UpOperateType getOperateType() {
        return operateType;
    }

    public void setOperateType(UpOperateType operateType) {
        this.operateType = operateType;
    }

    public Integer getInstanceNum() {
        return instanceNum;
    }

    public void setInstanceNum(Integer instanceNum) {
        this.instanceNum = instanceNum;
    }

    public String getJsonInitConfig() {
        return jsonInitConfig;
    }

    public void setJsonInitConfig(String jsonInitConfig) {
        this.jsonInitConfig = jsonInitConfig;
    }

    public String getJsonInstallConfig() {
        return jsonInstallConfig;
    }

    public void setJsonInstallConfig(String jsonInstallConfig) {
        this.jsonInstallConfig = jsonInstallConfig;
    }

    public String getJsonAppConfig() {
        return jsonAppConfig;
    }

    public void setJsonAppConfig(String jsonAppConfig) {
        this.jsonAppConfig = jsonAppConfig;
    }

    public String getJsonEx1Config() {
        return jsonEx1Config;
    }

    public void setJsonEx1Config(String jsonEx1Config) {
        this.jsonEx1Config = jsonEx1Config;
    }

    public String getJsonEx2Config() {
        return jsonEx2Config;
    }

    public void setJsonEx2Config(String jsonEx2Config) {
        this.jsonEx2Config = jsonEx2Config;
    }

}
