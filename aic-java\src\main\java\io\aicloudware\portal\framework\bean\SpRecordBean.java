package io.aicloudware.portal.framework.bean;

import io.swagger.annotations.ApiModelProperty;

public abstract class SpRecordBean extends RecordBean implements IDisplayName {

    @ApiModelProperty(value = "资源租户ID", position = 40)
    private Integer spTenantId;

    @ApiModelProperty(value = "资源租户名称", position = 50)
    private String spTenantName;

    @ApiModelProperty(value = "资源租户显示名称")
    private String spTenantDisplayName;

    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    @ApiModelProperty(value = "资源区域")
    private String resourceRegion;

    @ApiModelProperty(value = "显示名称", position = 50)
    private String displayName;

    public Integer getSpTenantId() {
        return spTenantId;
    }

    public void setSpTenantId(Integer spTenantId) {
        this.spTenantId = spTenantId;
    }

    public String getSpTenantName() {
        return spTenantName;
    }

    public void setSpTenantName(String spTenantName) {
        this.spTenantName = spTenantName;
    }

    public String getSpTenantDisplayName() {
        return spTenantDisplayName;
    }

    public void setSpTenantDisplayName(String spTenantDisplayName) {
        this.spTenantDisplayName = spTenantDisplayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }
}
