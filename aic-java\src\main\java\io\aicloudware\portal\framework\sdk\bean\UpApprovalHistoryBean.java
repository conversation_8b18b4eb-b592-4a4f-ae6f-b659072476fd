package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpApprovalStatus;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "待办审批单")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpApprovalHistoryBean.class})
public class UpApprovalHistoryBean extends RecordBean {

    @ApiModelProperty(value = "业务租户ID")
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称")
    private String upTenantName;

    @ApiModelProperty(value = "应用系统ID")
    private Integer appSystemId;

    @ApiModelProperty(value = "应用系统名称")
    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    private String groupName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "申请单详情")
    private UpApplicationBean applicationBean;

    @ApiModelProperty(value = "审批流程ID")
    private Integer processId;

    @ApiModelProperty(value = "流程节点ID")
    private Integer nodeId;

    @ApiModelProperty(value = "流程节点名称")
    private String nodeName;

    @ApiModelProperty(value = "上一审批人ID")
    private Integer preApproverId;

    @ApiModelProperty(value = "上一审批人名称")
    private String preApproverName;

    @ApiModelProperty(value = "上一审批人姓名")
    private String preApproverDisplayName;

    @ApiModelProperty(value = "待审批人ID")
    private Integer pendingApproverId;

    @ApiModelProperty(value = "待审批人名称")
    private String pendingApproverName;

    @ApiModelProperty(value = "待审批人姓名")
    private String pendingApproverDisplayName;

    @ApiModelProperty(value = "实际审批人ID")
    private Integer actualApproverId;

    @ApiModelProperty(value = "实际审批人名称")
    private String actualApproverName;

    @ApiModelProperty(value = "实际审批人姓名")
    private String actualApproverDisplayName;

    @ApiModelProperty(value = "审批状态")
    private UpApprovalStatus approvalStatus;

    @ApiModelProperty(value = "操作意见")
    private String opinion;

    @ApiModelProperty(value = "审批时间")
    private Date updateTm;

    @ApiModelProperty(value = "下一节点审批人列表")
    private Integer[] nextNodeApprovers;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getNodeId() {
        return nodeId;
    }

    public void setNodeId(Integer nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public Integer getPreApproverId() {
        return preApproverId;
    }

    public void setPreApproverId(Integer preApproverId) {
        this.preApproverId = preApproverId;
    }

    public String getPreApproverName() {
        return preApproverName;
    }

    public void setPreApproverName(String preApproverName) {
        this.preApproverName = preApproverName;
    }

    public String getPreApproverDisplayName() {
        return preApproverDisplayName;
    }

    public void setPreApproverDisplayName(String preApproverDisplayName) {
        this.preApproverDisplayName = preApproverDisplayName;
    }

    public String getPendingApproverDisplayName() {
        return pendingApproverDisplayName;
    }

    public void setPendingApproverDisplayName(String pendingApproverDisplayName) {
        this.pendingApproverDisplayName = pendingApproverDisplayName;
    }

    public Integer getPendingApproverId() {
        return pendingApproverId;
    }

    public void setPendingApproverId(Integer pendingApproverId) {
        this.pendingApproverId = pendingApproverId;
    }

    public String getPendingApproverName() {
        return pendingApproverName;
    }

    public void setPendingApproverName(String pendingApproverName) {
        this.pendingApproverName = pendingApproverName;
    }

    public Integer getActualApproverId() {
        return actualApproverId;
    }

    public void setActualApproverId(Integer actualApproverId) {
        this.actualApproverId = actualApproverId;
    }

    public String getActualApproverName() {
        return actualApproverName;
    }

    public void setActualApproverName(String actualApproverName) {
        this.actualApproverName = actualApproverName;
    }

    public String getActualApproverDisplayName() {
        return actualApproverDisplayName;
    }

    public void setActualApproverDisplayName(String actualApproverDisplayName) {
        this.actualApproverDisplayName = actualApproverDisplayName;
    }

    public UpApprovalStatus getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(UpApprovalStatus approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Integer getProcessId() {
        return processId;
    }

    public void setProcessId(Integer processId) {
        this.processId = processId;
    }

    public Integer[] getNextNodeApprovers() {
        return nextNodeApprovers;
    }

    public void setNextNodeApprovers(Integer[] nextNodeApprovers) {
        this.nextNodeApprovers = nextNodeApprovers;
    }

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Date getUpdateTm() {
        return updateTm;
    }

    public void setUpdateTm(Date updateTm) {
        this.updateTm = updateTm;
    }

    public UpApplicationBean getApplicationBean() {
        return applicationBean;
    }

    public void setApplicationBean(UpApplicationBean applicationBean) {
        this.applicationBean = applicationBean;
    }

}
