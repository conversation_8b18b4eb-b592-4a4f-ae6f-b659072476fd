package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.dao.IQueryDao;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageSearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class UpVmUsageService extends BaseService implements IUpVmUsageService {

    @Autowired
    private IQueryDao queryDao;

    @Autowired
    protected ICommonService commonService;

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @Override
    public UpVmUsageResultBean getStaticsDate(UpVmUsageSearchBean searchBean) {
        StringBuilder hql = new StringBuilder()
                .append("select t.date, sum(t.cpuUsageMHz), sum(t.cpuFreeMHz), sum(t.memoryUsageM), sum(t.memoryFreeM), sum(t.diskUsageG), sum(t.diskFreeG)")
                .append(" from UpVmUsage t")
                .append(" where t.status != 'deleted'");
        Map<String, Object> paramMap = MapUtil.of(
                ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize(),
                ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
        DaoUtil.addDataScope(hql, paramMap, searchBean.getMainMenuType());
        hql.append(" group by t.date order by t.date desc");
        List<Object[]> dataList = queryDao.queryHql(hql.toString(), paramMap);
        List<UpVmUsageBean> beanList = new ArrayList<>(dataList.size());
        for (Object[] values : dataList) {
            UpVmUsageBean bean = new UpVmUsageBean();
            bean.setDate((Integer) values[0]);
            fillUsageInfo(bean, values);
            beanList.add(bean);
        }
        UpVmUsageResultBean resultBean = new UpVmUsageResultBean();
        resultBean.setDataList(beanList.toArray(new UpVmUsageBean[beanList.size()]));
        return resultBean;
    }

    @Override
    public UpVmUsageResultBean getStaticsGroup(UpVmUsageSearchBean searchBean) {
        return getStaticsData("group", searchBean);
    }

    @Override
    public UpVmUsageResultBean getStaticsAppSystem(UpVmUsageSearchBean searchBean) {
        return getStaticsData("appSystem", searchBean);
    }

    @Override
    public UpVmUsageResultBean getStaticsVm(UpVmUsageSearchBean searchBean) {
        List<UpVmUsageBean> beanList = new ArrayList<>();
        Integer date = getLastDate();
        if (Utility.isNotZero(date)) {
            StringBuilder hql = new StringBuilder()
                    .append("select t.vm, t.cpuUsageMHz, t.cpuFreeMHz, t.memoryUsageM, t.memoryFreeM, t.diskUsageG, t.diskFreeG")
                    .append(" from UpVmUsage t")
                    .append(" where t.status != 'deleted' date = " + date );
            Map<String, Object> paramMap = MapUtil.of(
                    ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize(),
                    ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
            DaoUtil.addDataScope(hql, paramMap, searchBean.getMainMenuType());
            hql.append(" order by t.vm desc");
            List<Object[]> dataList = queryDao.queryHql(hql.toString(), paramMap);
            for (Object[] values : dataList) {
                UpVmUsageBean bean = new UpVmUsageBean();
                bean.setDate((Integer) values[0]);
                fillUsageInfo(bean, values);
                beanList.add(bean);
            }
        }
        UpVmUsageResultBean resultBean = new UpVmUsageResultBean();
        resultBean.setDataList(beanList.toArray(new UpVmUsageBean[beanList.size()]));
        return resultBean;
    }

    public UpVmUsageResultBean getStaticsData(String type, UpVmUsageSearchBean searchBean) {
        List<UpVmUsageBean> beanList = new ArrayList<>();
        Integer date = getLastDate();
        if (Utility.isNotZero(date)) {
            StringBuilder hql = new StringBuilder()
                    .append("select t." + type + ", sum(t.cpuUsageMHz), sum(t.cpuFreeMHz), sum(t.memoryUsageM), sum(t.memoryFreeM), sum(t.diskUsageG), sum(t.diskFreeG)")
                    .append(" from UpVmUsage t")
                    .append(" where t.status != 'deleted' date = " + date );
            Map<String, Object> paramMap = MapUtil.of(
                    ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize(),
                    ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
            DaoUtil.addDataScope(hql, paramMap, searchBean.getMainMenuType());
            hql.append(" group by t." + type + " order by t." + type + " desc");
            List<Object[]> dataList = queryDao.queryHql(hql.toString(), paramMap);
            for (Object[] values : dataList) {
                UpVmUsageBean bean = new UpVmUsageBean();
                bean.setDate((Integer) values[0]);
                fillUsageInfo(bean, values);
                beanList.add(bean);
            }
        }
        UpVmUsageResultBean resultBean = new UpVmUsageResultBean();
        resultBean.setDataList(beanList.toArray(new UpVmUsageBean[beanList.size()]));
        return resultBean;
    }

    private Integer getLastDate() {
        String hql = "select max(date) from UpVmUsage";
        List<Integer> dateList = queryDao.queryHql(hql, null);
        return ListUtil.first(dateList);
    }

    private void fillUsageInfo(UpVmUsageBean bean, Object[] values) {
        int fromIndex = 1;
        bean.setCpuUsageMHz((Integer) values[fromIndex++]);
        bean.setCpuFreeMHz((Integer) values[fromIndex++]);
        bean.setMemoryUsageM((Integer) values[fromIndex++]);
        bean.setMemoryFreeM((Integer) values[fromIndex++]);
        bean.setDiskUsageG((Integer) values[fromIndex++]);
        bean.setDiskFreeG((Integer) values[fromIndex]);
    }
}
