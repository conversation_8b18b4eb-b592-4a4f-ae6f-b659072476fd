package io.aicloudware.portal.framework.sdk.bean;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "监控指标数据")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpMetricsDataBean extends SpRecordBean {

    @ApiModelProperty(value = "时间")
    private Date time;
    
    @ApiModelProperty(value = "值")
    private BigDecimal value;
    
    @ApiModelProperty(value = "双精度浮点值")
    private Double doubleValue;

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public BigDecimal getValue() {
		return value;
	}

	public void setValue(BigDecimal value) {
		this.value = value;
	}

	public Double getDoubleValue() {
		return doubleValue;
	}

	public void setDoubleValue(Double doubleValue) {
		this.doubleValue = doubleValue;
	}
    
    
}
