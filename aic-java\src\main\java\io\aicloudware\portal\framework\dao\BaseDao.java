package io.aicloudware.portal.framework.dao;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Example;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.jdbc.Work;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.sql.JoinType;
import org.hibernate.transform.RootEntityResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.hibernate5.HibernateCallback;
import org.springframework.orm.hibernate5.HibernateTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.entity.ISpResourceEntity;
import io.aicloudware.portal.framework.hibernate.SqlHelper;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.service.ICloudService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;

@Repository
public abstract class BaseDao implements IDao {

    protected final Logger logger = Logger.getLogger(getClass());

    @Autowired
    private SessionFactory sessionFactory;

    private HibernateTemplate hibernateTemplate = null;

    protected HibernateTemplate getHibernateTemplate() {
        if (hibernateTemplate == null) {
            hibernateTemplate = new HibernateTemplate(sessionFactory);
            hibernateTemplate.setCheckWriteOperations(false);
        }
        return hibernateTemplate;
    }

/*
    protected <E extends IEntity> EntityType<E> getEntityType(Class<E> clazz) {
        return sessionFactory.getMetamodel().entity(clazz);
    }
*/

    @Override
    public <E extends IEntity> E load(Class<E> clazz, Integer id) {
        return load(clazz, id, null);
    }

    @Override
    public <E extends IEntity> E load(Class<E> clazz, Integer id, LazyLoad<E> lazyLoad) {
        E entity = getHibernateTemplate().get(clazz, id);
        SqlHelper.outputResult(entity);
        if (entity != null) {
            if (RecordStatus.deleted.equals(entity.getStatus())) {
                return null;
            }
            if (lazyLoad != null) {
                lazyLoad.getLazyData(entity);
            }
        }
        return entity;
    }
    
    @Override
    public <E extends IEntity> E loadFullStatus(Class<E> clazz, Integer id) {
        E entity = getHibernateTemplate().get(clazz, id);
        SqlHelper.outputResult(entity);
        return entity;
    }

    @Override
    public <E extends IEntity> List<E> listIncludeDeleted(Class<E> clazz) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                return (List<E>) DetachedCriteria.forClass(clazz)
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    @Override
    public <E extends IEntity> List<E> list(Class<E> clazz) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                return (List<E>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    @Override
    public <E extends IEntity> List<E> list(Class<E> clazz, String property, Object value) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                return (List<E>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.eq(property, value))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }
    
    @Override
    public <E extends IEntity> List<E> list(Class<E> clazz, Map<String, Object> paramMap) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                return (List<E>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.allEq(paramMap))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    @Override
    public <E extends IEntity> Map<Integer, E> map(Class<E> clazz, Collection<Integer> idList) {
        if (Utility.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                DetachedCriteria criteria = DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted));
                DaoUtil.addInValues(criteria, "id", new ArrayList<>(idList));
                return (List<E>) criteria.getExecutableCriteria(session).list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.map(dataList, new ListUtil.ConvertKey<E, Integer>() {
            @Override
            public Integer getKey(E value) {
                return value.getId();
            }
        });
    }

    @Override
    public <E extends IEntity<B>, B extends RecordBean> List<E> query(SearchBean<B> searchBean, E entity) {
        return query(searchBean, entity, null);
    }

    @Override
    public <E extends IEntity<B>, B extends RecordBean> List<E> query(SearchBean<B> searchBean, E entity, LazyLoad<E> lazyLoad) {
        AssertUtil.check(searchBean, "查询时SearchBean不能为空");
        AssertUtil.check(entity, "查询时Entity不能为空");
        return getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<E>>() {
            @Override
            public List<E> doInHibernate(Session session) throws HibernateException {
                Example example = Example.create(entity).setPropertySelector(Example.NotNullPropertySelector.INSTANCE);
                DetachedCriteria criteria = DetachedCriteria.forClass(entity.getClass()).add(example);
                if (Utility.isNotZero(entity.getId())) {
                    criteria.add(Restrictions.eq("id", entity.getId()));
                }
                if (entity.getStatus() == null) {
                    criteria.add(Restrictions.ne("status", RecordStatus.deleted));
                }
//                if (entity instanceof ISpEntity && !ThreadCache.isSystemAdminLogin()) {
//                    if (ThreadCache.getUser().getOrg()!=null) {
//                    	criteria.add(Restrictions.eq("org", ThreadCache.getUser().getOrg()));
//                    } else {
//                        fillPageInfo(searchBean, 0);
//                        return Collections.emptyList();
//                    }
//                }
                if (entity instanceof IEnvironmentEntity && !ThreadCache.isSystemAdminLogin()
                        && ThreadCache.getUser() != null ) {
                    //
                }
                Set<String> aliasSet = new LinkedHashSet<>(4);
                criteria = entity.fillCriteria(criteria, searchBean, aliasSet);
                searchBean.setPageNum(Utility.toZero(searchBean.getPageNum()));

                if (Utility.isNotEmpty(aliasSet)) {
                    for (String alias : aliasSet) {
                        criteria.createAlias(alias, alias, JoinType.LEFT_OUTER_JOIN);
                    }
                }
                int recordCount = ((Long) criteria.setProjection(Projections.count("id"))
                        .getExecutableCriteria(session).uniqueResult()).intValue();
                SqlHelper.outputResult(recordCount);
                fillPageInfo(searchBean, recordCount);
                if (recordCount == 0) {
                    return Collections.emptyList();
                }

                Set<String> aliasSet2 = new LinkedHashSet<>(4);
                List<Order> orderList = fillOrderInfo(searchBean, aliasSet2);
                for (Order order : orderList) {
                    criteria.addOrder(order);
                }
                aliasSet2.removeAll(aliasSet);
                if (Utility.isNotEmpty(aliasSet2)) {
                    for (String alias : aliasSet2) {
                        criteria.createAlias(alias, alias, JoinType.LEFT_OUTER_JOIN);
                    }
                }

                List<E> dataList = (List<E>) criteria.setProjection(null)
                        .setResultTransformer(RootEntityResultTransformer.INSTANCE)
                        .getExecutableCriteria(session)
                        .setFirstResult(searchBean.getPageNum() * searchBean.getPageSize())
                        .setMaxResults(searchBean.getPageSize())
                        .list();
                if (lazyLoad != null && Utility.isNotEmpty(dataList)) {
                    for (E _entity : dataList) {
                        lazyLoad.getLazyData(_entity);
                    }
                }
                SqlHelper.outputResult(dataList);
                return dataList;
            }
        });
    }

    private void fillByUuid(IEntity entity) {
        for (Field field : Utility.describeFieldMap(entity).values()) {
            if (ISpResourceEntity.class.isAssignableFrom(Utility.getFieldType(entity.getClass(), field))) {
                ISpResourceEntity resourceEntity = (ISpResourceEntity) Utility.getFieldValue(field, entity);
                if (resourceEntity != null && Utility.isNotEmpty(resourceEntity.getSpUuid()) && Utility.isZero(resourceEntity.getId())) {
                    Utility.setFieldValue(field, entity, ICloudService.getEntityByUuid(resourceEntity.getClass(), resourceEntity.getSpOrg(), resourceEntity.getSpUuid(), false));
                }
            }
        }
    }

    @Override
    @Transactional
    public <E extends IEntity> E insert(E entity) {
        DaoUtil.cascadeChildEntity(entity, entity1 -> {
            AssertUtil.check(entity1.getId() == null, "插入时ID必须为空：" + entity1.getClass().getSimpleName() + ".id=" + entity1.getId());
            if (entity1 instanceof IDisplayName && !Utility.toEmpty(((IDisplayName) entity1).getDisplayName()).endsWith(IDisplayName.DisplayName_Suffix)) {
                ((IDisplayName) entity1).setDisplayName(entity1.getName());
            }
            fillByUuid(entity1);
            try {
                getHibernateTemplate().save(entity1);
                flush();
            } catch (RuntimeException e) {
                logger.error("insert error : " + entity1.getName(), e);
                throw e;
            }
        }, null);
        return entity;
    }

    @Override
    public <E extends IEntity> Collection<E> insert(Collection<E> entityList) {
        for (E entity : entityList) {
            insert(entity);
        }
        return entityList;
    }

    @Override
    @Transactional
    public <E extends IEntity> E update(E entity, String... names) {
        AssertUtil.check(Utility.isNotZero(entity.getId()), "更新时ID必须有值：" + entity.getClass().getSimpleName() + ".name=" + entity.getName());
        AssertUtil.check(ThreadCache.isSystemAdminLogin() || !RecordStatus.system.equals(entity.getStatus()), "不能更新系统数据");
        fillByUuid(entity);
        if (Utility.isEmpty(names)) {
            if (entity instanceof IDisplayName && !Utility.toEmpty(((IDisplayName) entity).getDisplayName()).endsWith(IDisplayName.DisplayName_Suffix)) {
                ((IDisplayName) entity).setDisplayName(entity.getName());
            }
            try {
                getHibernateTemplate().update(entity);
                flush();
            } catch (RuntimeException e) {
                logger.error("update error : " + entity.getId() + " - " + entity.getName(), e);
                throw e;
            }
        } else {
            Map<String, Field> fieldMap = Utility.describeFieldMap(entity);
            boolean hasUpdateTm = fieldMap.containsKey("updateTm");
            StringBuilder sb = new StringBuilder()
                    .append("update ")
                    .append(StringUtils.contains(entity.getClass().getSimpleName(), "_$$_")
                            ? entity.getClass().getSuperclass().getSimpleName()
                            : entity.getClass().getSimpleName())
                    .append(" t set ");
            Map<String, Object> paramMap = new HashMap<>(names.length + (hasUpdateTm ? 3 : 1));
            if (hasUpdateTm) {
                sb.append("t.updateBy=:updateBy,");
                paramMap.put("updateBy", Utility.toZero(ThreadCache.getUserId()));
                sb.append("t.updateTm=:updateTm");
                paramMap.put("updateTm", new Date());
            }
            for (String name : names) {
                if (hasUpdateTm) {
                    sb.append(",");
                } else {
                    hasUpdateTm = true;
                }
                sb.append("t.").append(name).append("=:").append(name);
                paramMap.put(name, Utility.getFieldValue(fieldMap.get(name), entity));
            }
            sb.append(" where id=:id");
            paramMap.put("id", entity.getId());
            Integer result = executeHql(sb.toString(), paramMap);
            AssertUtil.check(result == 1, "未找到需要更新的记录");
        }
        return entity;
    }

    @Override
    public <E extends IEntity> Collection<E> update(Collection<E> entityList, String... names) {
        for (E entity : entityList) {
            update(entity, names);
        }
        return entityList;
    }

    @Override
    public <E extends IEntity> boolean delete(Class<E> clazz, Integer id) {
        if (clazz.getSimpleName().contains("_$$_jvst")) {
            clazz = (Class<E>) clazz.getSuperclass();
        }
        E entity = load(clazz, id);
        entity = BeanCopyUtil.getLazyBean(entity);
        if (entity != null) {
            entity.setStatus(RecordStatus.deleted);
            update(entity, "status");
            DaoUtil.cascadeChildEntity(entity, null, entity1 -> {
                if (!RecordStatus.deleted.equals(entity1.getStatus())) {
                    delete(entity1.getClass(), entity1.getId());
                }
            });
            return true;
        } else {
            return false;
        }
    }

    @Override
    public <E extends IEntity> boolean delete(Class<E> clazz, Collection<Integer> idList) {
        boolean result = true;
        for (Integer id : idList) {
            result &= delete(clazz, id);
        }
        return result;
    }

    @Override
    public <E extends IEntity> boolean destroy(Class<E> clazz, Integer id) {
        E entity = load(clazz, id);
        if (entity != null) {
            DaoUtil.cascadeChildEntity(entity, null, entity1 -> {
                getHibernateTemplate().delete(entity);
            });
            return true;
        } else {
            return false;
        }
    }

    @Override
    public <E extends IEntity> boolean destroy(Class<E> clazz, Collection<Integer> idList) {
        boolean result = true;
        for (Integer id : idList) {
            result &= destroy(clazz, id);
        }
        return result;
    }

    @Override
    public <E extends IEntity> void evict(Collection<E> entityList) {
        for (IEntity entity : entityList) {
            getHibernateTemplate().evict(entity);
        }
    }

    @Override
    public void flush() {
        getHibernateTemplate().flush();
    }

    @Override
    public void clear() {
        getHibernateTemplate().clear();
    }

    protected <B extends RecordBean> void fillPageInfo(SearchBean<B> searchBean, int recordCount) {
        if (recordCount == 0) {
            searchBean.setRecordCount(0);
            searchBean.setPageCount(1);
            searchBean.setPageNum(0);
        } else {
            searchBean.setRecordCount(recordCount);
            searchBean.setPageCount(recordCount / searchBean.getPageSize() + (recordCount % searchBean.getPageSize() == 0 ? 0 : 1));
            searchBean.setPageNum(Math.max(0, Math.min(searchBean.getPageNum(), searchBean.getPageCount() - 1)));
        }
    }

    protected <B extends RecordBean> List<Order> fillOrderInfo(final SearchBean<B> searchBean, final Set<String> aliasSet) {
        if (Utility.isEmpty(searchBean.getOrderName1())) {
            searchBean.setOrderName1("id");
            searchBean.setOrderBy1(Boolean.FALSE);
            searchBean.setOrderName2(null);
            searchBean.setOrderBy2(null);
        }
        List<Order> orderList = new ArrayList<>(2);
        orderList.add(searchBean.getOrderBy1() ? Order.asc(searchBean.getOrderName1()) : Order.desc(searchBean.getOrderName1()));
        if (searchBean.getOrderName1().contains(".")) {
            aliasSet.add(searchBean.getOrderName1().substring(0, searchBean.getOrderName1().indexOf(".")));
        }
        if (Utility.isNotEmpty(searchBean.getOrderName2())) {
            orderList.add(searchBean.getOrderBy2() ? Order.asc(searchBean.getOrderName2()) : Order.desc(searchBean.getOrderName2()));
            if (searchBean.getOrderName2().contains(".")) {
                aliasSet.add(searchBean.getOrderName2().substring(0, searchBean.getOrderName2().indexOf(".")));
            }
        }
        return orderList;
    }

    protected <V> int executeHql(final String hql, final Map<String, V> paramMap) {
        return execute(true, hql, paramMap);
    }

    protected <V> int executeSql(final String sql, final Map<String, V> paramMap) {
        return execute(false, sql, paramMap);
    }

    private <V> int execute(final boolean isHqlOrSql, final String queryString, final Map<String, V> paramMap) {
        return getHibernateTemplate().executeWithNativeSession(session -> {
            Query query = isHqlOrSql ? session.createQuery(queryString) : session.createNativeQuery(queryString);
            fillParameter(query, paramMap);
            return query.executeUpdate();
        });
    }

    protected <E, V> List<E> queryHqlList(final String hql, final Map<String, V> paramMap) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(session -> {
            Query query = session.createQuery(hql);
            fillParameter(query, paramMap);
            return (List<E>) query.list();
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    protected <E, V> List<E> querySqlList(final String sql, final Map<String, V> paramMap) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(session -> {
            NativeQuery query = session.createNativeQuery(sql);
            fillParameter(query, paramMap);
            return (List<E>) query.list();
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    protected <E extends IEntity, V> List<E> querySqlList(final String sql, final Map<String, V> paramMap, Class<E> returnType) {
        List<E> dataList = getHibernateTemplate().executeWithNativeSession(session -> {
            NativeQuery<E> query = (NativeQuery<E>) session.createNativeQuery(sql, returnType);
            fillParameter(query, paramMap);
            return query.list();
        });
        SqlHelper.outputResult(dataList);
        return dataList;
    }

    private <V> void fillParameter(final Query query, final Map<String, V> paramMap) {
        if (Utility.isNotEmpty(paramMap)) {
            for (Map.Entry<String, V> entry : paramMap.entrySet()) {
                Object value = entry.getValue();
                if (value == null) {
                    query.setParameter(entry.getKey(), null);
                } else if (ApiConstants.QUERY_FIRST_RESULT.equals(entry.getKey())) {
                    query.setFirstResult((Integer) value);
                } else if (ApiConstants.QUERY_MAX_RESULTS.equals(entry.getKey())) {
                    query.setMaxResults((Integer) value);
                } else if (value instanceof String) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Integer) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Long) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Float) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Double) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Boolean) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Date) {
                    query.setParameter(entry.getKey(), value);
                } else if (value instanceof Collection) {
                    query.setParameterList(entry.getKey(), (Collection) value);
                } else if (value instanceof IEntity) {
                    query.setParameter(entry.getKey(), value);
                } else if (value.getClass().isEnum()) {
                    query.setParameter(entry.getKey(), entry.getValue());
                } else {
                    AssertUtil.check(null, "不支持该参数类型", entry.getValue());
                }
            }
        }
    }

    protected void executeBatch(final Work work) {
        getHibernateTemplate().executeWithNativeSession(new HibernateCallback<Void>() {
            @Override
            public Void doInHibernate(Session session) throws HibernateException {
                if (work != null) {
                    session.doWork(work);
                }
                return null;
            }
        });
    }

    @Override
    public <E extends IEntity> void validateDuplicateName(Class<E> clazz, Collection<String> nameList) {
        if (Utility.isNotEmpty(nameList)) {
            List<String> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<String>>() {
                @Override
                public List<String> doInHibernate(Session session) throws HibernateException {
                    DetachedCriteria criteria = DetachedCriteria.forClass(clazz)
                            .setProjection(Projections.distinct(Projections.property("name")))
                            .add(Restrictions.ne("status", RecordStatus.deleted));
                    DaoUtil.addInValues(criteria, "name", new ArrayList<>(nameList));
                    return (List<String>) criteria.getExecutableCriteria(session).list();
                }
            });
            SqlHelper.outputResult(dataList);
            AssertUtil.check(Utility.isEmpty(dataList), "名称重复(" + dataList.toString() + ")");
        }
    }
    
}
