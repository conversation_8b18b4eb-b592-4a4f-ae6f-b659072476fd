package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机规格")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVmSpecBean extends SpRecordBean {


    @ApiModelProperty(value = "CPU(核)")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存(GB)")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘(GB)")
    private Integer diskGB;

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }
}
