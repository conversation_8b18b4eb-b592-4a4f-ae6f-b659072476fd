package io.aicloudware.portal.api_up.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.service.ISpOrgService;

@Service
@Transactional
public class UpTenantService extends BaseTaskService implements IUpTenantService {

    @Autowired
    protected ISpOrgService orgService;

    @Autowired
    protected IUpRoleService upRoleService;

    @Autowired
    protected IUpCodeMappingService upCodeMappingService;

    @Autowired
    protected IUpApprovalService upApprovalService;

    @Override
    protected void doTaskRunning(UpTask task) {
        if (UpTaskType.up_tenant_refresh.equals(task.getType())) {
        	List<SpOrg> spOrgList = dao.list(SpOrg.class);
            for (int i = 0; i < spOrgList.size(); i++) {
                orgService.refresh(spOrgList.get(i), task);
                if (UpTaskStatus.error.equals(task.getTaskStatus())) {
                    return;
                }
            }
            task.setTaskStatus(UpTaskStatus.finish);
            task.setStatusMessage(null);
        }
    }

    @Override
    public void initDefaultData() {
        upRoleService.initDefaultDataForTenant();
        upCodeMappingService.initDefaultDataForTenant();
        upApprovalService.initDefaultDataForTenant();
    }
}
