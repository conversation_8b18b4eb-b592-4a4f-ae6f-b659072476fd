package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpBareMetalPowerStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "裸金属")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpBareMetalBean extends SpRecordBean {

	@ApiModelProperty(value = "CPU主频")
	private String cpuClockSpeedGHz;

	@ApiModelProperty(value = "CPU核数")
	private Integer cpuNum;

	@ApiModelProperty(value = "缓存")
	private Integer cpuCacheM;

	@ApiModelProperty(value = "内存")
	private Integer memoryGB;

	@ApiModelProperty(value = "存储数")
	private Integer diskNum;

	@ApiModelProperty(value = "存储大小")
	private String disk;

	@ApiModelProperty(value = "存储类型")
	private String diskType;

	@ApiModelProperty(value = "多模光口数")
	private Integer multiModeFiberNum;

	@ApiModelProperty(value = "镜像名")
	private String templateName;

	@ApiModelProperty(value = "镜像名")
	private String templateId;

	@ApiModelProperty(value = "主机名")
	private String hostName;

	@ApiModelProperty(value = "密码")
	private String password;

	@ApiModelProperty(value = "IP地址")
	private String ipAddress;

	@ApiModelProperty(value = "公网IP地址")
	private String[] publicIpAddress;

	@ApiModelProperty(value = "公网端口")
	private String[] publicPort;

	@ApiModelProperty(value = "云主机端口")
	private String[] port;

	@ApiModelProperty(value = "电源状态")
	private SpBareMetalPowerStatus powerStatus;

	@ApiModelProperty(value = "管理IP")
	private String mgmtIp;

	@ApiModelProperty(value = "设备位置")
	private String devLocation;

	@ApiModelProperty(value = "所属机柜")
	private String rack;

	@ApiModelProperty(value = "设备序列号")
	private String serialNo;

	@ApiModelProperty(value = "带外用户名")
	private String userName;

	@ApiModelProperty(value = "vpn手册")
	private String vpnLink;

	@ApiModelProperty(value = "bmc手册")
	private String bmcLink;

	public String getCpuClockSpeedGHz() {
		return cpuClockSpeedGHz;
	}

	public void setCpuClockSpeedGHz(String cpuClockSpeedGHz) {
		this.cpuClockSpeedGHz = cpuClockSpeedGHz;
	}

	public Integer getCpuNum() {
		return cpuNum;
	}

	public void setCpuNum(Integer cpuNum) {
		this.cpuNum = cpuNum;
	}

	public Integer getCpuCacheM() {
		return cpuCacheM;
	}

	public void setCpuCacheM(Integer cpuCacheM) {
		this.cpuCacheM = cpuCacheM;
	}

	public Integer getMemoryGB() {
		return memoryGB;
	}

	public void setMemoryGB(Integer memoryGB) {
		this.memoryGB = memoryGB;
	}

	public Integer getDiskNum() {
		return diskNum;
	}

	public void setDiskNum(Integer diskNum) {
		this.diskNum = diskNum;
	}

	public String getDisk() {
		return disk;
	}

	public void setDisk(String disk) {
		this.disk = disk;
	}

	public String getDiskType() {
		return diskType;
	}

	public void setDiskType(String diskType) {
		this.diskType = diskType;
	}

	public Integer getMultiModeFiberNum() {
		return multiModeFiberNum;
	}

	public void setMultiModeFiberNum(Integer multiModeFiberNum) {
		this.multiModeFiberNum = multiModeFiberNum;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String[] getPublicIpAddress() {
		return publicIpAddress;
	}

	public void setPublicIpAddress(String[] publicIpAddress) {
		this.publicIpAddress = publicIpAddress;
	}

	public String[] getPublicPort() {
		return publicPort;
	}

	public void setPublicPort(String[] publicPort) {
		this.publicPort = publicPort;
	}

	public String[] getPort() {
		return port;
	}

	public void setPort(String[] port) {
		this.port = port;
	}

	public SpBareMetalPowerStatus getPowerStatus() {
		return powerStatus;
	}

	public void setPowerStatus(SpBareMetalPowerStatus powerStatus) {
		this.powerStatus = powerStatus;
	}

	public String getMgmtIp() {
		return mgmtIp;
	}

	public void setMgmtIp(String mgmtIp) {
		this.mgmtIp = mgmtIp;
	}

	public String getDevLocation() {
		return devLocation;
	}

	public void setDevLocation(String devLocation) {
		this.devLocation = devLocation;
	}

	public String getRack() {
		return rack;
	}

	public void setRack(String rack) {
		this.rack = rack;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
}
