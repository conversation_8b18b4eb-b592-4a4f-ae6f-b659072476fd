package io.aicloudware.portal.api_vcpp.service.product;

import java.util.List;
import java.util.Map;

import io.aicloudware.portal.api_vcpp.entity.UpProductCceSet;
import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBackupSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBandwidthSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductDiskSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductLoadBalanceSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductSnapshotSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

public interface IUpProductService {

	//新建sync方法

	void sync(Integer orgId, SpRegionEntity region);

	UpProductVmSet getVmSetByCode(ProductVmSetType type, String productCode);

	List<UpProductVmSetBean> queryVmSetList(ProductVmSetType type);

	Map<ServerType,List<UpProductVmSetBean>> queryVmSet(ProductVmSetType type, SpRegionEntity region);
	
	Map<ServerType,List<UpProductVmSetBean>> queryVmSet(ProductVmSetType type, String productCode);
	
	UpProductVmSetBean getVmSetByCode(String productCode);
	
	Map<DiskType,UpProductDiskSetBean> mapDiskSet(ProductDiskSetType type);
	
	Map<DiskType, UpProductDiskSetBean> mapDiskSet(ProductDiskSetType type, String productCode);
	
	List<UpProductDiskSetBean> listDiskSet(ProductDiskSetType type);
	
	UpProductDiskSetBean getDiskSet(ProductDiskSetType type, String productCode);
	
	UpProductDiskSetBean getDiskSet(String productCode);
	
	UpProductLoadBalanceSetBean getLoadBalanceSetByOrg(Integer orgId);
	
	UpProductBandwidthSetBean getBandwidthSetByOrg(Integer orgId);
	
	UpProductBandwidthSetBean getBandwidthSet(String productCode);
	
	UpProductSnapshotSetBean getSnapshotByOrg(Integer orgId);

	UpProductBackupSetBean getBackupByOrg(Integer orgId);
	
	UpProductBackupSetBean[] getBackupSet(String productCode);

    List<UpProductCceSet> queryCceSetByOrg(Integer orgId);
}
