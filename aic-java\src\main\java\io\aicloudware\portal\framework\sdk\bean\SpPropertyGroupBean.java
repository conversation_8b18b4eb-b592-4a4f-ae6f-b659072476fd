package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "属性组")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpPropertyGroupBean extends SpRecordBean {

    @ApiModelProperty(value = "属性项列表")
    private SpPropertyItemBean[] propertyItemList;

    public SpPropertyItemBean[] getPropertyItemList() {
        return propertyItemList;
    }

    public void setPropertyItemList(SpPropertyItemBean[] propertyItemList) {
        this.propertyItemList = propertyItemList;
    }
}
