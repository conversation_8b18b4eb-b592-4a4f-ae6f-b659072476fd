package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "集群")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcClusterBean extends VcRecordBean {

    @ApiModelProperty(value = "数据中心ID")
    private Integer dataCenterId;

    @ApiModelProperty(value = "数据中心名称")
    private String dataCenterName;

    public Integer getDataCenterId() {
        return dataCenterId;
    }

    public void setDataCenterId(Integer dataCenterId) {
        this.dataCenterId = dataCenterId;
    }

    public String getDataCenterName() {
        return dataCenterName;
    }

    public void setDataCenterName(String dataCenterName) {
        this.dataCenterName = dataCenterName;
    }
}
