package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "私有镜像")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderPrivateImageBean.class})
public class UpOrderPrivateImageBean extends SpRecordBean {

	private String cloudServerUuId;

	public String getCloudServerUuId() {
		return cloudServerUuId;
	}

	public void setCloudServerUuId(String cloudServerUuId) {
		this.cloudServerUuId = cloudServerUuId;
	}
}
