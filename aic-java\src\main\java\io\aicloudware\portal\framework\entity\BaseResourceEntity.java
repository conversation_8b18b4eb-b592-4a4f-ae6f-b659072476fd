package io.aicloudware.portal.framework.entity;

import io.aicloudware.portal.framework.bean.RecordBean;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class BaseResourceEntity<B extends RecordBean> extends BaseSpEntity<B> implements ISpResourceEntity<B>, IDisplayNameEntity<B> {

    public BaseResourceEntity() {
    }

    public BaseResourceEntity(Integer id) {
        super(id);
    }

    @Column(name = "display_name")
    private String displayName;

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    @Column(name = "resource_id")
    private String resourceId;

    @Column(name = "resource_region")
    private String resourceRegion;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }
}
