package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import javax.persistence.Column;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "带宽峰值产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductBandwidthSetBean extends RecordBean {

	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "带宽配件ID")
    private Integer bandwidthProductItemId;

	@ApiModelProperty(value = "单位数")
	private Integer unit;
	
	@ApiModelProperty(value = "价格")
	private BigDecimal price;
	
	@ApiModelProperty(value = "最大值")
	private Integer maxValue;
	
	@ApiModelProperty(value = "最小值")
	private Integer minValue;
	
	@ApiModelProperty(value = "步长")
	private Integer step;
	
	@ApiModelProperty(value = "数阶")
	private Integer[] number;
	
	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;

	@Column(name = "IP数量")
	private Integer ipNumber;
	
	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getBandwidthProductItemId() {
		return bandwidthProductItemId;
	}

	public void setBandwidthProductItemId(Integer bandwidthProductItemId) {
		this.bandwidthProductItemId = bandwidthProductItemId;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}

	public Integer[] getNumber() {
		return number;
	}

	public void setNumber(Integer[] number) {
		this.number = number;
	}

	public Integer getIpNumber() {
		return ipNumber;
	}

	public void setIpNumber(Integer ipNumber) {
		this.ipNumber = ipNumber;
	}
}
