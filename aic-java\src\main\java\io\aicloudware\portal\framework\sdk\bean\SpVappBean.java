package io.aicloudware.portal.framework.sdk.bean;

import java.util.Date;

import javax.validation.GroupSequence;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpDeployStatus;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vApp")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVappBean.class})
public class SpVappBean extends SpRecordBean {

	@ApiModelProperty(value = "部署状态")
    private SpDeployStatus deployStatus;
	
    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String groupName;

//    @ApiModelProperty(value = "应用系统ID")
//    private Integer appSystemId;

//    @ApiModelProperty(value = "应用系统名称")
//    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
//    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "蓝图ID")
    private Integer blueprintId;

    @ApiModelProperty(value = "蓝图名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String blueprintName;

    @ApiModelProperty(value = "蓝图显示名称")
    private String blueprintDisplayName;

    @ApiModelProperty(value = "蓝图实例ID")
    private Integer blueprintComponentRelationId;

    @ApiModelProperty(value = "蓝图组件关系名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String blueprintComponentRelationName;

    @ApiModelProperty(value = "蓝图组件关系显示名称")
    private String blueprintComponentRelationDisplayName;

    @ApiModelProperty(value = "父部署ID")
    private Integer parentId;

    @ApiModelProperty(value = "父部署名称")
    private String parentName;

    @ApiModelProperty(value = "创建时间")
    private Date createDt;

    @ApiModelProperty(value = "到期时间（日期）")
    private Date expireDt;

    @ApiModelProperty(value = "到期时间（月数）")
    private Integer expireMonths;

    @ApiModelProperty(value = "UUID")
    private String uuid;

    @ApiModelProperty(value = "子部署列表")
    private SpVappBean[] childList;

    @ApiModelProperty(value = "虚机列表")
    private SpVmBean[] vmList;

    @ApiModelProperty(value = "XAAS表单属性列表")
    private ReqPropertyXaasBean[] propertyXaasList;

/*
    // todo : 暂不支持属性组直接绑定到蓝图，必须要绑定到蓝图机器
    @ApiModelProperty(value = "属性组列表")
    private ReqPropertyGroupBean[] propertyGroupList;
*/

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;
    
    @ApiModelProperty(value = "变更配额")
    private UpOrderQuotaDetailBean updateOrderQuotaDetail;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

//    public Integer getAppSystemId() {
//        return appSystemId;
//    }
//
//    public void setAppSystemId(Integer appSystemId) {
//        this.appSystemId = appSystemId;
//    }

//    public String getAppSystemName() {
//        return appSystemName;
//    }
//
//    public void setAppSystemName(String appSystemName) {
//        this.appSystemName = appSystemName;
//    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getBlueprintId() {
        return blueprintId;
    }

    public void setBlueprintId(Integer blueprintId) {
        this.blueprintId = blueprintId;
    }

    public String getBlueprintName() {
        return blueprintName;
    }

    public void setBlueprintName(String blueprintName) {
        this.blueprintName = blueprintName;
    }

    public Integer getBlueprintComponentRelationId() {
        return blueprintComponentRelationId;
    }

    public void setBlueprintComponentRelationId(Integer blueprintComponentRelationId) {
        this.blueprintComponentRelationId = blueprintComponentRelationId;
    }

    public String getBlueprintComponentRelationName() {
        return blueprintComponentRelationName;
    }

    public void setBlueprintComponentRelationName(String blueprintComponentRelationName) {
        this.blueprintComponentRelationName = blueprintComponentRelationName;
    }

    public String getBlueprintComponentRelationDisplayName() {
        return blueprintComponentRelationDisplayName;
    }

    public void setBlueprintComponentRelationDisplayName(String blueprintComponentRelationDisplayName) {
        this.blueprintComponentRelationDisplayName = blueprintComponentRelationDisplayName;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    public Date getExpireDt() {
        return expireDt;
    }

    public void setExpireDt(Date expireDt) {
        this.expireDt = expireDt;
    }

    public Integer getExpireMonths() {
        return expireMonths;
    }

    public void setExpireMonths(Integer expireMonths) {
        this.expireMonths = expireMonths;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public SpVappBean[] getChildList() {
        return childList;
    }

    public void setChildList(SpVappBean[] childList) {
        this.childList = childList;
    }

    public SpVmBean[] getVmList() {
        return vmList;
    }

    public void setVmList(SpVmBean[] vmList) {
        this.vmList = vmList;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public String getBlueprintDisplayName() {
        return blueprintDisplayName;
    }

    public void setBlueprintDisplayName(String blueprintDisplayName) {
        this.blueprintDisplayName = blueprintDisplayName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public ReqPropertyXaasBean[] getPropertyXaasList() {
        return propertyXaasList;
    }

    public void setPropertyXaasList(ReqPropertyXaasBean[] propertyXaasList) {
        this.propertyXaasList = propertyXaasList;
    }

	public SpDeployStatus getDeployStatus() {
		return deployStatus;
	}

	public void setDeployStatus(SpDeployStatus deployStatus) {
		this.deployStatus = deployStatus;
	}

	public UpOrderQuotaDetailBean getUpdateOrderQuotaDetail() {
		return updateOrderQuotaDetail;
	}

	public void setUpdateOrderQuotaDetail(UpOrderQuotaDetailBean updateOrderQuotaDetail) {
		this.updateOrderQuotaDetail = updateOrderQuotaDetail;
	}

/*
    public ReqPropertyGroupBean[] getPropertyGroupList() {
        return propertyGroupList;
    }

    public void setPropertyGroupList(ReqPropertyGroupBean[] propertyGroupList) {
        this.propertyGroupList = propertyGroupList;
    }
*/
    
}
