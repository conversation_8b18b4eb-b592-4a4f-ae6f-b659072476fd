package io.aicloudware.portal.framework.action;

import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.utility.Utility;

import java.lang.reflect.Type;

public class RemoteModuleAction<B extends RecordBean, S extends SearchBean<B>, R extends ResultListBean<B>> extends RemoteAction {

    protected final Type[] types = Utility.getClassTypes(getClass());

    protected RemoteModuleAction(RemoteHost remoteHost, String module) {
        super(remoteHost, module);
    }

    protected final Class<B> getModuleType() {
        return (Class<B>) types[0];
    }

    protected Class<R> getResultType() {
        return (Class<R>) types[2];
    }

    public R query(S bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/query", bean);
            return getResultData(responseBean, getResultType());
        });
    }

    public B get(Integer id) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonGet("/" + getModule() + "/get/" + id);
            return getResultData(responseBean, getModuleType());
        });
    }

    public String export(S bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/export", bean);
            return getResultData(responseBean, String.class);
        });
    }
}
