package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vCenter存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcDataStoreBean extends VcRecordBean {

    @ApiModelProperty(value = "数据中心ID")
    private Integer dataCenterId;

    @ApiModelProperty(value = "数据中心名称")
    private String dataCenterName;

    @ApiModelProperty(value = "集群ID")
    private Integer clusterId;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "主机ID")
    private Integer hostId;

    @ApiModelProperty(value = "主机名称")
    private String hostName;

    @ApiModelProperty(value = "存储总量")
    private Long diskTotalB;

    @ApiModelProperty(value = "存储使用")
    private Long diskUsageB;

    @ApiModelProperty(value = "存储剩余")
    private Long diskFreeB;

    public Integer getDataCenterId() {
        return dataCenterId;
    }

    public void setDataCenterId(Integer dataCenterId) {
        this.dataCenterId = dataCenterId;
    }

    public String getDataCenterName() {
        return dataCenterName;
    }

    public void setDataCenterName(String dataCenterName) {
        this.dataCenterName = dataCenterName;
    }

    public Integer getClusterId() {
        return clusterId;
    }

    public void setClusterId(Integer clusterId) {
        this.clusterId = clusterId;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public Integer getHostId() {
        return hostId;
    }

    public void setHostId(Integer hostId) {
        this.hostId = hostId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public Long getDiskTotalB() {
        return diskTotalB;
    }

    public void setDiskTotalB(Long diskTotalB) {
        this.diskTotalB = diskTotalB;
    }

    public Long getDiskUsageB() {
        return diskUsageB;
    }

    public void setDiskUsageB(Long diskUsageB) {
        this.diskUsageB = diskUsageB;
    }

    public Long getDiskFreeB() {
        return diskFreeB;
    }

    public void setDiskFreeB(Long diskFreeB) {
        this.diskFreeB = diskFreeB;
    }
}
