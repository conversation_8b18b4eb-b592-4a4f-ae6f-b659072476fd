package io.aicloudware.portal.framework.bean;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.HashSet;
import java.util.Set;

public abstract class BaseBean implements IBean {

    private static final ThreadLocal<Set<Object>> BeanSetLocal = new ThreadLocal<>();

    @Override
    public String toString() {
        boolean root = false;
        if (BeanSetLocal.get() == null) {
            BeanSetLocal.set(new HashSet<>());
            root = true;
        }
        try {
            return new ReflectionToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE, new StringBuffer(), BaseBean.class, true, false) {
                @Override
                public ToStringBuilder append(String fieldName, Object obj) {
                    if (obj != null && !"password".equals(fieldName)) {
                        if (obj instanceof IDProvider) {
                            if (BeanSetLocal.get().contains(obj)) {
                                return this;
                            } else {
                                BeanSetLocal.get().add(obj);
                            }
                            String data = obj.getClass().getSimpleName() + "@" + ((IDProvider) obj).getId();
                            if (BeanSetLocal.get().contains(data)) {
                                obj = ((IDProvider) obj).getId();
                            } else {
                                BeanSetLocal.get().add(data);
                            }
                        }
                        super.append(fieldName, obj);
                    }
                    return this;
                }
            }.toString();
        } finally {
            if (root) {
                BeanSetLocal.remove();
            }
        }
    }
}
