package io.aicloudware.portal.framework.sdk.bean.profile;

import java.util.Date;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpMessageStatus;
import io.aicloudware.portal.framework.sdk.contants.UpMessageType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "消息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class})
public final class UpMessageBean extends RecordBean {

	@ApiModelProperty(value = "消息所有人ID")
	private Integer userId;

	@ApiModelProperty(value = "消息类型")
	private UpMessageType type;
	
	@ApiModelProperty(value = "状态")
	private UpMessageStatus messageStatus;
	
	@ApiModelProperty(value = "内容")
	private String content;
	
	@ApiModelProperty(value = "消息时间")
	private Date messageTm;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public UpMessageType getType() {
		return type;
	}

	public void setType(UpMessageType type) {
		this.type = type;
	}

	public UpMessageStatus getMessageStatus() {
		return messageStatus;
	}

	public void setMessageStatus(UpMessageStatus messageStatus) {
		this.messageStatus = messageStatus;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getMessageTm() {
		return messageTm;
	}

	public void setMessageTm(Date messageTm) {
		this.messageTm = messageTm;
	}

}
