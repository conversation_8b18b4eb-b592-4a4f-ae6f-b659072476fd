package io.aicloudware.portal.framework.dao;

import org.springframework.stereotype.Repository;

import io.aicloudware.portal.framework.entity.IResourceEntity;
import io.aicloudware.portal.platform_vcd.entity.SpIpScope;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

@Repository
public interface ICloudDao extends IDao {

    public <T extends IResourceEntity> T loadBySpUuid(Class<T> clazz, SpOrg spTenant, String spUuid);

    public <T extends IResourceEntity> T loadBySpUuid(Class<T> clazz, String spUuid);
    
    public <T extends IResourceEntity> T loadBySpUuidFullStatus(Class<T> clazz, String spUuid);

    public SpVappTemplate loadBlueprintByCatalogItemId(SpOrg spTenant, String catalogItemId);

    public SpIpScope getNetworkProfileByName(SpOrg spTenant, String name);

}
