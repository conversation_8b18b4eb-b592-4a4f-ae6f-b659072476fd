package io.aicloudware.portal.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "批量简单操作记录")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpSimpleOperateBean.class})
public class UpSimpleOperateBean extends BaseBean {

    @ApiModelProperty(value = "批量操作对象ID列表")
    private Integer[] idList;

    @ApiModelProperty(value = "操作扩展信息")
    private String operateInfo;

    public Integer[] getIdList() {
        return idList;
    }

    public void setIdList(Integer[] idList) {
        this.idList = idList;
    }

    public String getOperateInfo() {
        return operateInfo;
    }

    public void setOperateInfo(String operateInfo) {
        this.operateInfo = operateInfo;
    }
}
