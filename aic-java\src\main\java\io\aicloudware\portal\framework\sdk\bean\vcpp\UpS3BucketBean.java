package io.aicloudware.portal.framework.sdk.bean.vcpp;

import io.swagger.annotations.ApiModelProperty;

public class UpS3BucketBean {
	private String id;
	private String name;
	
	@ApiModelProperty(value = "使用量KB")
	private Long sizekbUtilized;
	
	@ApiModelProperty(value = "创建日期")
	private String createTime;
	
	public UpS3BucketBean() {}
	
	public UpS3BucketBean(String name) {
		this.id = name;
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getSizekbUtilized() {
		return sizekbUtilized;
	}

	public void setSizekbUtilized(Long sizekbUtilized) {
		this.sizekbUtilized = sizekbUtilized;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

}