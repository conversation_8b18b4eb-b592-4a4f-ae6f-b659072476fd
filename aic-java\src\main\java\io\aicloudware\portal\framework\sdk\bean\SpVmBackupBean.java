package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import javax.validation.GroupSequence;

@ApiModel(value = "虚机备份")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVmBackupBean.class})
public class SpVmBackupBean extends SpRecordBean {

    @ApiModelProperty(value = "备份序号")
    private Integer sequence;

    @ApiModelProperty(value = "UUID")
    private String backupUuid;

    @ApiModelProperty(value = "电源状态")
    private SpVmPowerStatus powerStatus;
    
    @ApiModelProperty(value = "状态")
    private RecordStatus status;

    @ApiModelProperty(value = "云主机UUID")
    private String cloudServerUuid;
    
    @ApiModelProperty(value = "云主机")
    private String cloudServerName;
    
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTm;
    
    public SpVmPowerStatus getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(SpVmPowerStatus powerStatus) {
        this.powerStatus = powerStatus;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public String getBackupUuid() {
        return backupUuid;
    }

    public void setBackupUuid(String backupUuid) {
        this.backupUuid = backupUuid;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getCloudServerName() {
        return cloudServerName;
    }

    public void setCloudServerName(String cloudServerName) {
        this.cloudServerName = cloudServerName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getCloudServerUuid() {
        return cloudServerUuid;
    }

    public void setCloudServerUuid(String cloudServerUuid) {
        this.cloudServerUuid = cloudServerUuid;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

}
