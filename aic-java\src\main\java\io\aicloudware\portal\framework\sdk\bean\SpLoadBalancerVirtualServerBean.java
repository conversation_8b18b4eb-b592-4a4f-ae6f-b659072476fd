package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡虚拟机服务器")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerVirtualServerBean.class})
public class SpLoadBalancerVirtualServerBean extends SpRecordBean {

    @ApiModelProperty(value = "负载均衡ID")
    private Integer loadBalancerId;
    
    @ApiModelProperty(value = "启用虚拟服务器")
    private Boolean enabled;
    
    @ApiModelProperty(value = "启用加速")
    private Boolean accelerated;
    
    @ApiModelProperty(value = "描述")
    private String description;
    
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "协议")
    private String protocal;
    
    @ApiModelProperty(value = "端口")
    private String port;
    
    @ApiModelProperty(value = "默认池")
    private String defaultPool;

	@ApiModelProperty(value = "ipBindingList")
	private SpIpBindingBean[] ipBindingList;

	public Integer getLoadBalancerId() {
		return loadBalancerId;
	}

	public void setLoadBalancerId(Integer loadBalancerId) {
		this.loadBalancerId = loadBalancerId;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Boolean getAccelerated() {
		return accelerated;
	}

	public void setAccelerated(Boolean accelerated) {
		this.accelerated = accelerated;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getProtocal() {
		return protocal;
	}

	public void setProtocal(String protocal) {
		this.protocal = protocal;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getDefaultPool() {
		return defaultPool;
	}

	public void setDefaultPool(String defaultPool) {
		this.defaultPool = defaultPool;
	}

	public SpIpBindingBean[] getIpBindingList() {
		return ipBindingList;
	}

	public void setIpBindingList(SpIpBindingBean[] ipBindingList) {
		this.ipBindingList = ipBindingList;
	}
}
