package io.aicloudware.portal.api_up.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpRight;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import io.aicloudware.portal.framework.common.CommonUtil;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.executor.IExecutorAR;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmListBean;
import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightListBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;
import io.aicloudware.portal.framework.sdk.contants.UpRightType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Service
@Transactional
public class UpRightService extends BaseService implements IUpRightService {

    private static final Map<Integer, UpRightBean> SystemRightCache = new ConcurrentHashMap<>();
    private static final Map<Integer, UpRightBean> TenantRightCache = new ConcurrentHashMap<>();
    private static Map<Integer, UpRightBean> TargetRightCache = new HashMap<>();

//    private static final Map<String, Class<? extends IEntity>> TargetEntityMap = MapUtil.of(
//            Arrays.asList(UpRightType.target_reservation, UpRightType.target_category, UpRightType.target_blueprint,
//                    UpRightType.target_deployment, UpRightType.target_vm, UpRightType.target_user),
//            Arrays.asList(SpOVDC.class, SpCatalog.class, SpVappTemplate.class,
//                    SpVapp.class, SpVm.class, UpUser.class)
//    );

    @Autowired
    private IUpRelationService upRelationService;

    private Map<Integer, UpRightBean> getUpTenantTargetRightMap() {
        Map<Integer, UpRightBean> rightMap = TargetRightCache;
        if (rightMap == null) {
            rightMap = new ConcurrentHashMap<>();
            TargetRightCache=rightMap;
        }
        return rightMap;
    }

    private void initCache() {
        if (SystemRightCache.isEmpty()) {
            synchronized (getClass()) {
                if (SystemRightCache.isEmpty()) {
                    for (UpRight right : dao.list(UpRight.class)) {
                        UpRightBean bean = BeanCopyUtil.copy2Bean(right, UpRightBean.class);
                        if (right.getType().isDynamic()) {
                            getUpTenantTargetRightMap().put(right.getId(), bean);
                        } else {
                            if (right.getSystemAdmin()) {
                                SystemRightCache.put(right.getId(), bean);
                            }
                            if (right.getTenantAdmin()) {
                                TenantRightCache.put(right.getId(), bean);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void createWebMenu(UpRightListBean bean) {
        ThreadCache.setSystemAdminLogin();

        final List<UpRightType> menuTypeList = Arrays.asList(UpRightType.url_web_main_menu, UpRightType.url_web_sub_menu, UpRightType.url_web_operate);
        List<UpRightBean> clientMenuList = Arrays.asList(bean.getDataList());
        Set<Integer> targetIdSet = new HashSet<>();
        for (UpRightBean rightBean : clientMenuList) {
            AssertUtil.check(menuTypeList.contains(rightBean.getType()), "必须输入页面菜单类型：" + rightBean.getType());
            AssertUtil.check(Utility.isNotZero(rightBean.getTargetId()), "页面菜单对象ID必须存在：" + rightBean.getName());
            AssertUtil.check(!targetIdSet.contains(rightBean.getTargetId()), "页面菜单对象ID不能重复：" + rightBean.getTargetId());
            targetIdSet.add(rightBean.getTargetId());
        }
        List<UpRight> dbRightList = dao.list(UpRight.class);
        for (int i = dbRightList.size() - 1; i >= 0; i--) {
            if (!menuTypeList.contains(dbRightList.get(i).getType())) {
                dbRightList.remove(i);
            }
        }
        for (UpRight dbRight : dbRightList) {
            boolean isFound = false;
            for (UpRightBean rightBean : clientMenuList) {
                if (dbRight.getTargetId().equals(rightBean.getTargetId())) {
                    dbRight.setName(rightBean.getName());
                    dbRight.setType(rightBean.getType());
                    dbRight.setUrl(rightBean.getUrl());
                    dbRight.setSeq(rightBean.getSeq());
                    dbRight.setIcon(rightBean.getIcon());
                    dbRight.setSystemAdmin(rightBean.getSystemAdmin());
                    dbRight.setTenantAdmin(rightBean.getTenantAdmin());
                    dao.update(dbRight);
                    rightBean.setId(dbRight.getId());
                    isFound = true;
                    break;
                }
            }
            if (!isFound) {
                dao.delete(UpRight.class, dbRight.getId());
            }
        }
        for (UpRightBean rightBean : clientMenuList) {
            if (Utility.isZero(rightBean.getId())) {
                UpRight dbRight = new UpRight();
                dbRight.setName(rightBean.getName());
                dbRight.setType(rightBean.getType());
                dbRight.setTargetId(rightBean.getTargetId());
                dbRight.setUrl(rightBean.getUrl());
                dbRight.setSeq(rightBean.getSeq());
                dbRight.setIcon(rightBean.getIcon());
                dbRight.setSystemAdmin(rightBean.getSystemAdmin());
                dbRight.setTenantAdmin(rightBean.getTenantAdmin());
                dbRight.setStatus(RecordStatus.system);
                dao.insert(dbRight);
            }
        }

        clearCache();
    }

    @Override
    public void createRight(UpRightListBean bean) {
        initCache();

        for (UpRightBean rightBean : bean.getDataList()) {
            AssertUtil.check(rightBean.getType().isDynamic(), "只能创建动态类型的权限");
            AssertUtil.check(Utility.isNotZero(rightBean.getTargetId()), "权限对象ID必须存在");
            for (UpRightBean _bean : getUpTenantTargetRightMap().values()) {
                if (rightBean.getType().equals(_bean.getType())
                        && rightBean.getTargetId().equals(_bean.getTargetId())) {
                    rightBean.setId(_bean.getId());
                    break;
                }
            }
            if (Utility.isZero(rightBean.getId())) {
                UpRight right = BeanCopyUtil.copy(rightBean, UpRight.class);
                UpRightType rightType = right.getType();
//                Class<? extends IEntity> clazz = TargetEntityMap.get(rightType.getTarget());
//                AssertUtil.check(clazz != null, "不支持的权限类型：" + rightBean.getType());
//                IEntity entity = dao.load(clazz, rightBean.getTargetId());
//                right.setName(rightType.getType() + "（" + rightType.getTarget() + "：" + entity.getName() + "）");
                right.setName(rightType.getType());
                right.setSystemAdmin(Boolean.FALSE);
                right.setTenantAdmin(Boolean.FALSE);
                dao.insert(right);
                rightBean.setId(right.getId());
                getUpTenantTargetRightMap().put(right.getId(), rightBean);
            }
        }

        clearCache();
    }

    @Override
    public void deleteRight(List<Integer> rightIdList) {
        initCache();

        for (Integer id : rightIdList) {
            UpRightBean bean = getRight(id);
            AssertUtil.check(bean.getType().isDynamic(), "只能删除动态类型的权限");
            getUpTenantTargetRightMap().remove(id);
        }
        dao.delete(UpRight.class, rightIdList);

        clearCache();
        upRelationService.clearCache();
    }

    @Override
    public UpRightBean getRight(Integer rightId) {
        initCache();

        UpRightBean bean = getUpTenantTargetRightMap().get(rightId);
        if (bean == null) {
            bean = TenantRightCache.get(rightId);
        }
        if (bean == null) {
            bean = SystemRightCache.get(rightId);
        }
        return bean;
    }

    @Override
    public TreeMap<Integer, String> findRoleMap(Integer userId) {
        initCache();

        Set<UpRelationBean> relationSet = new HashSet<>();
        List<UpRelationBean> relationList = upRelationService.getUserRelationList(userId);
        for (UpRelationBean relationBean : relationList) {
            deepFindUserRelation(relationBean, relationSet);
        }
        TreeMap<Integer, String> roleMap = new TreeMap<>();
        for (UpRelationBean relationBean : relationList) {
            if (Utility.isNotZero(relationBean.getRoleId())) {
                roleMap.put(relationBean.getRoleId(), relationBean.getRoleName());
            }
            if (Utility.isNotZero(relationBean.getSubRoleId())) {
                roleMap.put(relationBean.getSubRoleId(), relationBean.getSubRoleName());
            }
        }
        return roleMap;
    }

    @Override
    public TreeMap<Integer, String> findUserMap(Integer roleId) {
        initCache();

        Set<UpRelationBean> relationSet = new HashSet<>();
        List<UpRelationBean> relationList = upRelationService.getRoleRelationList(roleId);
        for (UpRelationBean relationBean : relationList) {
            deepFindRoleRelation(relationBean, relationSet);
        }
        TreeMap<Integer, String> userMap = new TreeMap<>();
        for (UpRelationBean relationBean : relationList) {
            if (Utility.isNotZero(relationBean.getUserId())) {
                userMap.put(relationBean.getUserId(), relationBean.getUserName());
            }
        }
        return userMap;
    }

    @Override
    public List<UpRightBean> findUserRightList() {
        initCache();

        final Integer userId = Utility.toZero(ThreadCache.getUserId());
        Set<UpRelationBean> relationSet = new HashSet<>();
        Set<UpRightBean> rightSet = new HashSet<>();
        Set<UpRightBean> rightDenySet = new HashSet<>();
        List<UpRelationBean> relationList = upRelationService.getUserRelationList(userId);
        for (UpRelationBean bean : relationList) {
            deepFindRight(bean, relationSet, rightSet, rightDenySet);
        }

        if (ThreadCache.getUser().getSystemAdmin()) {
            rightSet.addAll(SystemRightCache.values());
        }
        if (ThreadCache.getUser().getTenantAdmin()) {
            rightSet.addAll(TenantRightCache.values());
        }

        rightSet.removeAll(rightDenySet);
        List<UpRightBean> rightList = new ArrayList<>(rightSet);
        rightList.sort(Comparator.comparing(bean -> Utility.toEmpty(bean.getName())));
        return rightList;
    }

    @Override
    public List<UpRightBean> findTenantRightList() {
        List<UpRightBean> rightList = new ArrayList<>(TenantRightCache.values());
        rightList.addAll(getUpTenantTargetRightMap().values());
        return rightList;
    }

    private void deepFindRight(UpRelationBean bean, Set<UpRelationBean> relationSet, Set<UpRightBean> rightSet, Set<UpRightBean> rightDenySet) {
        if (relationSet.add(bean)) {
            if (UpRelationType.user_role.equals(bean.getType())
                    || UpRelationType.group_role.equals(bean.getType())
                    || UpRelationType.role_sub_role.equals(bean.getType())) {
                Integer roleId = UpRelationType.role_sub_role.equals(bean.getType())
                        ? bean.getSubRoleId() : bean.getRoleId();
                List<UpRelationBean> relationList = upRelationService.getRoleRelationList(roleId,
                        UpRelationType.role_sub_role, UpRelationType.role_right, UpRelationType.role_right_deny);
                for (UpRelationBean _bean : relationList) {
                    if (UpRelationType.role_sub_role.equals(_bean.getType())
                            || UpRelationType.role_right.equals(_bean.getType())) {
                        deepFindRight(_bean, relationSet, rightSet, rightDenySet);
                    }
                }
            } else if (UpRelationType.user_right.equals(bean.getType())
                    || UpRelationType.group_right.equals(bean.getType())
                    || UpRelationType.role_right.equals(bean.getType())) {
                UpRightBean rightBean = getRight(bean.getRightId());
                if (rightBean != null) {
                    rightSet.add(rightBean);
                }
            } else if (UpRelationType.user_right_deny.equals(bean.getType())
                    || UpRelationType.role_right_deny.equals(bean.getType())) {
                UpRightBean rightBean = getRight(bean.getRightId());
                if (rightBean != null) {
                    rightDenySet.add(rightBean);
                }
            }
        }
    }

    private void deepFindUserRelation(UpRelationBean bean, Set<UpRelationBean> relationSet) {
        if (relationSet.add(bean)) {
            if (UpRelationType.user_role.equals(bean.getType())
                    || UpRelationType.role_sub_role.equals(bean.getType())) {
                Integer roleId = UpRelationType.role_sub_role.equals(bean.getType())
                        ? bean.getSubRoleId() : bean.getRoleId();
                List<UpRelationBean> relationList = upRelationService.getRoleRelationList(roleId, UpRelationType.role_sub_role);
                for (UpRelationBean _bean : relationList) {
                    deepFindUserRelation(_bean, relationSet);
                }
            }
        }
    }

    private void deepFindRoleRelation(UpRelationBean bean, Set<UpRelationBean> relationSet) {
        if (relationSet.add(bean)) {
            if (UpRelationType.user_group.equals(bean.getType())) {
            } else if (UpRelationType.group_role.equals(bean.getType())) {
                List<UpRelationBean> relationList = upRelationService.getGroupRelationList(bean.getGroupId(), UpRelationType.user_group);
                for (UpRelationBean _bean : relationList) {
                    deepFindRoleRelation(_bean, relationSet);
                }
            } else if (UpRelationType.role_sub_role.equals(bean.getType())) {
                List<UpRelationBean> relationList = upRelationService.getRoleRelationList(bean.getSubRoleId(), UpRelationType.role_sub_role, UpRelationType.user_role);
                for (UpRelationBean _bean : relationList) {
                    deepFindRoleRelation(_bean, relationSet);
                }
            }
        }
    }

    @Override
    public boolean checkBlueprintView(SpVappTemplateBean bean) {
        return checkRightOperation(bean, UpRightType.blueprint_view_tenant, UpRightType.blueprint_view_category, UpRightType.blueprint_view_blueprint);
    }

    @Override
    public void checkDeploymentLifecycleLow(SpSimpleOperateBean bean) {
        doCheck(SpVapp.class, bean,
                deployment -> checkRightOperation(deployment, UpRightType.op_lifecycle_low_tenant, UpRightType.op_lifecycle_low_category,
                        UpRightType.op_lifecycle_low_blueprint, UpRightType.op_lifecycle_low_app_system, UpRightType.op_lifecycle_low_deployment)
        );
    }

    @Override
    public void checkDeploymentLifecycleHigh(SpSimpleOperateBean bean) {
        doCheck(SpVapp.class, bean,
                deployment -> checkRightOperation(deployment, UpRightType.op_lifecycle_high_tenant, UpRightType.op_lifecycle_high_category,
                        UpRightType.op_lifecycle_high_blueprint, UpRightType.op_lifecycle_high_app_system, UpRightType.op_lifecycle_high_deployment)
        );
    }

    @Override
    public void checkVmPower(SpSimpleOperateBean bean) {
        doCheck(SpVm.class, bean,
                vm -> checkRightOperation(vm, UpRightType.op_power_tenant, UpRightType.op_power_reservation, UpRightType.op_power_category,
                        UpRightType.op_power_blueprint, UpRightType.op_power_app_system, UpRightType.op_power_vm)
        );
    }

    @Override
    public void checkVmSnapshot(SpSimpleOperateBean bean) {
        doCheck(SpVm.class, bean,
                vm -> checkRightOperation(vm, UpRightType.op_snapshot_tenant, UpRightType.op_snapshot_reservation, UpRightType.op_snapshot_category,
                        UpRightType.op_snapshot_blueprint, UpRightType.op_snapshot_app_system, UpRightType.op_snapshot_vm)
        );
    }

    @Override
    public void checkVmConsole(Integer vmId) {
        doCheck(SpVm.class, Arrays.asList(vmId),
                vm -> checkRightOperation(vm, UpRightType.op_console_tenant, UpRightType.op_console_reservation, UpRightType.op_console_category,
                        UpRightType.op_console_blueprint, UpRightType.op_console_app_system, UpRightType.op_console_vm)
        );
    }

    @Override
    public void checkVmChangeResource(SpVmListBean bean) {
        doCheck(SpVm.class, bean.getDataList(),
                vm -> checkRightOperation(vm, UpRightType.op_change_resource_tenant, UpRightType.op_change_resource_reservation, UpRightType.op_change_resource_category,
                        UpRightType.op_change_resource_blueprint, UpRightType.op_change_resource_app_system, UpRightType.op_change_resource_vm)
        );
    }

    @Override
    public void clearCache() {
        SystemRightCache.clear();
        TenantRightCache.clear();
        TargetRightCache.clear();
    }

    private <E extends IEntity> void doCheck(Class<E> type, List<Integer> idList, IExecutorAR<E, Boolean> executor) {
        Map<Integer, E> entityMap = dao.map(type, idList);
        for (E entity : entityMap.values()) {
            AssertUtil.check(executor.doExecute(entity), "没有权限处理：" + entity.getId() + "->" + entity.getName());
        }
    }

    private <E extends IEntity> void doCheck(Class<E> type, SpSimpleOperateBean bean, IExecutorAR<E, Boolean> executor) {
        doCheck(type, Arrays.asList(bean.getIdList()), executor);
    }

    private <E extends IEntity<B>, B extends RecordBean> void doCheck(Class<E> type, B[] beanList, IExecutorAR<E, Boolean> executor) {
        List<Integer> idList = new ArrayList<>(beanList.length);
        for (B bean : beanList) {
            idList.add(bean.getId());
        }
        doCheck(type, idList, executor);
    }

    private boolean checkRightOperation(SpVappTemplateBean blueprint, UpRightType tenantType, UpRightType categoryType, UpRightType blueprintType) {
        List<UpRightBean> rightList = findUserRightList();
        return CommonUtil.isHasRight(rightList, tenantType)
                || Utility.isNotZero(blueprint.getCategoryId()) && CommonUtil.getTargetIdList(rightList, categoryType).contains(blueprint.getCategoryId())
                || CommonUtil.getTargetIdList(rightList, blueprintType).contains(blueprint.getId());
    }

    private boolean checkRightOperation(UpRightType tenantType, UpRightType appSystemType) {
        List<UpRightBean> rightList = findUserRightList();
        return CommonUtil.isHasRight(rightList, tenantType);
    }

    private boolean checkRightOperation(SpVapp deployment, UpRightType tenantType,
                                        UpRightType categoryType, UpRightType blueprintType, UpRightType appSystemType, UpRightType deploymentType) {
        List<UpRightBean> rightList = findUserRightList();
        return CommonUtil.isHasRight(rightList, tenantType)
                || CommonUtil.getTargetIdList(rightList, categoryType).contains(deployment.getVappTemplate().getCatalog().getId())
                || CommonUtil.getTargetIdList(rightList, blueprintType).contains(deployment.getVappTemplate().getId())
                || CommonUtil.getTargetIdList(rightList, deploymentType).contains(deployment.getId());
    }

    private boolean checkRightOperation(SpVm vm, UpRightType tenantType, UpRightType reservationType,
                                        UpRightType categoryType, UpRightType blueprintType, UpRightType appSystemType, UpRightType vmType) {
        List<UpRightBean> rightList = findUserRightList();
        return CommonUtil.isHasRight(rightList, tenantType)
                || CommonUtil.getTargetIdList(rightList, reservationType).contains(vm.getReservation().getId())
                || CommonUtil.getTargetIdList(rightList, categoryType).contains(vm.getSpVapp().getVappTemplate().getCatalog().getId())
                || CommonUtil.getTargetIdList(rightList, blueprintType).contains(vm.getSpVapp().getVappTemplate().getId())
                || CommonUtil.getTargetIdList(rightList, vmType).contains(vm.getId());
    }
}
