package io.aicloudware.portal.api_vcpp.service.finance;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.action.FinanceAction;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBalanceBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBillBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBillResultBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBillSearchBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceRechargeOrderBean;
import io.aicloudware.portal.framework.sdk.contants.UpFinanceProductType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
@Transactional
public class UpFinanceRechargeService extends BaseService implements IUpFinanceRechargeService {

	private Logger log = LoggerFactory.getLogger(UpFinanceRechargeService.class);

//	@Value("${finance_uri}")
	private String uri;

//	@Value("${limit_amount}")
	private String limit_amount;

	@Value("${env}")
	private String env;
	
//	@Value("${is_finance_balance}")
	private String isFinanceBalance;

	@Override
	public UpFinanceRechargeOrderBean order(Integer userId, UpFinanceRechargeOrderBean bean) {
		AssertUtil.check(bean != null && bean.getAmount() != null && bean.getAmount().compareTo(BigDecimal.ZERO) > 0, "请输入正确的金额！");
		AssertUtil.check(bean != null && bean.getPayChannel() != null, "请选择充值渠道！");
		UpUser user = this.dao.load(UpUser.class, userId);
		FinanceAction action = new FinanceAction(new RemoteHost(uri));

		Map<String, String> params = new HashMap<>();
		params.put("amount", bean.getAmount().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).toString());
		params.put("pay_channel", bean.getPayChannel().getCode().toString());
		params.put("business_type", "1");

		String orderCallBackStr = action.getOrder(user.getUcId(), params);
		// String orderCallBackStr = action.getOrder(userId.toString(), params);
		log.info("[FINANCE-ORDER] Finance Center connection callback: " + orderCallBackStr);
		JSONObject orderJson = JSONObject.fromObject(orderCallBackStr);
		String pay_order_id = null;
		if (orderJson != null && orderJson.containsKey("errno") && orderJson.getInt("errno") == 0) {
			if (orderJson.containsKey("data")) {
				JSONObject jsonData = orderJson.getJSONObject("data");
				if (jsonData.containsKey("pay_order_id")) {
					pay_order_id = jsonData.getString("pay_order_id");
				}
			}
		}

		AssertUtil.check(pay_order_id, "创建充值订单失败！");
		String uriCallBackStr = action.getOrderUri(user.getUcId(), pay_order_id);
		log.info("[FINANCE-PAYURL] Finance connection callback: " + uriCallBackStr);
		JSONObject uriJson = JSONObject.fromObject(uriCallBackStr);
		String url = null;
		if (uriJson != null && uriJson.containsKey("errno") && uriJson.getInt("errno") == 0) {
			if (uriJson.containsKey("data")) {
				JSONObject jsonData = uriJson.getJSONObject("data");
				if (jsonData.containsKey("code_url")) {
					url = jsonData.getString("code_url");
				}
			}
		}
		AssertUtil.check(url, "创建订单支付二维码失败！");
		bean.setOrderNum(pay_order_id);
		bean.setUrl(url);
		return bean;
	}

	@Override
	public UpFinanceRechargeOrderBean check(Integer userId, UpFinanceRechargeOrderBean bean) {
		bean.setIsSuccess(false);
		AssertUtil.check(bean != null && StringUtils.isNotEmpty(bean.getOrderNum()), "订单编号不能为空！");
		UpUser user = this.dao.load(UpUser.class, userId);
		String callBackStr = new FinanceAction(new RemoteHost(uri)).getOrderStatus(user.getUcId(), bean.getOrderNum());
		log.info("[FINANCE-CHECK] Finance connection callback: " + callBackStr);
		int status = 2;
		JSONObject json = JSONObject.fromObject(callBackStr);
		if (json != null && json.containsKey("errno") && json.getInt("errno") == 0) {
			if (json.containsKey("data")) {
				JSONObject jsonData = json.getJSONObject("data");
				if (jsonData.containsKey("status")) {
					status = jsonData.getInt("status");
				}
			}
		}
		if (status == 1) {
			bean.setIsSuccess(true);
		}
		return bean;
	}

	@Override
	public UpFinanceBalanceBean balance(Integer userId) {
		UpFinanceBalanceBean bean = new UpFinanceBalanceBean(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
		bean.setLimit_amount(new BigDecimal(limit_amount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
		UpUser user = this.dao.load(UpUser.class, userId);
		AssertUtil.check(user.getUcId(), "用户为关联SSO账户！");

		try {
			String callBackStr = new FinanceAction(new RemoteHost(uri)).getBalance(user.getUcId());
			log.info("[FINANCE-CHECK] Finance connection callback: " + callBackStr);
			JSONObject json = null;
			json = JSONObject.fromObject(callBackStr);
			if (json != null && json.containsKey("errno") && json.getInt("errno") == 0) {
				if (json.containsKey("data") && json.getJSONObject("data") != null) {
					JSONObject jsonData = json.getJSONObject("data");
					if (jsonData.containsKey("cash_amount")) {
						bean.setCash_amount(BigDecimal.valueOf(jsonData.getLong("cash_amount")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
					} else {
						bean.setCash_amount(BigDecimal.ZERO);
					}

					if (jsonData.containsKey("total_amount")) {
						bean.setTotal_amount(BigDecimal.valueOf(jsonData.getLong("total_amount")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
					} else {
						bean.setTotal_amount(BigDecimal.ZERO);
					}

					if (jsonData.containsKey("unclear_amount")) {
						bean.setUnclear_amount(BigDecimal.valueOf(jsonData.getLong("unclear_amount")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
					} else {
						bean.setUnclear_amount(BigDecimal.ZERO);
					}
				} else {
					bean.setCash_amount(BigDecimal.ZERO);
					bean.setTotal_amount(BigDecimal.ZERO);
					bean.setUnclear_amount(BigDecimal.ZERO);
				}
			}
		} catch (Exception e) {
			// 开发者模式
			if ("dev".equals(env)) {
				bean.setCash_amount(BigDecimal.ZERO);
				bean.setTotal_amount(BigDecimal.ZERO);
				bean.setUnclear_amount(BigDecimal.ZERO);
				bean.setLimit_amount(BigDecimal.ZERO);
				return bean;
			}
			AssertUtil.check(false, "获取用户账户资金异常！");
			log.info("[FINANCE-ORDER] Finance Center callback format error!", e);
		}

		return bean;
	}

	@Override
	public Boolean checkBalance(Integer userId) {
//		if("yes".equals(isFinanceBalance)) {
//			UpFinanceBalanceBean bean = this.balance(userId);
//			return bean.getTotal_amount().compareTo(bean.getLimit_amount()) >= 0;
//		}else {
//			return true;
//		}
		return true;
	}

	@Override
	public UpFinanceBillResultBean list(Integer userId, UpFinanceBillSearchBean bean) {
		UpUser user = this.dao.load(UpUser.class, userId);
		String callBackStr = new FinanceAction(new RemoteHost(uri)).getBills(user.getUcId(), bean.getPageNum(), bean.getPageSize());
		log.info("[FINANCE-CHECK] Finance connection callback: " + callBackStr);
		JSONObject json = JSONObject.fromObject(callBackStr);
		UpFinanceBillResultBean result = new UpFinanceBillResultBean();
		result.setPageNum(bean.getPageNum());
		result.setPageSize(bean.getPageSize());
		if (json != null && json.containsKey("errno") && json.getInt("errno") == 0) {
			if (json.containsKey("data") && json.getJSONObject("data") != null) {
				try {
					JSONObject jsonData = json.getJSONObject("data");
					if (jsonData.containsKey("total_count") && jsonData.get("total_count") != null) {
						int recordCount = jsonData.getInt("total_count");
						result.setRecordCount(recordCount);
					}
					List<UpFinanceBillBean> list = new ArrayList<>();
					if (jsonData.containsKey("items") && jsonData.get("items") != null) {
						JSONArray items = jsonData.getJSONArray("items");
						for (int i = 0; i < items.size(); i++) {
							JSONObject item = items.getJSONObject(i);
							UpFinanceBillBean itemBean = new UpFinanceBillBean();
							if (item.containsKey("id")) {
								itemBean.setFid(item.getString("id"));
							}
							if (item.containsKey("instance_id")) {
								itemBean.setInstance_id(item.getString("instance_id"));
							}
							if (item.containsKey("product_name")) {
								itemBean.setProduct_name(item.getString("product_name"));
							}
							if (item.containsKey("product_type") && item.get("product_type") != null) {
								itemBean.setProduct_type(UpFinanceProductType.getUpFinanceProductTypeByCode(item.getString("product_type")));
							}
							if (item.containsKey("bill_amount") && item.get("bill_amount") != null) {
								itemBean.setBill_amount(new BigDecimal(item.getString("bill_amount")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
							}
							itemBean.setIsSuccess(false);
							if (item.containsKey("pay_status") && item.getInt("pay_status") == 1) {
								itemBean.setIsSuccess(true);
							}
							if (item.containsKey("bill_start_time")) {
								itemBean.setBill_start_time(item.getString("bill_start_time"));
							}
							if (item.containsKey("bill_end_time")) {
								itemBean.setBill_end_time(item.getString("bill_end_time"));
							}
							list.add(itemBean);
						}
					}
					result.setDataList(list.toArray(new UpFinanceBillBean[list.size()]));
				} catch (Exception e) {

				}
			}
		}
		return result;
	}
}
