package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "项目")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpProjectBean.class})
public class SpProjectBean extends RecordBean implements IDisplayName {

    @ApiModelProperty(value = "显示名称", position = 50)
    private String displayName;
    
    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}
