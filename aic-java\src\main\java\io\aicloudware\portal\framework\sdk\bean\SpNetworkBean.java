package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "网络(端口组)")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpNetworkBean.class})
public class SpNetworkBean extends SpRecordBean {

    @ApiModelProperty(value = "端点ID")
    private Integer endpointId;

    @ApiModelProperty(value = "端点名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String endpointName;

    @ApiModelProperty(value = "端点显示名称")
    private String endpointDisplayName;

    @ApiModelProperty(value = "计算资源ID")
    private Integer computeResourceId;

    @ApiModelProperty(value = "计算资源名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String computeResourceName;

    @ApiModelProperty(value = "计算资源显示名称")
    private String computeResourceDisplayName;

    public Integer getEndpointId() {
        return endpointId;
    }

    public void setEndpointId(Integer endpointId) {
        this.endpointId = endpointId;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public Integer getComputeResourceId() {
        return computeResourceId;
    }

    public void setComputeResourceId(Integer computeResourceId) {
        this.computeResourceId = computeResourceId;
    }

    public String getComputeResourceName() {
        return computeResourceName;
    }

    public void setComputeResourceName(String computeResourceName) {
        this.computeResourceName = computeResourceName;
    }

    public String getEndpointDisplayName() {
        return endpointDisplayName;
    }

    public void setEndpointDisplayName(String endpointDisplayName) {
        this.endpointDisplayName = endpointDisplayName;
    }

    public String getComputeResourceDisplayName() {
        return computeResourceDisplayName;
    }

    public void setComputeResourceDisplayName(String computeResourceDisplayName) {
        this.computeResourceDisplayName = computeResourceDisplayName;
    }

}
