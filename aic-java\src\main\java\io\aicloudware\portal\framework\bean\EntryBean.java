package io.aicloudware.portal.framework.bean;

public class EntryBean<T, V> extends BaseBean {
    private T key;
    private V value;

    public EntryBean() {
    }

    public EntryBean(T key, V value) {
        this.key = key;
        this.value = value;
    }

    public T getKey() {
        return key;
    }

    public void setKey(T key) {
        this.key = key;
    }

    public V getValue() {
        return value;
    }

    public void setValue(V value) {
        this.value = value;
    }
}
