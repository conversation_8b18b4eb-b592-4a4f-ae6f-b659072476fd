package io.aicloudware.portal.framework.hibernate;

import org.apache.commons.dbcp2.BasicDataSource;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

public class DataSourceWrapper implements javax.sql.DataSource {

    private final BasicDataSource dataSource;

    public DataSourceWrapper(BasicDataSource dataSource) {
        this.dataSource = dataSource;
    }

    public Boolean getDefaultAutoCommit() {
        return dataSource.getDefaultAutoCommit();
    }

    public void setMinIdle(int minIdle) {
        dataSource.setMinIdle(minIdle);
    }

    public int getMaxIdle() {
        return dataSource.getMaxIdle();
    }

    public void removeConnectionProperty(String name) {
        dataSource.removeConnectionProperty(name);
    }

    public void setJmxName(String jmxName) {
        dataSource.setJmxName(jmxName);
    }

    public void close() throws SQLException {
        dataSource.close();
    }

    public boolean getTestOnBorrow() {
        return dataSource.getTestOnBorrow();
    }

    public void setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
        dataSource.setRemoveAbandonedTimeout(removeAbandonedTimeout);
    }

    public boolean getRemoveAbandonedOnBorrow() {
        return dataSource.getRemoveAbandonedOnBorrow();
    }

    public long getMaxConnLifetimeMillis() {
        return dataSource.getMaxConnLifetimeMillis();
    }

    public void setDriverClassLoader(ClassLoader driverClassLoader) {
        dataSource.setDriverClassLoader(driverClassLoader);
    }

    public int getRemoveAbandonedTimeout() {
        return dataSource.getRemoveAbandonedTimeout();
    }

    public void setRemoveAbandonedOnBorrow(boolean removeAbandonedOnBorrow) {
        dataSource.setRemoveAbandonedOnBorrow(removeAbandonedOnBorrow);
    }

    public List<String> getConnectionInitSqls() {
        return dataSource.getConnectionInitSqls();
    }

    public void setTimeBetweenEvictionRunsMillis(long timeBetweenEvictionRunsMillis) {
        dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
    }

    public void setDriver(Driver driver) {
        dataSource.setDriver(driver);
    }

    public Driver getDriver() {
        return dataSource.getDriver();
    }

    public void setDefaultCatalog(String defaultCatalog) {
        dataSource.setDefaultCatalog(defaultCatalog);
    }

    public int getValidationQueryTimeout() {
        return dataSource.getValidationQueryTimeout();
    }

    public void preDeregister() throws Exception {
        dataSource.preDeregister();
    }

    public int getMinIdle() {
        return dataSource.getMinIdle();
    }

    public void setCacheState(boolean cacheState) {
        dataSource.setCacheState(cacheState);
    }

    public void setPassword(String password) {
        dataSource.setPassword(password);
    }

    public void setTestOnCreate(boolean testOnCreate) {
        dataSource.setTestOnCreate(testOnCreate);
    }

    public void setMaxOpenPreparedStatements(int maxOpenStatements) {
        dataSource.setMaxOpenPreparedStatements(maxOpenStatements);
    }

    public void setMaxWaitMillis(long maxWaitMillis) {
        dataSource.setMaxWaitMillis(maxWaitMillis);
    }

    public boolean getEnableAutoCommitOnReturn() {
        return dataSource.getEnableAutoCommitOnReturn();
    }

    public String getUsername() {
        return dataSource.getUsername();
    }

    public ObjectName preRegister(MBeanServer server, ObjectName name) {
        return dataSource.preRegister(server, name);
    }

    public int getNumTestsPerEvictionRun() {
        return dataSource.getNumTestsPerEvictionRun();
    }

    public void setDefaultAutoCommit(Boolean defaultAutoCommit) {
        dataSource.setDefaultAutoCommit(defaultAutoCommit);
    }

    public void setDefaultTransactionIsolation(int defaultTransactionIsolation) {
        dataSource.setDefaultTransactionIsolation(defaultTransactionIsolation);
    }

    public void setValidationQuery(String validationQuery) {
        dataSource.setValidationQuery(validationQuery);
    }

    public String getEvictionPolicyClassName() {
        return dataSource.getEvictionPolicyClassName();
    }

    public void setTestWhileIdle(boolean testWhileIdle) {
        dataSource.setTestWhileIdle(testWhileIdle);
    }

    public void setMaxConnLifetimeMillis(long maxConnLifetimeMillis) {
        dataSource.setMaxConnLifetimeMillis(maxConnLifetimeMillis);
    }

    public void setRollbackOnReturn(boolean rollbackOnReturn) {
        dataSource.setRollbackOnReturn(rollbackOnReturn);
    }

    public boolean getCacheState() {
        return dataSource.getCacheState();
    }

    public PrintWriter getAbandonedLogWriter() {
        return dataSource.getAbandonedLogWriter();
    }

    public long getSoftMinEvictableIdleTimeMillis() {
        return dataSource.getSoftMinEvictableIdleTimeMillis();
    }

    public int getDefaultTransactionIsolation() {
        return dataSource.getDefaultTransactionIsolation();
    }

    public void addConnectionProperty(String name, String value) {
        dataSource.addConnectionProperty(name, value);
    }

    public void setAbandonedUsageTracking(boolean usageTracking) {
        dataSource.setAbandonedUsageTracking(usageTracking);
    }

    public boolean getRemoveAbandonedOnMaintenance() {
        return dataSource.getRemoveAbandonedOnMaintenance();
    }

    public int getMaxOpenPreparedStatements() {
        return dataSource.getMaxOpenPreparedStatements();
    }

    public String[] getConnectionInitSqlsAsArray() {
        return dataSource.getConnectionInitSqlsAsArray();
    }

    public int getNumIdle() {
        return dataSource.getNumIdle();
    }

    public int getInitialSize() {
        return dataSource.getInitialSize();
    }

    public void setAccessToUnderlyingConnectionAllowed(boolean allow) {
        dataSource.setAccessToUnderlyingConnectionAllowed(allow);
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        dataSource.setTestOnBorrow(testOnBorrow);
    }

    public Set<String> getDisconnectionSqlCodes() {
        return dataSource.getDisconnectionSqlCodes();
    }

    public void setLifo(boolean lifo) {
        dataSource.setLifo(lifo);
    }

    public Integer getDefaultQueryTimeout() {
        return dataSource.getDefaultQueryTimeout();
    }

    public void setTestOnReturn(boolean testOnReturn) {
        dataSource.setTestOnReturn(testOnReturn);
    }

    public void setConnectionInitSqls(Collection<String> connectionInitSqls) {
        dataSource.setConnectionInitSqls(connectionInitSqls);
    }

    public boolean isClosed() {
        return dataSource.isClosed();
    }

    public boolean getLogAbandoned() {
        return dataSource.getLogAbandoned();
    }

    public String getDriverClassName() {
        return dataSource.getDriverClassName();
    }

    public void setDefaultQueryTimeout(Integer defaultQueryTimeout) {
        dataSource.setDefaultQueryTimeout(defaultQueryTimeout);
    }

    public String getPassword() {
        return dataSource.getPassword();
    }

    public void postRegister(Boolean registrationDone) {
        dataSource.postRegister(registrationDone);
    }

    public void setValidationQueryTimeout(int timeout) {
        dataSource.setValidationQueryTimeout(timeout);
    }

    public String getJmxName() {
        return dataSource.getJmxName();
    }

    public void setUsername(String username) {
        dataSource.setUsername(username);
    }

    public void setNumTestsPerEvictionRun(int numTestsPerEvictionRun) {
        dataSource.setNumTestsPerEvictionRun(numTestsPerEvictionRun);
    }

    public boolean getTestOnCreate() {
        return dataSource.getTestOnCreate();
    }

    public ClassLoader getDriverClassLoader() {
        return dataSource.getDriverClassLoader();
    }

    public void setSoftMinEvictableIdleTimeMillis(long softMinEvictableIdleTimeMillis) {
        dataSource.setSoftMinEvictableIdleTimeMillis(softMinEvictableIdleTimeMillis);
    }

    public boolean getLifo() {
        return dataSource.getLifo();
    }

    public boolean getFastFailValidation() {
        return dataSource.getFastFailValidation();
    }

    public void setPoolPreparedStatements(boolean poolingStatements) {
        dataSource.setPoolPreparedStatements(poolingStatements);
    }

    public void postDeregister() {
        dataSource.postDeregister();
    }

    public void setDriverClassName(String driverClassName) {
        dataSource.setDriverClassName(driverClassName);
    }

    public void setEvictionPolicyClassName(String evictionPolicyClassName) {
        dataSource.setEvictionPolicyClassName(evictionPolicyClassName);
    }

    public boolean isPoolPreparedStatements() {
        return dataSource.isPoolPreparedStatements();
    }

    public void setInitialSize(int initialSize) {
        dataSource.setInitialSize(initialSize);
    }

    public int getNumActive() {
        return dataSource.getNumActive();
    }

    public void setRemoveAbandonedOnMaintenance(boolean removeAbandonedOnMaintenance) {
        dataSource.setRemoveAbandonedOnMaintenance(removeAbandonedOnMaintenance);
    }

    public void setAbandonedLogWriter(PrintWriter logWriter) {
        dataSource.setAbandonedLogWriter(logWriter);
    }

    public boolean getTestOnReturn() {
        return dataSource.getTestOnReturn();
    }

    public long getMaxWaitMillis() {
        return dataSource.getMaxWaitMillis();
    }

    public boolean getLogExpiredConnections() {
        return dataSource.getLogExpiredConnections();
    }

    public String getUrl() {
        return dataSource.getUrl();
    }

    public String getValidationQuery() {
        return dataSource.getValidationQuery();
    }

    public void setLogExpiredConnections(boolean logExpiredConnections) {
        dataSource.setLogExpiredConnections(logExpiredConnections);
    }

    public void setConnectionProperties(String connectionProperties) {
        dataSource.setConnectionProperties(connectionProperties);
    }

    public String getDefaultCatalog() {
        return dataSource.getDefaultCatalog();
    }

    public boolean isAccessToUnderlyingConnectionAllowed() {
        return dataSource.isAccessToUnderlyingConnectionAllowed();
    }

    public void setUrl(String url) {
        dataSource.setUrl(url);
    }

    public void setDefaultReadOnly(Boolean defaultReadOnly) {
        dataSource.setDefaultReadOnly(defaultReadOnly);
    }

    public void setMaxIdle(int maxIdle) {
        dataSource.setMaxIdle(maxIdle);
    }

    public boolean getTestWhileIdle() {
        return dataSource.getTestWhileIdle();
    }

    public int getMaxTotal() {
        return dataSource.getMaxTotal();
    }

    public Boolean getDefaultReadOnly() {
        return dataSource.getDefaultReadOnly();
    }

    public void setMinEvictableIdleTimeMillis(long minEvictableIdleTimeMillis) {
        dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
    }

    public void setFastFailValidation(boolean fastFailValidation) {
        dataSource.setFastFailValidation(fastFailValidation);
    }

    public void setMaxTotal(int maxTotal) {
        dataSource.setMaxTotal(maxTotal);
    }

    public void setDisconnectionSqlCodes(Collection<String> disconnectionSqlCodes) {
        dataSource.setDisconnectionSqlCodes(disconnectionSqlCodes);
    }

    public void setEnableAutoCommitOnReturn(boolean enableAutoCommitOnReturn) {
        dataSource.setEnableAutoCommitOnReturn(enableAutoCommitOnReturn);
    }

    public String[] getDisconnectionSqlCodesAsArray() {
        return dataSource.getDisconnectionSqlCodesAsArray();
    }

    public boolean getRollbackOnReturn() {
        return dataSource.getRollbackOnReturn();
    }

    public void setLogAbandoned(boolean logAbandoned) {
        dataSource.setLogAbandoned(logAbandoned);
    }

    public void invalidateConnection(Connection connection) throws IllegalStateException {
        dataSource.invalidateConnection(connection);
    }

    public long getTimeBetweenEvictionRunsMillis() {
        return dataSource.getTimeBetweenEvictionRunsMillis();
    }

    public long getMinEvictableIdleTimeMillis() {
        return dataSource.getMinEvictableIdleTimeMillis();
    }

    public boolean getAbandonedUsageTracking() {
        return dataSource.getAbandonedUsageTracking();
    }

    @Override
    public Connection getConnection() throws SQLException {
        return new ConnectionWrapper(dataSource.getConnection());
    }

    @Override
    public Connection getConnection(String user, String pass) throws SQLException {
        return new ConnectionWrapper(dataSource.getConnection(user, pass));
    }

    @Override
    public int getLoginTimeout() throws SQLException {
        return dataSource.getLoginTimeout();
    }

    @Override
    public PrintWriter getLogWriter() throws SQLException {
        return dataSource.getLogWriter();
    }

    @Override
    public void setLoginTimeout(int loginTimeout) throws SQLException {
        dataSource.setLoginTimeout(loginTimeout);
    }

    @Override
    public void setLogWriter(PrintWriter logWriter) throws SQLException {
        dataSource.setLogWriter(logWriter);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return dataSource.isWrapperFor(iface);
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        return dataSource.unwrap(iface);
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return dataSource.getParentLogger();
    }
}
