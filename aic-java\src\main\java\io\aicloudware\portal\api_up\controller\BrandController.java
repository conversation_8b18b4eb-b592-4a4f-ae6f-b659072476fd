package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@RequestMapping("/brand")
public class BrandController {

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @RequestMapping(value = "/", method = RequestMethod.POST)
    @ApiOperation(notes = "/", httpMethod = "POST", value = "设定品牌")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "", response = String.class)})
    @ResponseBody
    public ResponseBean setBrand(@ApiParam(value = "查询条件") @RequestBody Map<String, String> brand)  {
        AssertUtil.check(brand, "品牌不能为空");
        if (brand.get("title") != null) {
            UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_title);
            bean.setValue(brand.get("title"));
            UpSystemConfigListBean listBean = new UpSystemConfigListBean();
            listBean.setDataList(new UpSystemConfigBean[]{bean});
            upSystemConfigService.save(listBean);
            upSystemConfigService.refreshConfig(UpSystemConfigKey.system_title);
        }
        if (brand.get("logo") != null) {
            UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_logo);
            bean.setValue(brand.get("logo"));
            UpSystemConfigListBean listBean = new UpSystemConfigListBean();
            listBean.setDataList(new UpSystemConfigBean[]{bean});
            upSystemConfigService.save(listBean);
            upSystemConfigService.refreshConfig(UpSystemConfigKey.system_logo);
        }
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/title", method = RequestMethod.GET)
    @ApiOperation(notes = "/title", httpMethod = "GET", value = "获取品牌TITLE")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean getCode()  {
        UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_title);
        if (Utility.isNotEmpty(bean.getValue())) {
            return ResponseBean.success(bean.getValue());
        }
        return ResponseBean.success("欢迎来到AI云管家");
    }

    @RequestMapping(value = "/logo", method = RequestMethod.GET)
    @ApiOperation(notes = "/logo", httpMethod = "GET", value = "获取品牌LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean getLogo()  {
        UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_logo);
        if (Utility.isNotEmpty(bean.getValue())) {
            return ResponseBean.success(bean.getValue());
        }
        return ResponseBean.success("default");
    }

    @RequestMapping(value = "/logoList", method = RequestMethod.GET)
    @ApiOperation(notes = "/logoList", httpMethod = "GET", value = "获取品牌LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean logoList()  {
        return ResponseBean.success(upSystemConfigService.logoList());
    }

    @RequestMapping(value = "/uploadLogo/full", method = RequestMethod.POST)
    @ApiOperation(notes = "/uploadLogo/full", httpMethod = "POST", value = "上传完整版LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "上传成功", response = String.class)})
    @ResponseBody
    public ResponseBean uploadFullLogo(@ApiParam(value = "LOGO文件") @RequestParam("file") MultipartFile file) {
        try {
            String result = upSystemConfigService.uploadFullLogo(file);
            return ResponseBean.success(result);
        } catch (IllegalArgumentException e) {
            return ResponseBean.error(400, "参数错误", e.getMessage());
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }

    @RequestMapping(value = "/uploadLogo/mini", method = RequestMethod.POST)
    @ApiOperation(notes = "/uploadLogo/mini", httpMethod = "POST", value = "上传迷你版LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "上传成功", response = String.class)})
    @ResponseBody
    public ResponseBean uploadMiniLogo(@ApiParam(value = "LOGO文件") @RequestParam("file") MultipartFile file) {
        try {
            String result = upSystemConfigService.uploadMiniLogo(file);
            return ResponseBean.success(result);
        } catch (IllegalArgumentException e) {
            return ResponseBean.error(400, "参数错误", e.getMessage());
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }
}
