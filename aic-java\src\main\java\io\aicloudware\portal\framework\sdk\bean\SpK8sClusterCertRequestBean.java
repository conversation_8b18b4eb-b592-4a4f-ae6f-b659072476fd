package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "K8s证书下载请求")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpK8sClusterCertRequestBean {

    @ApiModelProperty(value = "集群ID")
    private String type;

    @ApiModelProperty(value = "证书有效期")
    private Integer duration;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }
}
