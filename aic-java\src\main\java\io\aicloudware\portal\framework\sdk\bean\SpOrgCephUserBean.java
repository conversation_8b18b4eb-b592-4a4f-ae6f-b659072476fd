package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "Ceph user")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOrgCephUserBean.class})
public class SpOrgCephUserBean extends SpRecordBean {

	@ApiModelProperty(value = "accessKey")
	private String accessKey;
	
	@ApiModelProperty(value = "secretKey")
	private String secretKey;

	@ApiModelProperty(value = "totalSizeKb")
	private Long totalSizeKb;

	@ApiModelProperty(value = "totalUsagedSizeBytes")
	private Long totalUsagedSizeBytes;
	
	@ApiModelProperty(value = "公网访问")
	private String os_public_access_url;
	
	@ApiModelProperty(value = "内网访问")
	private String os_private_access_url;

	private Long backupSizeMb;

	private RecordStatus backupStatus;

	private Integer backupStatusCount;

	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;

	private SpRegionBean region;

	public String getAccessKey() {
		return accessKey;
	}

	public void setAccessKey(String accessKey) {
		this.accessKey = accessKey;
	}

	public String getSecretKey() {
		return secretKey;
	}

	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}

	public Long getTotalSizeKb() {
		return totalSizeKb;
	}

	public void setTotalSizeKb(Long totalSizeKb) {
		this.totalSizeKb = totalSizeKb;
	}

	public String getOs_public_access_url() {
		return os_public_access_url;
	}

	public void setOs_public_access_url(String os_public_access_url) {
		this.os_public_access_url = os_public_access_url;
	}

	public String getOs_private_access_url() {
		return os_private_access_url;
	}

	public void setOs_private_access_url(String os_private_access_url) {
		this.os_private_access_url = os_private_access_url;
	}

	public Long getTotalUsagedSizeBytes() {
		return totalUsagedSizeBytes;
	}

	public void setTotalUsagedSizeBytes(Long totalUsagedSizeBytes) {
		this.totalUsagedSizeBytes = totalUsagedSizeBytes;
	}

	public Long getBackupSizeMb() {
		return backupSizeMb;
	}

	public void setBackupSizeMb(Long backupSizeMb) {
		this.backupSizeMb = backupSizeMb;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public SpRegionBean getRegion() {
		return region;
	}

	public void setRegion(SpRegionBean region) {
		this.region = region;
	}

	public RecordStatus getBackupStatus() {
		return backupStatus;
	}

	public void setBackupStatus(RecordStatus backupStatus) {
		this.backupStatus = backupStatus;
	}

	public Integer getBackupStatusCount() {
		return backupStatusCount;
	}

	public void setBackupStatusCount(Integer backupStatusCount) {
		this.backupStatusCount = backupStatusCount;
	}
}
