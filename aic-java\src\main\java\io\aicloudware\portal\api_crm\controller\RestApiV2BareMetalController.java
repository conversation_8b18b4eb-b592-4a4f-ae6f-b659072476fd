package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpBareMetalBean;
import io.aicloudware.portal.framework.sdk.bean.SpBareMetalResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpBareMetalSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpBareMetal;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/api/v2/baremetal")
@Api(value = "/baremetal", description = "裸金属", position = 47)
public class RestApiV2BareMetalController extends BaseEntityController<SpBareMetal, SpBareMetalBean, SpBareMetalResultBean> {
//	@Autowired
//	private ISpBareMetalService bareMetalService;
	
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpBareMetalResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpBareMetalSearchBean searchBean) {
    	SpBareMetal entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if(StringUtils.isNotEmpty(entity.getName())) {
        	SpBareMetalBean fuzzyBean = new SpBareMetalBean();
        	fuzzyBean.setName(entity.getName());
        	entity.setName(null);
        	searchBean.setFuzzyBean(fuzzyBean);
        }
        SpBareMetalBean[] entityList = doQuery(searchBean, entity);
        ResultListBean<SpBareMetalBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);
       
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }
    

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpBareMetalBean.class)})
    @ResponseBody
    public ResponseBean getEdgeFirewall(@ApiParam(value = "对象ID") @PathVariable Integer id) {
    	// check org
    	commonService.load(SpBareMetal.class, SpBareMetalBean.class, id, ThreadCache.getOrgId());
        return getEntity(id);
    }
    
    
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.HOST, description = "删除")
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
    	// check org
    	commonService.load(SpBareMetal.class, SpBareMetalBean.class, id, ThreadCache.getOrgId());
    	return ResponseBean.success(id);
    }
    
    @RequestMapping(value = "/power_on/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_on/{id}", httpMethod = "POST", value = "虚拟机开机")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.HOST, description = "开机")
    public ResponseBean powerOn(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success("success");
    }
    
    @RequestMapping(value = "/power_off/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_off/{id}", httpMethod = "POST", value = "虚拟机关机")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.HOST, description = "关机")
    public ResponseBean powerOff(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success("success");
    }
    
    @RequestMapping(value = "/power_reboot/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_reboot/{id}", httpMethod = "POST", value = "虚拟机重启")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.HOST, description = "重启")
    public ResponseBean powerReboot(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success("success");
    }
    
    @RequestMapping(value = "/power_reset/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_reset/{id}", httpMethod = "POST", value = "虚拟机重置")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.HOST, description = "重置")
    public ResponseBean powerReset(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success("success");
    }
    
    @RequestMapping(value = "/init", method = RequestMethod.POST)
    @ApiOperation(notes = "/init", httpMethod = "POST", value = "初始化类型")
    @ResponseBody
    public ResponseBean init() {
    	Map<String,Object> datas = new HashMap<>();
    	
        
        return ResponseBean.success(datas);
    }
}
