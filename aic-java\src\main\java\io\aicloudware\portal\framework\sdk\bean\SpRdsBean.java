package io.aicloudware.portal.framework.sdk.bean;

import java.util.Date;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RdsStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RdsType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "Redis")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, SpRdsBean.class })
public class SpRdsBean extends SpRecordBean {

    private static final long serialVersionUID = -5589262931956764458L;

    @ApiModelProperty(value = "进度")
    private Integer progres;

    @ApiModelProperty(value = "付费方式")
    private PaymentType paymentType;

    @ApiModelProperty(value = "类型")
    private RdsType rdsType;

    @ApiModelProperty(value = "数据盘大小")
	private Integer diskSizeG;

    @ApiModelProperty(value = "vpcId")
    private Integer vpcId;

    @ApiModelProperty(value = "vpc名称")
    private String vpcName;

    @ApiModelProperty(value = "ovdcNetworkId")
    private Integer ovdcNetworkId;

    @ApiModelProperty(value = "ovdcNetworkName")
    private String ovdcNetworkName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "spVapp")
    private SpVappBean spVapp;

    @ApiModelProperty(value = "创建时间")
    private Date createTm;

    @ApiModelProperty(value = "运行状态")
    private RdsStatus rdsStatus;

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public Integer getVpcId() {
        return vpcId;
    }

    public void setVpcId(Integer vpcId) {
        this.vpcId = vpcId;
    }

    public String getVpcName() {
        return vpcName;
    }

    public void setVpcName(String vpcName) {
        this.vpcName = vpcName;
    }

    public Integer getOvdcNetworkId() {
        return ovdcNetworkId;
    }

    public void setOvdcNetworkId(Integer ovdcNetworkId) {
        this.ovdcNetworkId = ovdcNetworkId;
    }

    public String getOvdcNetworkName() {
        return ovdcNetworkName;
    }

    public void setOvdcNetworkName(String ovdcNetworkName) {
        this.ovdcNetworkName = ovdcNetworkName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpVappBean getSpVapp() {
        return spVapp;
    }

    public void setSpVapp(SpVappBean spVapp) {
        this.spVapp = spVapp;
    }

    public Integer getProgres() {
        return progres;
    }

    public void setProgres(Integer progres) {
        this.progres = progres;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

	public RdsStatus getRdsStatus() {
		return rdsStatus;
	}

	public void setRdsStatus(RdsStatus rdsStatus) {
		this.rdsStatus = rdsStatus;
	}

	public RdsType getRdsType() {
		return rdsType;
	}

	public void setRdsType(RdsType rdsType) {
		this.rdsType = rdsType;
	}

	public Integer getDiskSizeG() {
		return diskSizeG;
	}

	public void setDiskSizeG(Integer diskSizeG) {
		this.diskSizeG = diskSizeG;
	}


}
