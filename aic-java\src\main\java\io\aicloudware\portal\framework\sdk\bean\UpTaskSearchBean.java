package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "任务查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpTaskSearchBean extends SearchBean<UpTaskBean> {

    @ApiModelProperty(value = "任务状态列表")
    private UpTaskStatus[] taskStatusList;

    public UpTaskStatus[] getTaskStatusList() {
        return taskStatusList;
    }

    public void setTaskStatusList(UpTaskStatus[] taskStatusList) {
        this.taskStatusList = taskStatusList;
    }
}
