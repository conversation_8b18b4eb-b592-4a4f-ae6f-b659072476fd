package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpPermissionBean;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.UpPermissionType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Table(name = "up_permission")
@Access(AccessType.FIELD)
public class UpPermission extends BaseUpEntity<UpPermissionBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    @Where(clause = "status!='deleted'")
    private UpUser owner;

    @Column(name = "service")
    @Enumerated(EnumType.STRING)
    private SpService service;

    @Column(name = "url")
    private String url;

    @Column(name = "permission_type")
    @Enumerated(EnumType.STRING)
    private UpPermissionType permissionType;

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg org;

    @Column(name = "is_user_template")
    private Boolean isUserTemplate;

    @Column(name = "enabled_status")
    private Boolean enabledStatus;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public UpPermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(UpPermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public SpOrg getOrg() {
        return org;
    }

    public void setOrg(SpOrg org) {
        this.org = org;
    }

    public Boolean getUserTemplate() {
        return isUserTemplate;
    }

    public void setUserTemplate(Boolean userTemplate) {
        isUserTemplate = userTemplate;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getEnabledStatus() {
        return enabledStatus;
    }

    public void setEnabledStatus(Boolean enabledStatus) {
        this.enabledStatus = enabledStatus;
    }
}
