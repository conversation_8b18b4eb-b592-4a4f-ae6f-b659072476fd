package io.aicloudware.portal.api_up.service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.aicloudware.portal.framework.bean.PageBean;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.dao.IQueryDao;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostSearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpYearlyStartDateBean;
import io.aicloudware.portal.framework.sdk.bean.UpYearlyStartDateSetResultBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.utility.Utility;

@Service
public class UpCostService extends BaseService implements IUpCostService {

    @Autowired
    private IQueryDao queryDao;

    @Autowired
    protected ICommonService commonService;

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @Override
    public UpVmCostResultBean groupStatistic(UpVmCostSearchBean searchBean) {

        UpVmCostResultBean resultBean = new UpVmCostResultBean();

        Integer[] yearArrays = getYearList(searchBean);

        StringBuilder hql = new StringBuilder();
        Map<String, Object> map = new HashMap<>();

        // 条件查询
        assembleGroupStatisticHql(hql, map, searchBean);

        // 设置分页参数
        List<Object[]> staticList = queryDao.queryHql(hql.toString(), map);
        int recordCount = staticList.size();
        searchBean.setPageNum(Utility.toZero(searchBean.getPageNum()));
        fillPageInfo(searchBean, recordCount);
        copyPageInfo(searchBean, resultBean);

        // 分页查询
        map.put(ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize());
        map.put(ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
        List<Object[]> currentPageList = queryDao.queryHql(hql.toString(), map);

        UpVmCostBean[] dataList = new UpVmCostBean[currentPageList.size()];
        UpVmCostBean totalData = new UpVmCostBean();

        BigDecimal cpuNum = BigDecimal.ZERO;
        BigDecimal memoryGB = BigDecimal.ZERO;
        BigDecimal diskGB = BigDecimal.ZERO;
        BigDecimal cpuMoney = BigDecimal.ZERO;
        BigDecimal memoryMoney = BigDecimal.ZERO;
        BigDecimal diskMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal vmNum = BigDecimal.ZERO;

        if (Utility.isNotEmpty(currentPageList)) {
            // 统计数据
            for (int i = 0; i < currentPageList.size(); i++) {
                Object[] obj = currentPageList.get(i);
                UpVmCostBean bean = new UpVmCostBean();

                bean.setFiscalYear((Integer) obj[0]);
                bean.setGroupId((Integer) obj[1]);
                bean.setCpuNum(Long.valueOf(obj[2].toString()).intValue());
                bean.setMemoryGB(Long.valueOf(obj[3].toString()).intValue());
                bean.setDiskGB(Long.valueOf(obj[4].toString()).intValue());
                bean.setCpuMoney((Double) obj[5]);
                bean.setMemoryMoney((Double) obj[6]);
                bean.setDiskMoney((Double) obj[7]);
                bean.setTotalMoney((Double) obj[8]);
                bean.setVmNum(Long.valueOf(obj[9].toString()).intValue());

                cpuNum = cpuNum.add(new BigDecimal(bean.getCpuNum()));
                memoryGB = memoryGB.add(new BigDecimal(bean.getMemoryGB()));
                diskGB = diskGB.add(new BigDecimal(bean.getDiskGB()));
                cpuMoney = cpuMoney.add(new BigDecimal(bean.getCpuMoney().toString()));
                memoryMoney = memoryMoney.add(new BigDecimal(bean.getMemoryMoney().toString()));
                diskMoney = diskMoney.add(new BigDecimal(bean.getDiskMoney().toString()));
                totalMoney = totalMoney.add(new BigDecimal(bean.getTotalMoney().toString()));
                vmNum = vmNum.add(new BigDecimal(bean.getVmNum()));

                dataList[i] = bean;
            }

            // 合计数据
            totalData.setCpuNum(cpuNum.intValue());
            totalData.setMemoryGB(memoryGB.intValue());
            totalData.setDiskGB(diskGB.intValue());
            totalData.setCpuMoney(cpuMoney.doubleValue());
            totalData.setMemoryMoney(memoryMoney.doubleValue());
            totalData.setDiskMoney(diskMoney.doubleValue());
            totalData.setTotalMoney(totalMoney.doubleValue());
            totalData.setVmNum(vmNum.intValue());
        }

        resultBean.setFiscalYearList(yearArrays);
        resultBean.setDataList(dataList);
        resultBean.setTotalCost(totalData);

        return resultBean;
    }

    @Override
    public UpVmCostResultBean groupStatisticDetail(UpVmCostSearchBean searchBean) {

        UpVmCostResultBean resultBean = new UpVmCostResultBean();

        Integer[] yearArrays = getYearList(searchBean);

        StringBuilder hql = new StringBuilder();
        Map<String, Object> map = new HashMap<>();

        if (searchBean.getQueryYear() == null) {
            if (Utility.isEmpty(yearArrays)) {
                searchBean.setQueryYear(0);
            } else {
                searchBean.setQueryYear(yearArrays[yearArrays.length - 1]);
            }
        }
        if (searchBean.getQueryGroupId() == null) {
            searchBean.setQueryGroupId(0);
        }

        // 条件查询
        assembleGroupDetailHql(hql, map, searchBean);

        // 设置分页参数
        List<Object[]> staticList = queryDao.queryHql(hql.toString(), map);
        int recordCount = staticList.size();
        searchBean.setPageNum(Utility.toZero(searchBean.getPageNum()));
        fillPageInfo(searchBean, recordCount);
        copyPageInfo(searchBean, resultBean);

        // 分页查询
        map.put(ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize());
        map.put(ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
        List<Object[]> currentPageList = queryDao.queryHql(hql.toString(), map);

        UpVmCostBean[] dataList = new UpVmCostBean[currentPageList.size()];
        UpVmCostBean totalData = new UpVmCostBean();

        BigDecimal cpuNum = BigDecimal.ZERO;
        BigDecimal memoryGB = BigDecimal.ZERO;
        BigDecimal diskGB = BigDecimal.ZERO;
        BigDecimal cpuMoney = BigDecimal.ZERO;
        BigDecimal memoryMoney = BigDecimal.ZERO;
        BigDecimal diskMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal vmNum = BigDecimal.ZERO;

        if (Utility.isNotEmpty(currentPageList)) {
            // 统计数据
            for (int i = 0; i < currentPageList.size(); i++) {
                Object[] obj = currentPageList.get(i);
                UpVmCostBean bean = new UpVmCostBean();

                bean.setDate((Integer) obj[0]);
                bean.setCpuNum(Long.valueOf(obj[1].toString()).intValue());
                bean.setMemoryGB(Long.valueOf(obj[2].toString()).intValue());
                bean.setDiskGB(Long.valueOf(obj[3].toString()).intValue());
                bean.setCpuMoney((Double) obj[4]);
                bean.setMemoryMoney((Double) obj[5]);
                bean.setDiskMoney((Double) obj[6]);
                bean.setTotalMoney((Double) obj[7]);
                bean.setVmNum(Long.valueOf(obj[8].toString()).intValue());

                cpuNum = cpuNum.add(new BigDecimal(bean.getCpuNum()));
                memoryGB = memoryGB.add(new BigDecimal(bean.getMemoryGB()));
                diskGB = diskGB.add(new BigDecimal(bean.getDiskGB()));
                cpuMoney = cpuMoney.add(new BigDecimal(bean.getCpuMoney().toString()));
                memoryMoney = memoryMoney.add(new BigDecimal(bean.getMemoryMoney().toString()));
                diskMoney = diskMoney.add(new BigDecimal(bean.getDiskMoney().toString()));
                totalMoney = totalMoney.add(new BigDecimal(bean.getTotalMoney().toString()));
                vmNum = vmNum.add(new BigDecimal(bean.getVmNum()));

                dataList[i] = bean;
            }

            // 合计数据
            totalData.setCpuNum(cpuNum.intValue());
            totalData.setMemoryGB(memoryGB.intValue());
            totalData.setDiskGB(diskGB.intValue());
            totalData.setCpuMoney(cpuMoney.doubleValue());
            totalData.setMemoryMoney(memoryMoney.doubleValue());
            totalData.setDiskMoney(diskMoney.doubleValue());
            totalData.setTotalMoney(totalMoney.doubleValue());
            totalData.setVmNum(vmNum.intValue());
        }

        resultBean.setFiscalYearList(yearArrays);
        resultBean.setDataList(dataList);
        resultBean.setTotalCost(totalData);

        return resultBean;
    }

    @Override
    public UpVmCostResultBean appSystemStatistic(UpVmCostSearchBean searchBean) {

        UpVmCostResultBean resultBean = new UpVmCostResultBean();

        Integer[] yearArrays = getYearList(searchBean);

        StringBuilder hql = new StringBuilder();
        Map<String, Object> map = new HashMap<>();

        // 条件查询
        assembleAppSystemStatisticHql(hql, map, searchBean);

        // 设置分页参数
        List<Object[]> staticList = queryDao.queryHql(hql.toString(), map);
        int recordCount = staticList.size();
        searchBean.setPageNum(Utility.toZero(searchBean.getPageNum()));
        fillPageInfo(searchBean, recordCount);
        copyPageInfo(searchBean, resultBean);

        // 分页查询
        map.put(ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize());
        map.put(ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
        List<Object[]> currentPageList = queryDao.queryHql(hql.toString(), map);

        UpVmCostBean[] dataList = new UpVmCostBean[currentPageList.size()];
        UpVmCostBean totalData = new UpVmCostBean();

        BigDecimal cpuNum = BigDecimal.ZERO;
        BigDecimal memoryGB = BigDecimal.ZERO;
        BigDecimal diskGB = BigDecimal.ZERO;
        BigDecimal cpuMoney = BigDecimal.ZERO;
        BigDecimal memoryMoney = BigDecimal.ZERO;
        BigDecimal diskMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal vmNum = BigDecimal.ZERO;

        if (Utility.isNotEmpty(currentPageList)) {
            // 统计数据
            for (int i = 0; i < currentPageList.size(); i++) {
                Object[] obj = currentPageList.get(i);
                UpVmCostBean bean = new UpVmCostBean();

                bean.setFiscalYear((Integer) obj[0]);
                bean.setGroupId((Integer) obj[1]);
                bean.setAppSystemId((Integer) obj[2]);
                bean.setCpuNum(Long.valueOf(obj[3].toString()).intValue());
                bean.setMemoryGB(Long.valueOf(obj[4].toString()).intValue());
                bean.setDiskGB(Long.valueOf(obj[5].toString()).intValue());
                bean.setCpuMoney((Double) obj[6]);
                bean.setMemoryMoney((Double) obj[7]);
                bean.setDiskMoney((Double) obj[8]);
                bean.setTotalMoney((Double) obj[9]);
                bean.setVmNum(Long.valueOf(obj[10].toString()).intValue());

                cpuNum = cpuNum.add(new BigDecimal(bean.getCpuNum()));
                memoryGB = memoryGB.add(new BigDecimal(bean.getMemoryGB()));
                diskGB = diskGB.add(new BigDecimal(bean.getDiskGB()));
                cpuMoney = cpuMoney.add(new BigDecimal(bean.getCpuMoney().toString()));
                memoryMoney = memoryMoney.add(new BigDecimal(bean.getMemoryMoney().toString()));
                diskMoney = diskMoney.add(new BigDecimal(bean.getDiskMoney().toString()));
                totalMoney = totalMoney.add(new BigDecimal(bean.getTotalMoney().toString()));
                vmNum = vmNum.add(new BigDecimal(bean.getVmNum()));

                dataList[i] = bean;
            }

            // 合计数据
            totalData.setCpuNum(cpuNum.intValue());
            totalData.setMemoryGB(memoryGB.intValue());
            totalData.setDiskGB(diskGB.intValue());
            totalData.setCpuMoney(cpuMoney.doubleValue());
            totalData.setMemoryMoney(memoryMoney.doubleValue());
            totalData.setDiskMoney(diskMoney.doubleValue());
            totalData.setTotalMoney(totalMoney.doubleValue());
            totalData.setVmNum(vmNum.intValue());
        }

        resultBean.setFiscalYearList(yearArrays);
        resultBean.setDataList(dataList);
        resultBean.setTotalCost(totalData);

        return resultBean;
    }

    @Override
    public UpVmCostResultBean appSystemStatisticDetail(UpVmCostSearchBean searchBean) {

        UpVmCostResultBean resultBean = new UpVmCostResultBean();

        Integer[] yearArrays = getYearList(searchBean);

        StringBuilder hql = new StringBuilder();
        Map<String, Object> map = new HashMap<>();

        // 点击详情菜单默认查询最后一年的数据
        if (searchBean.getQueryYear() == null) {
            if (Utility.isEmpty(yearArrays)) {
                searchBean.setQueryYear(0);
            } else {
                searchBean.setQueryYear(yearArrays[yearArrays.length - 1]);
            }
        }
        // 点击详情菜单默认查询首个应用系统的数据
        if (searchBean.getQueryAppSystemId() == null) {
            searchBean.setQueryAppSystemId(0);
        }

        // 条件查询
        assembleAppSystemDetailHql(hql, map, searchBean);

        // 设置分页参数
        List<Object[]> staticList = queryDao.queryHql(hql.toString(), map);
        int recordCount = staticList.size();
        searchBean.setPageNum(Utility.toZero(searchBean.getPageNum()));
        fillPageInfo(searchBean, recordCount);
        copyPageInfo(searchBean, resultBean);

        // 分页查询
        map.put(ApiConstants.QUERY_FIRST_RESULT, searchBean.getPageNum() * searchBean.getPageSize());
        map.put(ApiConstants.QUERY_MAX_RESULTS, searchBean.getPageSize());
        List<Object[]> currentPageList = queryDao.queryHql(hql.toString(), map);

        UpVmCostBean[] dataList = new UpVmCostBean[currentPageList.size()];
        UpVmCostBean totalData = new UpVmCostBean();

        BigDecimal cpuNum = BigDecimal.ZERO;
        BigDecimal memoryGB = BigDecimal.ZERO;
        BigDecimal diskGB = BigDecimal.ZERO;
        BigDecimal cpuMoney = BigDecimal.ZERO;
        BigDecimal memoryMoney = BigDecimal.ZERO;
        BigDecimal diskMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal vmNum = BigDecimal.ZERO;

        if (Utility.isNotEmpty(currentPageList)) {
            // 统计数据
            for (int i = 0; i < currentPageList.size(); i++) {
                Object[] obj = currentPageList.get(i);
                UpVmCostBean bean = new UpVmCostBean();

                bean.setDate((Integer) obj[0]);
                bean.setCpuNum(Long.valueOf(obj[1].toString()).intValue());
                bean.setMemoryGB(Long.valueOf(obj[2].toString()).intValue());
                bean.setDiskGB(Long.valueOf(obj[3].toString()).intValue());
                bean.setCpuMoney((Double) obj[4]);
                bean.setMemoryMoney((Double) obj[5]);
                bean.setDiskMoney((Double) obj[6]);
                bean.setTotalMoney((Double) obj[7]);
                bean.setVmNum(Long.valueOf(obj[8].toString()).intValue());

                cpuNum = cpuNum.add(new BigDecimal(bean.getCpuNum()));
                memoryGB = memoryGB.add(new BigDecimal(bean.getMemoryGB()));
                diskGB = diskGB.add(new BigDecimal(bean.getDiskGB()));
                cpuMoney = cpuMoney.add(new BigDecimal(bean.getCpuMoney().toString()));
                memoryMoney = memoryMoney.add(new BigDecimal(bean.getMemoryMoney().toString()));
                diskMoney = diskMoney.add(new BigDecimal(bean.getDiskMoney().toString()));
                totalMoney = totalMoney.add(new BigDecimal(bean.getTotalMoney().toString()));
                vmNum = vmNum.add(new BigDecimal(bean.getVmNum()));

                dataList[i] = bean;
            }

            // 合计数据
            totalData.setCpuNum(cpuNum.intValue());
            totalData.setMemoryGB(memoryGB.intValue());
            totalData.setDiskGB(diskGB.intValue());
            totalData.setCpuMoney(cpuMoney.doubleValue());
            totalData.setMemoryMoney(memoryMoney.doubleValue());
            totalData.setDiskMoney(diskMoney.doubleValue());
            totalData.setTotalMoney(totalMoney.doubleValue());
            totalData.setVmNum(vmNum.intValue());
        }

        resultBean.setFiscalYearList(yearArrays);
        resultBean.setDataList(dataList);
        resultBean.setTotalCost(totalData);

        return resultBean;
    }

    @Override
    public UpVmCostResultBean query(UpVmCostResultBean resultBean, UpVmCostSearchBean searchBean) {

        Integer[] yearArrays = getYearList(searchBean);
        UpVmCostBean[] dataList = resultBean.getDataList();
        UpVmCostBean totalData = new UpVmCostBean();

        BigDecimal cpuNum = BigDecimal.ZERO;
        BigDecimal memoryGB = BigDecimal.ZERO;
        BigDecimal diskGB = BigDecimal.ZERO;
        BigDecimal cpuMoney = BigDecimal.ZERO;
        BigDecimal memoryMoney = BigDecimal.ZERO;
        BigDecimal diskMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;

        if (Utility.isNotEmpty(dataList)) {
            // 统计数据
            for (UpVmCostBean bean : dataList) {
                cpuNum = cpuNum.add(new BigDecimal(bean.getCpuNum()));
                memoryGB = memoryGB.add(new BigDecimal(bean.getMemoryGB()));
                diskGB = diskGB.add(new BigDecimal(bean.getDiskGB()));
                cpuMoney = cpuMoney.add(new BigDecimal(bean.getCpuMoney().toString()));
                memoryMoney = memoryMoney.add(new BigDecimal(bean.getMemoryMoney().toString()));
                diskMoney = diskMoney.add(new BigDecimal(bean.getDiskMoney().toString()));
                totalMoney = totalMoney.add(new BigDecimal(bean.getTotalMoney().toString()));
            }

            // 合计数据
            totalData.setCpuNum(cpuNum.intValue());
            totalData.setMemoryGB(memoryGB.intValue());
            totalData.setDiskGB(diskGB.intValue());
            totalData.setCpuMoney(cpuMoney.doubleValue());
            totalData.setMemoryMoney(memoryMoney.doubleValue());
            totalData.setDiskMoney(diskMoney.doubleValue());
            totalData.setTotalMoney(totalMoney.doubleValue());
        }


        resultBean.setFiscalYearList(yearArrays);
        resultBean.setTotalCost(totalData);

        return resultBean;
    }

    @Override
    public UpYearlyStartDateSetResultBean yearlyStartdateSet(UpYearlyStartDateBean bean) {

        UpSystemConfigBean oldBean = upSystemConfigService.get(UpSystemConfigKey.yearly_start_date);

        String oldValue = oldBean.getValue();

        final int year = Calendar.getInstance().get(Calendar.YEAR);

        String yearly_start_date = bean.getYearlyStartDate();

        if (Utility.isNotEmpty(yearly_start_date)) {
            AssertUtil.check(yearly_start_date.startsWith(String.valueOf(year))
                    || yearly_start_date.startsWith(String.valueOf(year - 1)), "请选择正确的本年度" + year + "起始日期");

            int yearOffset = -1;

            if (yearly_start_date.startsWith(String.valueOf(year))) {
                yearOffset = 0;
            } else if (yearly_start_date.startsWith(String.valueOf(year - 1))) {
                yearOffset = 1;
            }

            String newValue = yearOffset + ":" + yearly_start_date.substring(5);

            if (!newValue.equals(oldValue)) {
                UpSystemConfigBean newBean = new UpSystemConfigBean();
                newBean.setKey(UpSystemConfigKey.yearly_start_date);
                newBean.setName(UpSystemConfigKey.yearly_start_date.toString());
                newBean.setValue(newValue);

                UpSystemConfigListBean newListBean = new UpSystemConfigListBean();
                newListBean.setDataList(new UpSystemConfigBean[]{newBean});
                upSystemConfigService.save(newListBean);

                oldValue = newValue;
                updateCostFiscalYear(newValue);
            }
        }

        if (Utility.isEmpty(oldValue)) {
            yearly_start_date = year + "-01-01";
        } else {
            yearly_start_date = (year - Integer.parseInt(oldValue.split(":")[0])) + "-" + oldValue.split(":")[1];
        }

        UpYearlyStartDateSetResultBean rsltBean = new UpYearlyStartDateSetResultBean();

        rsltBean.setYearlyStartDate(yearly_start_date);
        rsltBean.setMaxYearlyStartDate(year + "-06-30");
        rsltBean.setMinYearlyStartDate((year - 1) + "-07-01");
        rsltBean.setYear(String.valueOf(year));

        return rsltBean;
    }

    private void updateCostFiscalYear(String new_yearly_start_date) {

        List<Integer> staticsDtList = queryDao.queryHql(
                "select distinct t.date from UpVmCost t where t.status != 'deleted'",null);

        for (Integer date : staticsDtList) {
            queryDao.doExecuteHql(
                    "update UpVmCost t set t.fiscalYear = :fiscalYear where t.status != 'deleted' and t.date = :date",
                    MapUtil.of("date", date, "fiscalYear",
                            getFiscalYear(FormatUtil.formatDate(date), new_yearly_start_date)));
        }
    }

    private Integer getFiscalYear(String date, String new_yearly_start_date) {
        int year = Integer.valueOf(date.substring(0, 4));
        if (date.substring(5).compareTo(new_yearly_start_date.substring(2)) < 0) {
            year--;
        }
        if (new_yearly_start_date.startsWith("1")) {
            year++;
        }
        return year;
    }

    private void assembleGroupStatisticHql(StringBuilder hql, Map<String, Object> map, UpVmCostSearchBean searchBean) {

        hql.append("select t.fiscalYear, t.group.id, "
                + "sum(t.cpuNum), sum(t.memoryGB), sum(t.diskGB), "
                + "sum(t.cpuMoney), sum(t.memoryMoney), sum(t.diskMoney), "
                + "sum(t.totalMoney), count(t.id) "
                + "from UpVmCost t " + "where t.status != 'deleted' ");

        if (null != searchBean.getQueryYear()) {
            hql.append("and t.fiscalYear = :fiscalYear ");
            map.put("fiscalYear", searchBean.getQueryYear());
        }
        if (null != searchBean.getQueryGroupId()) {
            hql.append("and t.group.id = :groupId ");
            map.put("groupId", searchBean.getQueryGroupId());
        }
        if (Utility.isNotEmpty(searchBean.getStartDt())) {
            hql.append("and t.date >= :startDt ");
            map.put("startDt", FormatUtil.formatDate(searchBean.getStartDt()));
        }
        if (Utility.isNotEmpty(searchBean.getEndDt())) {
            hql.append("and t.date <= :endDt ");
            map.put("endDt", FormatUtil.formatDate(searchBean.getEndDt()));
        }

        DaoUtil.addDataScope(hql, map, searchBean.getMainMenuType());

        hql.append("and t.upTenant.id = :tenantId "
                + "group by t.fiscalYear, t.group.id "
                + "order by t.fiscalYear desc, t.group.id");
    }

    private void assembleGroupDetailHql(StringBuilder hql, Map<String, Object> map, UpVmCostSearchBean searchBean) {

        hql.append("select t.date, "
                + "sum(t.cpuNum), sum(t.memoryGB), sum(t.diskGB), "
                + "sum(t.cpuMoney), sum(t.memoryMoney), sum(t.diskMoney), "
                + "sum(t.totalMoney), count(t.id) "
                + "from UpVmCost t " + "where t.status != 'deleted' ");

        if (null != searchBean.getQueryYear()) {
            hql.append("and t.fiscalYear = :fiscalYear ");
            map.put("fiscalYear", searchBean.getQueryYear());
        }
        if (null != searchBean.getQueryGroupId()) {
            hql.append("and t.group.id = :groupId ");
            map.put("groupId", searchBean.getQueryGroupId());
        }
        if (Utility.isNotEmpty(searchBean.getStartDt())) {
            hql.append("and t.date >= :startDt ");
            map.put("startDt", FormatUtil.formatDate(searchBean.getStartDt()));
        }
        if (Utility.isNotEmpty(searchBean.getEndDt())) {
            hql.append("and t.date <= :endDt ");
            map.put("endDt", FormatUtil.formatDate(searchBean.getEndDt()));
        }

        DaoUtil.addDataScope(hql, map, searchBean.getMainMenuType());

        hql.append("and t.upTenant.id = :tenantId "
                + "group by t.date "
                + "order by t.date desc");
    }

    private void assembleAppSystemStatisticHql(StringBuilder hql, Map<String, Object> map, UpVmCostSearchBean searchBean) {

        hql.append("select t.fiscalYear, t.group.id, t.appSystem.id, "
                + "sum(t.cpuNum), sum(t.memoryGB), sum(t.diskGB), "
                + "sum(t.cpuMoney), sum(t.memoryMoney), sum(t.diskMoney), "
                + "sum(t.totalMoney), count(t.id) "
                + "from UpVmCost t " + "where t.status != 'deleted' ");

        if (null != searchBean.getQueryYear()) {
            hql.append("and t.fiscalYear = :fiscalYear ");
            map.put("fiscalYear", searchBean.getQueryYear());
        }
        if (null != searchBean.getQueryAppSystemId()) {
            hql.append("and t.appSystem.id = :appSystemId ");
            map.put("appSystemId", searchBean.getQueryAppSystemId());
        }
        if (Utility.isNotEmpty(searchBean.getStartDt())) {
            hql.append("and t.date >= :startDt ");
            map.put("startDt", FormatUtil.formatDate(searchBean.getStartDt()));
        }
        if (Utility.isNotEmpty(searchBean.getEndDt())) {
            hql.append("and t.date <= :endDt ");
            map.put("endDt", FormatUtil.formatDate(searchBean.getEndDt()));
        }

        DaoUtil.addDataScope(hql, map, searchBean.getMainMenuType());

        hql.append("and t.upTenant.id = :tenantId "
                + "group by t.fiscalYear, t.group.id, t.appSystem.id "
                + "order by t.fiscalYear desc, t.group.id, t.appSystem.id ");
    }

    private void assembleAppSystemDetailHql(StringBuilder hql, Map<String, Object> map, UpVmCostSearchBean searchBean) {

        hql.append("select t.date, "
                + "sum(t.cpuNum), sum(t.memoryGB), sum(t.diskGB), "
                + "sum(t.cpuMoney), sum(t.memoryMoney), sum(t.diskMoney), "
                + "sum(t.totalMoney), count(t.id) "
                + "from UpVmCost t " + "where t.status != 'deleted' ");

        if (null != searchBean.getQueryYear()) {
            hql.append("and t.fiscalYear = :fiscalYear ");
            map.put("fiscalYear", searchBean.getQueryYear());
        }
        if (null != searchBean.getQueryAppSystemId()) {
            hql.append("and t.appSystem.id = :appSystemId ");
            map.put("appSystemId", searchBean.getQueryAppSystemId());
        }
        if (Utility.isNotEmpty(searchBean.getStartDt())) {
            hql.append("and t.date >= :startDt ");
            map.put("startDt", FormatUtil.formatDate(searchBean.getStartDt()));
        }
        if (Utility.isNotEmpty(searchBean.getEndDt())) {
            hql.append("and t.date <= :endDt ");
            map.put("endDt", FormatUtil.formatDate(searchBean.getEndDt()));
        }

        DaoUtil.addDataScope(hql, map, searchBean.getMainMenuType());

        hql.append("and t.upTenant.id = :tenantId "
                + "group by t.date "
                + "order by t.date desc");
    }

    private Integer[] getYearList(UpVmCostSearchBean searchBean) {
        StringBuilder hql = new StringBuilder("select distinct t.fiscalYear from UpVmCost t where t.status != 'deleted'");
        Map<String, Object> paramMap = new HashMap<>();
        DaoUtil.addDataScope(hql, paramMap, searchBean.getMainMenuType());
        List<Integer> dataList = queryDao.queryHql(hql.toString(), paramMap);
        dataList.sort(Integer::compareTo);
        return dataList.toArray(new Integer[dataList.size()]);
    }

    protected <B extends RecordBean> void fillPageInfo(SearchBean<B> searchBean, int recordCount) {
        if (recordCount == 0) {
            searchBean.setRecordCount(0);
            searchBean.setPageCount(1);
            searchBean.setPageNum(0);
        } else {
            searchBean.setRecordCount(recordCount);
            searchBean.setPageCount(recordCount / searchBean.getPageSize() + (recordCount % searchBean.getPageSize() == 0 ? 0 : 1));
            searchBean.setPageNum(Math.max(0, Math.min(searchBean.getPageNum(), searchBean.getPageCount() - 1)));
        }
    }

    protected final void copyPageInfo(PageBean orig, PageBean dest) {
        dest.setPageNum(orig.getPageNum());
        dest.setPageSize(orig.getPageSize());
        dest.setPageCount(orig.getPageCount());
        dest.setRecordCount(orig.getRecordCount());
    }


}
