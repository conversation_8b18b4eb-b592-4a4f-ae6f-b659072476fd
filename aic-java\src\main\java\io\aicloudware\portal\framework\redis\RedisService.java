package io.aicloudware.portal.framework.redis;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

@Component 
public class RedisService {
	
	@Value("#{'${redis_server_urls}'.split(',')}") 
    private List<String> redis_server_urls;// = "**************:6379";
	
    private static JedisCluster jedisCluster = null;
    //数据库模式是16个数据库 0~15 
    public static final int DEFAULT_DATABASE = 0;
    
    @PostConstruct
    public void init() {
        try {
            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(200); //连接实例的最大连接数
            config.setMaxIdle(20); //控制一个pool最多有多少个状态为idle(空闲的)的jedis实例，默认值也是8。
//            config.setMaxWaitMillis(MAX_WAIT); //等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
            config.setTestOnBorrow(true); // 在borrow一个jedis实例时，是否提前进行validate操作；如果为true，则得到的jedis实例均是可用的；
            
            if(redis_server_urls!=null && !redis_server_urls.isEmpty()) {
            	Set<HostAndPort> jedisNode=new HashSet<HostAndPort>();
            	for(String url : redis_server_urls) {
            		String[] uri = url.split(":");
            		jedisNode.add(new HostAndPort(uri[0],Integer.valueOf(uri[1])));
            	}
            	if(jedisNode.size() > 0) {
            		jedisCluster = new JedisCluster(jedisNode,config);
            	}
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Boolean loginTokenCache(String token) {
    	try {
	    	if(jedisCluster == null) {
	    		return true;
	    	}else if(jedisCluster.exists(token)) {
				return jedisCluster.get(token).equals("login");
			}else {
				jedisCluster.set(token, "login");
				jedisCluster.expire(token, 172800);
			}
    	}catch(Exception e) {
    		
    	}
        return true;
    }
    
    public void logoutTokenCache(String token) {
    	if(jedisCluster == null) {
    		return;
    	}else if(jedisCluster != null) {
    		if(jedisCluster.exists(token)) {
    			jedisCluster.set(token, "logout");
    		}
        }
    }
    
    public static void main(String[] args) throws InterruptedException {
    	try {
    	JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(200); 
        config.setMaxIdle(20);
        config.setTestOnBorrow(true);
        
        
    	Set<HostAndPort> jedisNode=new HashSet<HostAndPort>();
    	jedisNode.add(new HostAndPort("**************",7001));
    	jedisNode.add(new HostAndPort("**************",7002));
    	jedisNode.add(new HostAndPort("**************",7003));
    	jedisNode.add(new HostAndPort("**************",7004));
    	jedisNode.add(new HostAndPort("**************",7005));
    	jedisNode.add(new HostAndPort("**************",7006));
		JedisCluster jedisCluster = new JedisCluster(jedisNode,config);
        
		jedisCluster.set("test","hello");
		jedisCluster.expire("test", 3);
        
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
        Thread.sleep(1000);
        System.out.println(jedisCluster.exists("test"));
    	}catch(Exception e) {
    		e.printStackTrace();
    	}
	}
}
