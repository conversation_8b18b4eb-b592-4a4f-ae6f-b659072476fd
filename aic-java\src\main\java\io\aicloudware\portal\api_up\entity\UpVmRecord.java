package io.aicloudware.portal.api_up.entity;


import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpVmRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpVmRecordType;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_vm_record")
@Access(AccessType.FIELD)
public class UpVmRecord extends BaseUpEntity<UpVmRecordBean> implements IDataScopeEntity<UpVmRecordBean>, IEnvironmentEntity<UpVmRecordBean> {

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "sp_vm_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @Column(name = "record_dt")
    private Integer recordDt;

    @Column(name = "record_type")
    @Enumerated(EnumType.STRING)
    private UpVmRecordType recordType;

    @Column(name = "cpu")
    private Integer cpu;

    @Column(name = "memory_gb")
    private Integer memoryGB;

    @Column(name = "disk_gb")
    private Integer diskGB;

    @Column(name = "network")
    private Integer network;

    @Override
    public UpUser getOwner() {
        return owner;
    }

    @Override
    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
        if (vm != null && Utility.isNotZero(vm.getId())) {
        	vm = BeanFactory.getCloudDao().load(SpVm.class, vm.getId());
        }
    }

    public Integer getRecordDt() {
        return recordDt;
    }

    public void setRecordDt(Integer recordDt) {
        this.recordDt = recordDt;
    }

    public UpVmRecordType getRecordType() {
        return recordType;
    }

    public void setRecordType(UpVmRecordType recordType) {
        this.recordType = recordType;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public Integer getNetwork() {
        return network;
    }

    public void setNetwork(Integer network) {
        this.network = network;
    }
}
