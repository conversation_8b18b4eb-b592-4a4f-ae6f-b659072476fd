package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IDisplayNameEntity;
import io.aicloudware.portal.framework.sdk.bean.UpUploadFileBean;
import io.aicloudware.portal.framework.sdk.contants.UpUploadFileType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_upload_file")
@Access(AccessType.FIELD)
public final class UpUploadFile extends BaseUpEntity<UpUploadFileBean> implements IDisplayNameEntity<UpUploadFileBean> {

    @JoinColumn(name = "user_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser user;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpUploadFileType type;

    @Column(name = "path", nullable = false)
    private String path;

    @Column(name = "display_name")
    private String displayName;

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }

    public UpUploadFileType getType() {
        return type;
    }

    public void setType(UpUploadFileType type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}
