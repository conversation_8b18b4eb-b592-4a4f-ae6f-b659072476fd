package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡监视器")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerHealthCheckBean.class})
public class SpLoadBalancerHealthCheckBean extends SpRecordBean {

    @ApiModelProperty(value = "负载均衡")
    private Integer loadBalancerMemberId;

    @ApiModelProperty(value = "间隔")
    private String interval;
    
    @ApiModelProperty(value = "超时")
    private String timeout;
    
    @ApiModelProperty(value = "最大重试次数")
    private String maxRetry;
    
    @ApiModelProperty(value = "类型")
    private String type;
    
    @ApiModelProperty(value = "MODE")
    private String mode;
    
    @ApiModelProperty(value = "预期")
    private String expectation;
    
    @ApiModelProperty(value = "方法")
    private String method;
    
    @ApiModelProperty(value = "URL")
    private String url;
    
    @ApiModelProperty(value = "发送")
    private String send;
    
    @ApiModelProperty(value = "接收")
    private String receive;
    
    @ApiModelProperty(value = "扩展")
    private String extension;
    
	public Integer getLoadBalancerMemberId() {
		return loadBalancerMemberId;
	}

	public void setLoadBalancerMemberId(Integer loadBalancerMemberId) {
		this.loadBalancerMemberId = loadBalancerMemberId;
	}

	public String getInterval() {
		return interval;
	}

	public void setInterval(String interval) {
		this.interval = interval;
	}

	public String getTimeout() {
		return timeout;
	}

	public void setTimeout(String timeout) {
		this.timeout = timeout;
	}

	public String getMaxRetry() {
		return maxRetry;
	}

	public void setMaxRetry(String maxRetry) {
		this.maxRetry = maxRetry;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getExpectation() {
		return expectation;
	}

	public void setExpectation(String expectation) {
		this.expectation = expectation;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getSend() {
		return send;
	}

	public void setSend(String send) {
		this.send = send;
	}

	public String getReceive() {
		return receive;
	}

	public void setReceive(String receive) {
		this.receive = receive;
	}

	public String getExtension() {
		return extension;
	}

	public void setExtension(String extension) {
		this.extension = extension;
	}

	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	

}
