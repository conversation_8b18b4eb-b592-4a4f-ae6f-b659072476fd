package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBalanceServerRelationBean;

@Entity
@Table(name = "up_order_balance_server_relation")
@Access(AccessType.FIELD)
public class UpOrderBalanceServerRelation extends BaseUpEntity<UpOrderBalanceServerRelationBean> {

	@Column(name = "cloud_server_id")
	private Integer cloudServerId;
	
	@JoinColumn(name = "order_load_balance_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderLoadBalance orderLoadBalance;
	
	@Column(name = "weight")
	private Integer weight;

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public UpOrderLoadBalance getOrderLoadBalance() {
		return orderLoadBalance;
	}

	public void setOrderLoadBalance(UpOrderLoadBalance orderLoadBalance) {
		this.orderLoadBalance = orderLoadBalance;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	

}
