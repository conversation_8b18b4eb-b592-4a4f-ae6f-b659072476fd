package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "安全组关系")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpSecurityGroupRelationBean.class})
public class SpSecurityGroupRelationBean extends SpRecordBean {

    @ApiModelProperty(value = "安全组ID")
    private Integer securityGroupId;

    @ApiModelProperty(value = "安全组名称")
    private String securityGroupName;
    
    @ApiModelProperty(value = "安全组显示名称")
    private String securityGroupDisplayName;

    @ApiModelProperty(value = "云主机ID")
    private Integer vmId;

    @ApiModelProperty(value = "云主机名称")
    private String vmName;

    @ApiModelProperty(value = "云主机显示名称")
    private String vmDisplayName;

	public Integer getSecurityGroupId() {
		return securityGroupId;
	}

	public void setSecurityGroupId(Integer securityGroupId) {
		this.securityGroupId = securityGroupId;
	}

	public String getSecurityGroupName() {
		return securityGroupName;
	}

	public void setSecurityGroupName(String securityGroupName) {
		this.securityGroupName = securityGroupName;
	}

	public String getSecurityGroupDisplayName() {
		return securityGroupDisplayName;
	}

	public void setSecurityGroupDisplayName(String securityGroupDisplayName) {
		this.securityGroupDisplayName = securityGroupDisplayName;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public String getVmName() {
		return vmName;
	}

	public void setVmName(String vmName) {
		this.vmName = vmName;
	}

	public String getVmDisplayName() {
		return vmDisplayName;
	}

	public void setVmDisplayName(String vmDisplayName) {
		this.vmDisplayName = vmDisplayName;
	}
}
