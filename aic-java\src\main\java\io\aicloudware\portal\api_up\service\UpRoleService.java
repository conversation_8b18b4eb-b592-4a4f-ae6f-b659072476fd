package io.aicloudware.portal.api_up.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpRole;
import io.aicloudware.portal.framework.service.BaseService;

@Service
@Transactional
public class UpRoleService extends BaseService implements IUpRoleService {
    @Override
    public void initDefaultDataForTenant() {
        UpRole role = new UpRole();
        role.setName("信息部运维管理员");
        role.setDescription("信息部运维管理员（资源部署与管理）");
        dao.insert(role);

        role = new UpRole();
        role.setName("信息部领导");
        role.setDescription("信息部领导（二级审批）");
        dao.insert(role);

        role = new UpRole();
        role.setName("业务部门领导");
        role.setDescription("业务部门领导（一级审批）");
        dao.insert(role);

        role = new UpRole();
        role.setName("业务部门用户");
        role.setDescription("业务部门用户（资源申请与使用）");
        dao.insert(role);
    }
}
