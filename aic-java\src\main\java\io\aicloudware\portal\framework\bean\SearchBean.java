package io.aicloudware.portal.framework.bean;

import io.aicloudware.portal.framework.sdk.contants.UpExportFileType;
import io.aicloudware.portal.framework.sdk.contants.UpMainMenuType;
import io.swagger.annotations.ApiModelProperty;

public abstract class SearchBean<B extends RecordBean> extends PageBean {

    @ApiModelProperty(value = "主菜单模块类型")
    private UpMainMenuType mainMenuType;

    @ApiModelProperty(value = "精确查询条件")
    private B bean;

    @ApiModelProperty(value = "模糊查询条件")
    private B fuzzyBean;

    @ApiModelProperty(value = "排序字段一")
    private String orderName1;
    @ApiModelProperty(value = "排序方向一")
    private Boolean orderBy1;
    @ApiModelProperty(value = "排序字段二")
    private String orderName2;
    @ApiModelProperty(value = "排序方向二")
    private Boolean orderBy2;

    @ApiModelProperty(value = "导出文件类型")
    private UpExportFileType exportFileType;

    public UpMainMenuType getMainMenuType() {
        return mainMenuType;
    }

    public void setMainMenuType(UpMainMenuType mainMenuType) {
        this.mainMenuType = mainMenuType;
    }

    public B getBean() {
        return bean;
    }

    public void setBean(B bean) {
        this.bean = bean;
    }

    public B getFuzzyBean() {
        return fuzzyBean;
    }

    public void setFuzzyBean(B fuzzyBean) {
        this.fuzzyBean = fuzzyBean;
    }

    public String getOrderName1() {
        return orderName1;
    }

    public void setOrderName1(String orderName1) {
        this.orderName1 = orderName1;
    }

    public Boolean getOrderBy1() {
        return orderBy1;
    }

    public void setOrderBy1(Boolean orderBy1) {
        this.orderBy1 = orderBy1;
    }

    public String getOrderName2() {
        return orderName2;
    }

    public void setOrderName2(String orderName2) {
        this.orderName2 = orderName2;
    }

    public Boolean getOrderBy2() {
        return orderBy2;
    }

    public void setOrderBy2(Boolean orderBy2) {
        this.orderBy2 = orderBy2;
    }

    public UpExportFileType getExportFileType() {
        return exportFileType;
    }

    public void setExportFileType(UpExportFileType exportFileType) {
        this.exportFileType = exportFileType;
    }

}
