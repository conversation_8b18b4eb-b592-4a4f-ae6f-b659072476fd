package io.aicloudware.portal.api_up.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.framework.sdk.bean.ReqBlueprintDeploymentBean;
import io.aicloudware.portal.framework.sdk.bean.SpOVDCBean;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateMachineBean;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;

@Service
@Transactional
public interface IUpNewApplicationService {

    public UpApplicationBean getApplication(Integer applicationId);

    public UpApplicationBean createApplication(UpApplicationBean applicationBean);

    public UpApplicationBean updateApplication(UpApplicationBean applicationBean);

    public void submitApplication(Integer applicationId);

    public SpOVDCBean[] getReservationList();

    public String getAvailableFirstVmName(Integer appSystemId, Integer reservationId);

//    public SpIpUsageBean[] getAvailableIpList(Integer appSystemId, Integer reservationId);

    public SpVappTemplateMachineBean[] getBlueprintMachineList(Integer blueprintId);

    public ReqBlueprintDeploymentBean[] getReqBlueprintDeploymentList(Integer applicationId);

    public void saveReqBlueprintDeploymentList(Integer applicationId, ReqBlueprintDeploymentBean[] blueprintDeploymentList);

}
