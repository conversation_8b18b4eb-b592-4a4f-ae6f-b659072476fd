package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpApprovalHistory;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.api_up.service.IUpApprovalService;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryResultBean;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/approval_history")
@Api(value = "/approval_history", description = "待办审批单", position = 613)
public class UpApprovalHistoryController extends BaseUpController<UpApprovalHistory, UpApprovalHistoryBean, UpApprovalHistoryResultBean> {

    @Autowired
    private IUpApprovalService upApprovalHistoryService;

//    protected UpApprovalHistoryBean[] doQuery(SearchBean<UpApprovalHistoryBean> search, UpApprovalHistory entity) {
//        UpApprovalHistoryBean[] arraysBean = commonService.query(search, entity, getBeanType());
//        for (UpApprovalHistoryBean bean : arraysBean) {
//            UpApplicationBean applicationBean = commonService.load(UpApplication.class, UpApplicationBean.class, bean.getApplicationId());
//            bean.setApplicationBean(applicationBean);
//        }
//        return arraysBean;
//    }
//
//    protected UpApprovalHistoryBean doLoad(Integer id) {
//        UpApprovalHistoryBean bean = commonService.load(getEntityType(), getBeanType(), id);
//        UpApplicationBean applicationBean = commonService.load(UpApplication.class, UpApplicationBean.class, bean.getApplicationId());
//        bean.setApplicationBean(applicationBean);
//        return bean;
//    }
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalHistoryResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryApprovalHistory(@ApiParam(value = "查询条件") @RequestBody UpApprovalHistorySearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalHistoryBean.class)})
//    @ResponseBody
//    public ResponseBean getApprovalHistory(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }

//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalHistoryBean.class)})
//    @ResponseBody
//    public ResponseBean addApprovalHistory(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalHistoryListBean.class)})
//    @ResponseBody
//    public ResponseBean addApprovalHistory(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryListBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalHistoryBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalHistory(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                              @ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryBean bean,
//                                              BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalHistoryListBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalHistory(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalHistory(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalHistory(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }

//    @RequestMapping(value = "/to_approve", method = RequestMethod.POST)
//    @ApiOperation(notes = "/to_approve", httpMethod = "POST", value = "预提交审批单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpUserListBean.class)})
//    @ResponseBody
//    public ResponseBean toApprove(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryBean bean, BindingResult bindingResult) {
//        handleValidateResult(bindingResult);
//        return ResponseBean.success(upApprovalHistoryService.toApprove(bean));
//    }

//    @RequestMapping(value = "/do_approve", method = RequestMethod.POST)
//    @ApiOperation(notes = "/do_approve", httpMethod = "POST", value = "同意审批单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean doApprove(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryBean bean, BindingResult bindingResult) {
//        ThreadCache.initOperationLogLocal(UpOperationType.application_approve.getTitle(), ThreadCache.getUser(),
//                UpOperationType.application_approve, UpApprovalHistory.class, bean.getId(), bean.getName());
//        handleValidateResult(bindingResult);
//        upApprovalHistoryService.doApprove(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/do_approve_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/do_approve_list", httpMethod = "POST", value = "批量同意审批单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean doApproveList(@ApiParam(value = "实例对象") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        handleValidateResult(bindingResult);
//        for (Integer id : bean.getIdList()) {
//            UpApprovalHistoryBean historyBean = doLoad(id);
//            ThreadCache.initOperationLogLocal(UpOperationType.application_approve.getTitle(), ThreadCache.getUser(),
//                    UpOperationType.application_approve, UpApprovalHistory.class, historyBean.getId(), historyBean.getName());
//            UpUserListBean userListBean = upApprovalHistoryService.toApprove(historyBean);
//            List<Integer> userIdList = ListUtil.toList(Arrays.asList(userListBean.getDataList()), RecordBean::getId);
//            historyBean.setNextNodeApprovers(userIdList.toArray(new Integer[userIdList.size()]));
//            historyBean.setOpinion(bean.getOperateInfo());
//            upApprovalHistoryService.doApprove(historyBean);
//        }
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/do_reject", method = RequestMethod.POST)
//    @ApiOperation(notes = "/do_reject", httpMethod = "POST", value = "拒绝审批单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean doReject(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalHistoryBean bean, BindingResult bindingResult) {
//        ThreadCache.initOperationLogLocal(UpOperationType.application_reject.getTitle(), ThreadCache.getUser(),
//                UpOperationType.application_reject, UpApprovalHistory.class, bean.getId(), bean.getName());
//        handleValidateResult(bindingResult);
//        upApprovalHistoryService.doReject(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/do_reject_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/do_reject_list", httpMethod = "POST", value = "批量拒绝审批单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean doRejectList(@ApiParam(value = "实例对象") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        handleValidateResult(bindingResult);
//        for (Integer id : bean.getIdList()) {
//            UpApprovalHistoryBean historyBean = doLoad(id);
//            ThreadCache.initOperationLogLocal(UpOperationType.application_reject.getTitle(), ThreadCache.getUser(),
//                    UpOperationType.application_reject, UpApprovalHistory.class, historyBean.getId(), historyBean.getName());
//            historyBean.setOpinion(bean.getOperateInfo());
//            upApprovalHistoryService.doReject(historyBean);
//        }
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/get_application_detail/{applicationId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_application_detail/{applicationId}", httpMethod = "GET", value = "申请单审批详情")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalHistoryResultBean.class)})
//    @ResponseBody
//    public ResponseBean get_approval_detail(@ApiParam(value = "对象ID") @PathVariable Integer applicationId) {
//        return ResponseBean.success(upApprovalHistoryService.getApplicationDetail(applicationId));
//    }

}
