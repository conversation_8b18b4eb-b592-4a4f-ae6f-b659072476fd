package io.aicloudware.portal.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.remote.RemoteUtil;
import io.aicloudware.portal.framework.utility.Utility;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "请求结果信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ResponseBean extends BaseBean {

    public static ResponseBean success(Object data) {
        return new ResponseBean(0, "", "").setData(data);
    }

    public static ResponseBean error(int errorCode, String errorType, String errorMsg) {
        return new ResponseBean(errorCode, errorType, errorMsg);
    }

    @ApiModelProperty(value = "错误代码")
    private final Integer errorCode;
    @ApiModelProperty(value = "错误类型")
    private final String errorType;
    @ApiModelProperty(value = "错误消息")
    private final String errorMsg;
    @ApiModelProperty(value = "返回结果数据")
    private String jsonData;

    private ResponseBean(Integer errorCode, String errorType, String errorMsg) {
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.errorMsg = errorMsg;
    }

    public boolean isSuccess() {
        return Utility.isZero(errorCode);
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorType() {
        return errorType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public String getJsonData() {
        return jsonData;
    }

    public void setJsonData(String jsonData) {
        this.jsonData = jsonData;
    }

    public <T> T getData(Class<T> type) {
        if (Utility.isEmpty(jsonData) || type == null) {
            return null;
        }
        if (String.class.equals(type)) {
            return (T) jsonData;
        }
        return RemoteUtil.gson.fromJson(jsonData, type);
    }

    public ResponseBean setData(Object data) {
        if (data != null) {
            if (data instanceof String) {
                this.jsonData = data.toString();
            } else {
                this.jsonData = RemoteUtil.gson.toJson(data);
            }
        }
        return this;
    }
}
