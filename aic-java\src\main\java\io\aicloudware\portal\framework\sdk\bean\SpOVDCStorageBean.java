package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "预留存储配置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOVDCStorageBean.class})
public class SpOVDCStorageBean extends SpRecordBean {

    @ApiModelProperty(value = "预留ID")
    private Integer ovdcId;

    @ApiModelProperty(value = "预留名称")
    private String ovdcName;

    @ApiModelProperty(value = "预留显示名称")
    private String ovdcDisplayName;

    @ApiModelProperty(value = "存储ID")
    private Integer storageId;

    @ApiModelProperty(value = "存储名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String storageName;

    @ApiModelProperty(value = "存储显示名称")
    private String storageDisplayName;

    @ApiModelProperty(value = "存储预留(GB)")
    private Integer storageReservedGB;

    @ApiModelProperty(value = "存储已分配(GB)")
    private Integer storageUsedGB;

    public Integer getOvdcId() {
		return ovdcId;
	}

	public void setOvdcId(Integer ovdcId) {
		this.ovdcId = ovdcId;
	}

	public String getOvdcName() {
		return ovdcName;
	}

	public void setOvdcName(String ovdcName) {
		this.ovdcName = ovdcName;
	}

	public String getOvdcDisplayName() {
		return ovdcDisplayName;
	}

	public void setOvdcDisplayName(String ovdcDisplayName) {
		this.ovdcDisplayName = ovdcDisplayName;
	}

	public Integer getStorageId() {
        return storageId;
    }

    public void setStorageId(Integer storageId) {
        this.storageId = storageId;
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = storageName;
    }

    public Integer getStorageReservedGB() {
        return storageReservedGB;
    }

    public void setStorageReservedGB(Integer storageReservedGB) {
        this.storageReservedGB = storageReservedGB;
    }

    public Integer getStorageUsedGB() {
        return storageUsedGB;
    }

    public void setStorageUsedGB(Integer storageUsedGB) {
        this.storageUsedGB = storageUsedGB;
    }

    

    public String getStorageDisplayName() {
        return storageDisplayName;
    }

    public void setStorageDisplayName(String storageDisplayName) {
        this.storageDisplayName = storageDisplayName;
    }

}
