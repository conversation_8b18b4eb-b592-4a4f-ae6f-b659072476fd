package io.aicloudware.portal.api_vcpp.service.order;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudDisk;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudServer;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudServerNetwork;
import io.aicloudware.portal.api_vcpp.entity.UpOrderElasticSearch;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.OrderUtil;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpDeployStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudStorageChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.EskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.SpElasticSearch;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplateMachine;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.entity.SpVmDisk;

@Service
@Transactional
public class UpOrderEskService extends BaseService implements IUpOrderEskService {

//	@Autowired
//	private IUpFinanceRechargeService financeRechargeService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Override
	public Integer save(UpOrderCloudServerBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean.getServerConfigId(), "请选择配置！");
		AssertUtil.check(bean.getImageId(), "请选择镜像！");
		AssertUtil.check(bean.getVpcId(), "请选择专有网络！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
		AssertUtil.check(bean.getNetworkId(), "请选择子网！");
		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");
		
		AssertUtil.check(bean.getName(), "请输入实例名！");
		bean.setAccount(user.getName());
		AssertUtil.check(Pattern.compile("(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,20}").matcher(bean.getPassword()).find(),"密码不符合规则");
		bean.setHostname(bean.getName());
		
		AssertUtil.check((bean.getCloudDiskList() != null && bean.getCloudDiskList().length >= 1), "请输入磁盘大小！");
		AssertUtil.check(bean.getAmount() != null && bean.getAmount() >= 1, "请输入购买数量！");
		AssertUtil.check(bean.getAmount() <= 20, "一次申请数量不能超过20台！");
		Boolean hasDisk = false;
		for (UpOrderCloudDiskBean disk : bean.getCloudDiskList()) {
			if (disk.getType().equals(SpVmDiskType.system) && disk.getDiskGB() != null && disk.getDiskGB() != 0) {
				hasDisk = true;
			}
		}
		AssertUtil.check(hasDisk, "缺失系统盘！");
//		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_esk, user.getId()) == 0, "您有未完成的Elasticsearch服务器申请！");
		
		SpVappTemplate template = this.dao.load(SpVappTemplate.class, bean.getImageId());
		
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_esk);
		order.setName("[" + OrderType.new_esk + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setPaymentType(bean.getPaymentType());
		order.setSpOrg(user.getOrg());
		order.setNumber(bean.getAmount());
		if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		List<UpOrderCloudServer> entitys = new ArrayList<>();
		List<SpVapp> vappEntitys = new ArrayList<>();
		
		UpProductVmSet vmSet = null;
        SpVapp spVapp = new SpVapp();
        spVapp.setName(bean.getName() + "-" + System.currentTimeMillis()
                + String.format("%04d", (int) (Math.random() * 1000)));
        // spVapp.setOrderCloudServer(cloudServer);
        spVapp.setOwner(user);
        spVapp.setSpOrg(user.getOrg());
        spVapp.setDeployStatus(SpDeployStatus.INIT);
        spVapp.setVappTemplate(template);
        spVapp.setVmType(SpVmType.esk);
        
        List<SpVm> vms = new ArrayList<SpVm>();
        
//		UpProductDiskSet diskSet = null;
		for (int i = 0; i < bean.getAmount();) {
			for (int t = 0; t < template.getVmList().size();) {
				SpVappTemplateMachine machine = template.getVmList().get(t);
				String name = bean.getName() + "-" + String.format("%03d", ++i)+"-" + String.format("%03d", ++t);
				String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));
				UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
				entity.setRegion(order.getRegion());
				entity.setTaskSequence(i*100);
				entity.setDbName(bean.getName());
				entity.setOwner(user);
				entity.setName(name + "-" + random);
				entity.setOrder(order);
				entity.setSpOrg(user.getOrg());
				if (vmSet == null) {
					vmSet = this.dao.load(UpProductVmSet.class, entity.getServerConfigId());
					AssertUtil.check(vmSet != null && vmSet.getEnabled() && vmSet.getType().equals(ProductVmSetType.esk), "虚拟机配置信息异常！");
					order.setVmSet(vmSet);
					order.setCpuNum(vmSet.getCpuUnit());
					order.setMemoryNum(vmSet.getMemoryUnit());
					order.setCpuPrice(vmSet.getCpuPrice());
					order.setMemoryPrice(vmSet.getMemoryPrice());
					
				}
				
	//			order.setCpuNum((order.getCpuNum() == null ? 0 : order.getCpuNum()) + vmSet.getCpuUnit());
	//			order.setMemoryNum((order.getMemoryNum() == null ? 0 : order.getMemoryNum()) + vmSet.getMemoryUnit());
				entity.setCpu(vmSet.getCpuUnit());
				entity.setMemory(vmSet.getMemoryUnit());
				entity.setServerType(vmSet.getServerType());
				entity.setHostname(bean.getName() + "-" + i);
				entity.setTemplateMachine(machine);
				
				// 处理磁盘
				List<UpOrderCloudDisk> disks = new ArrayList<>();
				int j = 0;
				for (UpOrderCloudDiskBean diskBean : bean.getCloudDiskList()) {
					if (diskBean.getDiskGB() == null || diskBean.getDiskGB() == 0) {
						continue;
					}
					UpOrderCloudDisk disk = BeanCopyUtil.copy(diskBean, UpOrderCloudDisk.class);
					disk.setRegion(order.getRegion());
					disk.setTaskSequence(i*100+j*10);
					disk.setChargeType(CloudStorageChargeType.hour);
					disk.setOrderCloudServer(entity);
					
					if(diskBean.getType().equals(SpVmDiskType.system)) {
						disk.setType(SpVmDiskType.system);
						disk.setDiskType(DiskType.hdd);
					}else {
	//					if (diskSet == null) {
	//						diskSet = this.dao.load(UpProductDiskSet.class, disk.getDiskConfigId());
	//					}
						disk.setType(SpVmDiskType.mount);
						disk.setDiskType(DiskType.hdd);
					}
					
					disk.setName(name + "-" + random + "-" + j);
					disk.setDiskNumber(j);
					disk.setOwner(user);
					disk.setPaymentType(bean.getPaymentType());
					disk.setOrder(order);
					disk.setSpOrg(user.getOrg());
					disks.add(disk);
	
					if (disk.getType().equals(SpVmDiskType.mount)) {
	//					AssertUtil.check(diskSet != null && diskSet.getEnabled(), "云盘配置信息异常！");
	//					AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= disk.getDiskGB(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
	//					AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= disk.getDiskGB(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
	//					order.setDiskSet(diskSet);
						order.setDiskNum(disk.getDiskGB());
	//					order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(disk.getDiskGB())));
						order.setDiskPrice(BigDecimal.ZERO);
	//					order.setDiskNum((order.getDiskNum() == null ? 0 : order.getDiskNum()) + disk.getDiskGB());
					}else if(disk.getType().equals(SpVmDiskType.system)) {
						order.setSystemDiskNum(disk.getDiskGB());
					}
					j++;
				}
	
				entity.setCloudDiskList(disks);
				
				Integer network1Id = null;
	    		List<Integer> networkIdList = queryDao.querySql("select id from sp_ovdc_network where name='vmware-shared-network' and status='active' and sp_org_id=:orgId", MapUtil.of("orgId", user.getOrg().getId()));
	    		AssertUtil.check(networkIdList, "未找到匹配的网络");
	    		network1Id = networkIdList.get(0);
	    		entity.setNetworkId(network1Id);
	    		Integer externalNetworkId = bean.getNetworkId();
	            entity.setExternalNetworkId(externalNetworkId);
	
	            List<UpOrderCloudServerNetwork> orderCloudServerNetworkList = new ArrayList<>();
	            UpOrderCloudServerNetwork net1 = new UpOrderCloudServerNetwork();
	            net1.setNicNumber(0);
	            net1.setNetworkId(networkIdList.get(0));
	            net1.setName(entity.getName()+"-net0");
	            net1.setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType.VMXNET3);
	            net1.setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode.POOL);
	            net1.setFenceMode(UpOrderSystemEnums.FenceMode.bridged);
	            net1.setConnected(true);
	            net1.setOwner(user);
	            net1.setOrderCloudServer(entity);
	            net1.setOrder(order);
	            net1.setRegion(order.getRegion());
	            orderCloudServerNetworkList.add(net1);
	
	            UpOrderCloudServerNetwork net2 = new UpOrderCloudServerNetwork();
	            net2.setNicNumber(1);
	            net2.setName(entity.getName()+"-net1");
	            net2.setNetworkId(externalNetworkId);
	            net2.setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType.VMXNET3);
	            net2.setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode.POOL);
	            net2.setFenceMode(UpOrderSystemEnums.FenceMode.bridged);
	            net2.setConnected(true);
	            net2.setOwner(user);
	            net2.setOrder(order);
	            net2.setOrderCloudServer(entity);
	            net2.setRegion(order.getRegion());
	            orderCloudServerNetworkList.add(net2);
	            
	            entity.setOrderCloudServerNetworkList(orderCloudServerNetworkList);
	            
	            SpVm vm = this.addVm(entity, template);
	            vm.setTemplateMachine(machine);
	            vms.add(vm);
	            entity.setVm(vm);
	            entitys.add(entity);
			}
			spVapp.setVmList(vms);
			spVapp.setOrder(order);
			spVapp.setRegion(order.getRegion());
			vappEntitys.add(spVapp);
			
		}

//		order.setCpuPrice(vmSet.getCpuPrice().multiply(BigDecimal.valueOf(order.getCpuNum() / vmSet.getCpuUnit())));
//		order.setMemoryPrice(vmSet.getMemoryPrice().multiply(BigDecimal.valueOf(order.getMemoryNum() / vmSet.getMemoryUnit())));

		this.dao.insert(order);
		this.dao.insert(vappEntitys);
		this.dao.insert(entitys);

		for(SpVapp vapp : vappEntitys) {
			for(UpOrderCloudServer orderServer : entitys) {
				if(vapp.getVmList().get(0).getId().equals(orderServer.getVm().getId())) {
					vapp.setOrderCloudServer(orderServer);
					break;
				}
			}
		}
		this.dao.update(vappEntitys, "orderCloudServer");

		String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));

		UpOrderElasticSearch myEskOrder = new UpOrderElasticSearch();
		myEskOrder.setOwner(user);
		myEskOrder.setName(bean.getName()+"-"+random);
		myEskOrder.setOrder(order);
		myEskOrder.setSpOrg(user.getOrg());
		myEskOrder.setRegion(order.getRegion());
		//mySqlOrder.setSpVapp(spVapp);
		this.dao.insert(myEskOrder);

		SpElasticSearch esk = new SpElasticSearch();
		esk.setName(bean.getName());
		esk.setOrder(order);
		esk.setOvdcNetwork(ovdcNetwork);
		esk.setOwner(user);
		esk.setPassword(bean.getPassword());
		esk.setPaymentType(bean.getPaymentType());
		esk.setEskStatus(EskStatus.init);
		esk.setSpOrg(user.getOrg());
		esk.setSpVapp(spVapp);

		esk.setSpUuid("urn:vcloud:esk:vapp:"+spVapp.getId());
		esk.setRegion(order.getRegion());
		this.dao.insert(esk);
			


        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        dao.insert(task);
        quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}


	private SpVm addVm(UpOrderCloudServer cloudServer, SpVappTemplate template) {
        SpVm spVm = new SpVm();
        spVm.setName(cloudServer.getName());
        spVm.setHostName(cloudServer.getHostname());
        spVm.setSpOrg(cloudServer.getSpOrg());
        spVm.setCpuNum(cloudServer.getCpu());
        spVm.setMemoryGB(cloudServer.getMemory());
        spVm.setVappTemplate(template);
        spVm.setPowerStatus(SpVmPowerStatus.power_on);
        spVm.setOwner(cloudServer.getOwner());
        spVm.setOrder(cloudServer.getOrder());
        spVm.setDeployStatus(SpDeployStatus.INIT);
        //spVm.setVmType(VcdOperationCommon.getVmType(cloudServer.getOrder()));
        spVm.setVmType(OrderUtil.getVmType(cloudServer.getOrder()!=null?cloudServer.getOrder().getType():null));
        List<SpVmDisk> spvmDiskList = new ArrayList<SpVmDisk>();
        int number = 0;
        int diskGB = 0;

        for (UpOrderCloudDisk disk : cloudServer.getCloudDiskList()) {
            SpVmDisk spvmDisk = new SpVmDisk();
            spvmDisk.setSpOrg(disk.getSpOrg());
            spvmDisk.setName(disk.getName());
            spvmDisk.setDiskGB(disk.getDiskGB());
            spvmDisk.setDiskNumber(number++);
            spvmDisk.setDiskLabel(disk.getName());
            spvmDisk.setDiskPath("");
            spvmDisk.setType(disk.getType());
            spvmDisk.setOrder(disk.getOrder());
            spvmDisk.setRegion(cloudServer.getRegion());
            // String spUuid = "urn:vcloud:disk:" +
            // MD5Util.format(MD5Util.encode(disk.getName()));
            // String spUuid = spVm.getSpUuid() + VcdCloudService.SEPERATOR +
            // spvmDisk.getDiskNumber();
            // spvmDisk.setSpUuid(spUuid);
            // spvmDisk.setThinProvisioned(thinProvisioned);
            spvmDiskList.add(spvmDisk);
            diskGB += spvmDisk.getDiskGB();
        }
        spVm.setDiskList(spvmDiskList);
        spVm.setDiskGB(diskGB);
        spVm.setRegion(cloudServer.getRegion());
        return spVm;
    }

}
