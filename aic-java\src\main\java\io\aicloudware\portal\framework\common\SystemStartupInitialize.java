package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.api_up.entity.UpRight;
import io.aicloudware.portal.api_up.entity.UpSystemConfig;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpTenantService;
import io.aicloudware.portal.framework.controller.BaseController;
import io.aicloudware.portal.framework.dao.IQueryDao;
import io.aicloudware.portal.framework.exception.LogicException;
import io.aicloudware.portal.framework.executor.IExecutor;
import io.aicloudware.portal.framework.hibernate.EntityRename;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpRightType;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpPVDC;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.persistence.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional
public class SystemStartupInitialize implements IExecutor {

    @Autowired
    private SystemVersionUpgrade systemVersionUpgrade;

    @Autowired
    private IQueryDao queryDao;

    @Autowired
    private IUpTenantService upTenantService;

    @Autowired
    private ICommonService commonService;

    @PostConstruct
    public void doExecute() {
        ThreadCache.setSystemAdminLogin();
        UpSystemConfig systemConfig = null;
        try {
            systemConfig = queryDao.load(UpSystemConfig.class, 1);
        } catch (Exception ignored) {
        }
        systemVersionUpgrade.doUpgrade_VersionFirst(systemConfig == null ? null : systemConfig.getValue());
        commonService.doTransactionalTask(() -> {
            String dbVersion = doInitDB();
            systemVersionUpgrade.doVersionUpgrade(dbVersion);
        });

//        upStaticsCacheService.startPerformanceMonitorTask();
    }

    private String doInitDB() {
        final List<String> tableList = new ArrayList<>();
        final Set<Class<?>> classSet = ClassReaderUtil.getClasses("io.aicloudware.portal");
        for (Class<?> clazz : classSet) {
            checkTableAndColumnDuplicateName(tableList, clazz);
        }

        tableList.clear();
        tableList.addAll(queryDao.querySql("select tablename from pg_tables where schemaname = 'public' order by tablename", null));

        final List<String> sqlList = new ArrayList<>();
        boolean is_create_new_db = !tableList.contains("up_system_config");
        // comment for security issue
//        if (is_create_new_db) {
//            for (String tableName : tableList) {
//                if (StringUtils.startsWithIgnoreCase(tableName, ApiConstants.IOT_TABLE_PREFIX)) {
//                    // do nothing for iot tables
//                }else {
//                    sqlList.add("drop table if exists " + tableName + " cascade");
//                }
//            }
//            tableList.clear();
//        }

        if (Utility.isNotEmpty(tableList)) {
            for (Class<?> clazz : classSet) {
                generateTableRenameSql(sqlList, tableList, clazz);
            }
        }
        if (Utility.isNotEmpty(sqlList)) {
            systemVersionUpgrade.executeSqlList(sqlList);
            sqlList.clear();
        }

        final List<String> defaultColumnList = Arrays.asList("id", "name", "create_by", "update_by", "create_tm", "update_tm", "status");
        for (Class<?> clazz : classSet) {
            generateTableSql(sqlList, clazz, tableList, defaultColumnList);
        }
        for (String tableName : tableList) {
            sqlList.add("drop table if exists " + tableName + " cascade");
        }
        if (Utility.isNotEmpty(sqlList)) {
            systemVersionUpgrade.executeSqlList(sqlList);
            sqlList.clear();
        }

        insertRight();
        if (is_create_new_db) {
            insertTenantAndUser();
        }
        systemVersionUpgrade.insertDefaultSystemConfig();
        return queryDao.load(UpSystemConfig.class, 1).getValue();
    }

    private void checkTableAndColumnDuplicateName(final List<String> tableList, final Class<?> clazz) {
        Table table = clazz.getAnnotation(Table.class);
        if (table == null) {
            return;
        }
        if (tableList.contains(table.name())) {
            throw new LogicException("Duplicate table name : " + clazz.getSimpleName() + " : " + table.name());
        }
        tableList.add(table.name());
        EntityRename renameTable = clazz.getAnnotation(EntityRename.class);
        if (renameTable != null && Utility.isNotEmpty(renameTable.fromName())) {
            for (String tableName : renameTable.fromName()) {
                if (tableList.contains(tableName)) {
                    throw new LogicException("Duplicate table name : " + clazz.getSimpleName() + " : " + tableName);
                }
                tableList.add(tableName);
            }
        }

        final List<String> columnList = new ArrayList<>();
        Map<String, Field> fieldMap = Utility.describeFieldMap(clazz);
        for (Field field : fieldMap.values()) {
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                if (columnList.contains(column.name())) {
                    throw new LogicException("Duplicate column name : " + clazz.getSimpleName() + "." + field.getName() + " : " + column.name());
                }
                columnList.add(column.name());
            }
            EntityRename renameColumn = field.getAnnotation(EntityRename.class);
            if (renameColumn != null && Utility.isNotEmpty(renameColumn.fromName())) {
                for (String columnName : renameColumn.fromName()) {
                    if (columnList.contains(columnName)) {
                        throw new LogicException("Duplicate column name : " + clazz.getSimpleName() + "." + field.getName() + " : " + columnName);
                    }
                    columnList.add(columnName);
                }
            }
        }
    }

    private void generateTableRenameSql(final List<String> sqlList, final List<String> tableList, final Class<?> clazz) {
        Table table = clazz.getAnnotation(Table.class);
        if (table == null) {
            return;
        }

        if (!tableList.contains(table.name())) {
            EntityRename renameTable = clazz.getAnnotation(EntityRename.class);
            if (renameTable != null && Utility.isNotEmpty(renameTable.fromName())) {
                for (String oldTableName : renameTable.fromName()) {
                    if (tableList.contains(oldTableName)) {
                        sqlList.add("alter table " + oldTableName + " rename to " + table.name());
                        tableList.remove(oldTableName);
                        tableList.add(table.name());
                        break;
                    }
                }
            }
        }
    }

    private void generateTableSql(List<String> sqlList, Class<?> clazz, List<String> tableList, List<String> defaultColumnList) {
        Table table = clazz.getAnnotation(Table.class);
        if (table == null) {
            return;
        }
        if (tableList.contains(table.name())) {
            tableList.remove(table.name());
            checkTableColumn(sqlList, table, clazz, defaultColumnList);
        } else {
            StringBuilder sb = new StringBuilder("create table " + table.name() + " (" +
                    "id serial not null," +
                    "name varchar(256) not null,"
            );
            Map<String, Field> fieldMap = Utility.describeFieldMap(clazz);
            fillColumnInfo(clazz, defaultColumnList, fieldMap, new LinkedHashMap<>(), sb);
            sb.append("status varchar(256) not null," +
                    "create_by int not null," +
                    "update_by int not null," +
                    "create_tm timestamp not null," +
                    "update_tm timestamp not null" +
                    ")");
            sqlList.add(sb.toString());
            sqlList.add("alter table " + table.name() + " add constraint pk_" + table.name() + " primary key(id)");
        }
    }

    private void fillColumnInfo(Class<?> clazz, List<String> defaultColumnList, Map<String, Field> fieldMap, Map<String, List<String>> renameMap, StringBuilder sb) {
        for (Field field : fieldMap.values()) {
            String columnName = null;
            String type = null;
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                if (defaultColumnList.contains(column.name())) {
                    continue;
                }
                columnName = column.name();
                EntityRename rename = field.getAnnotation(EntityRename.class);
                if (rename != null && Utility.isNotEmpty(rename.fromName())) {
                    renameMap.put(columnName, Arrays.asList(rename.fromName()));
                }
                Enumerated enumerated = field.getAnnotation(Enumerated.class);
                if (enumerated != null) {
                    if (enumerated.value() == EnumType.STRING) {
                        type = "varchar(" + column.length() + ")";
                    } else {
                        type = "smallint";
                    }
                } else if (String.class.equals(Utility.getFieldType(field))) {
                    type = "varchar(" + column.length() + ")";
                } else if (Integer.class.equals(Utility.getFieldType(field))) {
                    type = "int";
                } else if (Long.class.equals(Utility.getFieldType(field))) {
                    type = "bigint";
                } else if (Float.class.equals(Utility.getFieldType(field))) {
                    type = "real";
                } else if (Double.class.equals(Utility.getFieldType(field))) {
                    type = "double precision";
                } else if (Boolean.class.equals(Utility.getFieldType(field))) {
                    type = "boolean";
                } else if (Date.class.equals(Utility.getFieldType(field))) {
                    type = "timestamp";
                } else if (BigDecimal.class.equals(Utility.getFieldType(field))) {
                    type = "numeric";
                } else if (JSONArray.class.equals(Utility.getFieldType(field))) {
                    type = "json";
                } else {
                    AssertUtil.check(false, "不支持该字段类型：" + clazz.getSimpleName() + "." + field.getName() + "->" + Utility.getFieldType(field));
                }
                if (!column.nullable()) {
                    type += " not null";
                }
            } else {
                JoinColumn joinColumn = field.getAnnotation(JoinColumn.class);
                if (joinColumn != null) {
                    columnName = joinColumn.name();
                    type = "int";
                    if (!joinColumn.nullable()) {
                        type += " not null";
                    }
                }
            }
            if (!Collection.class.isAssignableFrom(Utility.getFieldType(field)) || JSON.class.isAssignableFrom(Utility.getFieldType(field))) {
                Transient _transient = field.getAnnotation(Transient.class);
                if (_transient == null) {
                    AssertUtil.check(Utility.isNotEmpty(columnName), "找不到配置的列字段名称：" + clazz.getSimpleName() + "." + field.getName());
                    sb.append(columnName + " " + type + ",");
                }
            }
        }
    }

    private void checkTableColumn(List<String> sqlList, Table table, Class<?> clazz, List<String> defaultColumnList) {
        StringBuilder sb = new StringBuilder();
        Map<String, Field> fieldMap = Utility.describeFieldMap(clazz);
        Map<String, List<String>> renameMap = new LinkedHashMap<>();
        fillColumnInfo(clazz, defaultColumnList, fieldMap, renameMap, sb);
        List<String> entityColumnList = Arrays.asList(sb.toString().split(","));
        Map<String, String> entityColumnMap = ListUtil.map(entityColumnList, new ListUtil.ConvertKey<String, String>() {
            @Override
            public String getKey(String value) {
                return value.split(" ")[0];
            }
        });
        entityColumnMap.remove("");

        String sql = "select column_name, data_type, character_maximum_length, numeric_precision," +
                " numeric_scale, is_nullable, column_default from information_schema.columns" +
                " where table_name = '" + table.name() + "' order by ordinal_position";
        List<Object[]> dataList = queryDao.querySql(sql, null);
        Map<String, String> tableColumnMap = ListUtil.map(dataList, new ListUtil.Convert<Object[], String, String>() {
            @Override
            public String getKey(Object[] value) {
                return value[0].toString();
            }

            @Override
            public String getValue(Object[] value) {
                String column = value[0] + " ";
                if ("integer".equals(value[1])) {
                    column += "int";
                } else if ("character varying".equals(value[1])) {
                    column += "varchar(" + value[2] + ")";
                } else if ("boolean".equals(value[1])) {
                    column += "boolean";
                } else if ("bigint".equals(value[1])) {
                    column += "bigint";
                } else if ("double precision".equals(value[1])) {
                    column += "double precision";
                } else if ("timestamp without time zone".equals(value[1])) {
                    column += "timestamp";
                } else if ("numeric".equals(value[1])) {
                	column += "numeric";
                } else if ("json".equals(value[1])) {
                	column += "json";
                } else {
                    AssertUtil.check(false, "不支持该字段类型：" + table.name() + "." + value[0] + "->" + value[1]);
                }
                if ("NO".equals(value[5])) {
                    column += " not null";
                }
                return column;
            }
        });
        tableColumnMap.keySet().removeAll(defaultColumnList);

        for (String name : entityColumnMap.keySet()) {
            if (!tableColumnMap.containsKey(name)) {
                if (renameMap.containsKey(name)) {
                    for (String column : tableColumnMap.keySet()) {
                        if (renameMap.get(name).contains(column)) {
                            sqlList.add("alter table " + table.name() + " rename " + column + " to " + name);
                            tableColumnMap.put(name, tableColumnMap.remove(column));
                            break;
                        }
                    }
                } else {
                    // 不能直接添加非空的字段，如果表里面已经有数据，则会因为没有默认数据而报错
                    sqlList.add("alter table " + table.name() + " add " + entityColumnMap.get(name).replace(" not null", ""));
                }
            }
        }
        for (String name : entityColumnMap.keySet()) {
            if (tableColumnMap.containsKey(name) && !entityColumnMap.get(name).equals(tableColumnMap.get(name))) {
                String[] column_entity = entityColumnMap.get(name).split(" ", 3);
                String[] column_table = tableColumnMap.get(name).split(" ", 3);
                if (!column_entity[1].equals(column_table[1])) {
                    sqlList.add("alter table " + table.name() + " alter " + name + " type " + column_entity[1]);
                }
                if (column_entity.length == 3 && column_table.length == 2) {
                    // 不能直接修改成非空的字段，如果表里面已经有数据，则会因为没有默认数据而报错
//                    sqlList.add("alter table " + table.name() + " alter " + name + " set not null");
                }
                if (column_entity.length == 2 && column_table.length == 3) {
                    sqlList.add("alter table " + table.name() + " alter " + name + " drop not null");
                }
            }
        }
        for (String name : tableColumnMap.keySet()) {
            if (!entityColumnMap.containsKey(name)) {
                sqlList.add("alter table " + table.name() + " drop " + name);
            }
        }
    }

    private void insertTenantAndUser() {
        upTenantService.initDefaultData();

        UpUser user = new UpUser();
        user.setName("system-admin");
        user.setPassword(EncryptUtil.encryptWithRSA("1qaz@WSX"));
        user.setDisplayName("系统管理员");
        user.setMobile("13912345678");
        user.setEmail("<EMAIL>");
        user.setSystemAdmin(Boolean.TRUE);
        user.setTenantAdmin(Boolean.FALSE);
        user.setStatus(RecordStatus.system);
        queryDao.insert(user);

        SpOrg spOrg = new SpOrg();
        spOrg.setName("default");
        spOrg.setUsername("ppwsCg852EJABXvL4jRETY1TFRlY");
        spOrg.setPassword("sjfmYwFW7qytvpGvtW5Mxt64kZp0Ff");
        SpOrg newOrg = queryDao.insert(spOrg);

        SpPVDC pvdc = new SpPVDC();
        pvdc.setName("PVDC-default");
        pvdc.setSpUuid("PVDC-default");
        pvdc.setSpOrg(newOrg);
        queryDao.insert(pvdc);



        UpUser userAdmin = new UpUser();
        userAdmin.setName("admin");
        userAdmin.setPassword(EncryptUtil.encryptWithRSA("1qaz@WSX"));
        userAdmin.setDisplayName("管理员");
        userAdmin.setMobile("13900000000");
        userAdmin.setEmail("<EMAIL>");
        userAdmin.setOrg(newOrg);
        userAdmin.setSystemAdmin(Boolean.TRUE);
        userAdmin.setTenantAdmin(Boolean.TRUE);
        userAdmin.setStatus(RecordStatus.active);
        queryDao.insert(userAdmin);

    }

    private void insertRight() {
        List<UpRight> oldRightList = queryDao.list(UpRight.class);
        List<Integer> systemRightList = new ArrayList<>();
        List<Integer> tenantRightList = new ArrayList<>();
        List<UpRight> newRightList = new ArrayList<>();
        generateDefaultRight(systemRightList, tenantRightList, newRightList);
        generateApiUrlRight(systemRightList, tenantRightList, newRightList);
        for (UpRight newRight : newRightList) {
            newRight.setSystemAdmin(systemRightList.contains(newRight.getId()));
            newRight.setTenantAdmin(tenantRightList.contains(newRight.getId()));
            newRight.setId(null);
            for (UpRight oldRight : oldRightList) {
                if (Utility.equals(newRight.getType(), oldRight.getType())
                        && Utility.equals(newRight.getUrl(), oldRight.getUrl())
                        && Utility.equals(newRight.getTargetId(), oldRight.getTargetId())) {
                    oldRight.setName(newRight.getName());
                    oldRight.setSystemAdmin(newRight.getSystemAdmin());
                    oldRight.setTenantAdmin(newRight.getTenantAdmin());
                    queryDao.update(oldRight);
                    newRight = null;
                    break;
                }
            }
            if (newRight != null) {
                newRight.setStatus(RecordStatus.system);
                queryDao.insert(newRight);
            }
        }
    }

    private void generateDefaultRight(List<Integer> systemRightList, List<Integer> tenantRightList, List<UpRight> rightList) {
        int rightId = Utility.isEmpty(rightList) ? 0 : ListUtil.last(rightList).getId();
        Map<UpRightType, String> rightMap = new LinkedHashMap<>();
        rightMap.put(UpRightType.data_scope_system, "系统全部");
        rightMap.put(UpRightType.data_scope_tenant, "本租户");
        rightMap.put(UpRightType.data_scope_owner, "本用户");
        rightMap.put(UpRightType.entrust_manage_function, "授权动作");
        rightMap.put(UpRightType.authorize_app_system, "授权应用系统");
        rightMap.put(UpRightType.authorize_vm, "授权虚机");
        rightMap.put(UpRightType.blueprint_view_tenant, "本租户");
        rightMap.put(UpRightType.op_power_tenant, "本租户");
        rightMap.put(UpRightType.op_snapshot_tenant, "本租户");
        rightMap.put(UpRightType.op_change_resource_tenant, "本租户");
        rightMap.put(UpRightType.op_lifecycle_low_tenant, "本租户");
        rightMap.put(UpRightType.op_lifecycle_high_tenant, "本租户");
        rightMap.put(UpRightType.op_console_tenant, "本租户");
        for (UpRightType rightType : rightMap.keySet()) {
            UpRight right = new UpRight();
            right.setId(++rightId);
            right.setName(rightType.getType() + "（" + rightMap.get(rightType) + "）");
            right.setType(rightType);
            rightList.add(right);
            if (UpRightType.data_scope_system.equals(rightType)) {
                systemRightList.add(right.getId());
            } else {
                tenantRightList.add(right.getId());
            }
        }
    }

    private void generateApiUrlRight(List<Integer> systemRightList, List<Integer> tenantRightList, List<UpRight> rightList) {
        int rightId = Utility.isEmpty(rightList) ? 0 : ListUtil.last(rightList).getId();
        List<Api> apiList = new ArrayList<>();
        Map<Api, Class> apiMap = new HashMap<>();
        Set<Class<?>> classSet = ClassReaderUtil.getClasses(BaseController.class.getPackage().getName());
        for (Class<?> clazz : classSet) {
            Api api = clazz.getAnnotation(Api.class);
            if (api != null) {
                apiList.add(api);
                apiMap.put(api, clazz);
            }
        }
        List<String> systemUrlList = Arrays.asList("/server_connection", "/sp_tenant", "/up_tenant", "/up_tenant_relation");
        List<String> bothUrlList = Arrays.asList("/user", "/system_manage", "/right");
        Collections.sort(apiList, new Comparator<Api>() {
            @Override
            public int compare(Api o1, Api o2) {
                return o1.position() - o2.position();
            }
        });
        for (int i = 0; i < apiList.size(); i++) {
            Api api = apiList.get(i);
            UpRight classRight = new UpRight();
            classRight.setId(++rightId);
            classRight.setName(UpRightType.url_api_module.getType() + "（" + api.description() + "）");
            classRight.setType(UpRightType.url_api_module);
            classRight.setUrl(api.value());
            rightList.add(classRight);
            if (systemUrlList.contains(classRight.getUrl())) {
                systemRightList.add(classRight.getId());
            } else if (bothUrlList.contains(classRight.getUrl())) {
                systemRightList.add(classRight.getId());
                tenantRightList.add(classRight.getId());
            } else {
                tenantRightList.add(classRight.getId());
            }
            for (Method method : apiMap.get(api).getMethods()) {
                ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
                if (apiOperation != null) {
                    UpRightType rightType = UpRightType.url_api_operate;
                    UpRight methodRight = new UpRight();
                    methodRight.setId(++rightId);
                    methodRight.setName(rightType.getType() + "（" + apiOperation.value() + "）");
                    methodRight.setType(rightType);
                    methodRight.setUrl(api.value() + apiOperation.notes().replace("{id}", ""));
                    rightList.add(methodRight);
                    if (systemUrlList.contains(classRight.getUrl())) {
                        systemRightList.add(methodRight.getId());
                    } else if (bothUrlList.contains(classRight.getUrl())) {
                        systemRightList.add(methodRight.getId());
                        tenantRightList.add(methodRight.getId());
                    } else {
                        tenantRightList.add(methodRight.getId());
                    }
                }
            }
        }
    }
}
