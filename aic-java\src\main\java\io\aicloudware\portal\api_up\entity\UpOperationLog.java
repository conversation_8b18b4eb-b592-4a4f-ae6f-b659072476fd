package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpOperationLogBean;
import io.aicloudware.portal.framework.sdk.bean.UpOperationLogSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOperationLevel;
import io.aicloudware.portal.framework.sdk.contants.UpOperationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import org.hibernate.criterion.DetachedCriteria;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Date;
import java.util.Set;

@Entity
@Table(name = "up_operation_log")
@Access(AccessType.FIELD)
public class UpOperationLog extends BaseEntity<UpOperationLogBean> implements IDataScopeEntity<UpOperationLogBean>, IEnvironmentEntity<UpOperationLogBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

//    @JoinColumn(name = "application_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//    private UpApplication application;

    @JoinColumn(name = "order_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
    
    @Column(name = "operation_type")
    @Enumerated(EnumType.STRING)
    private UpOperationType operationType;

    @Column(name = "operation_level")
    @Enumerated(EnumType.STRING)
    private UpOperationLevel operationLevel;

    @Column(name = "operation_status")
    @Enumerated(EnumType.STRING)
    private UpOperationStatus operationStatus;

    @Column(name = "target_table")
    private String targetTable;

    @Column(name = "target_id")
    private Integer targetId;

    @Column(name = "target_name")
    private String targetName;

    @Column(name = "error_msg", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String errorMsg;

    @Column(name = "error_stack_trace", length = ApiConstants.STRING_MAX_LENGTH)
    private String errorStackTrace;

    @Column(name = "remote_ip")
    private String remoteIp;

    @Column(name = "remote_uri")
    private String requestUri;

    @Column(name = "request_tm")
    private Date requestTm;

    @Column(name = "cost_ms")
    private Integer costMS;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpOperationLogBean> searchBean, Set<String> aliasSet) {
        UpOperationLogSearchBean bean = (UpOperationLogSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getOperationTypeList())) {
            DaoUtil.addInValues(criteria, "operationType", Arrays.asList(bean.getOperationTypeList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    @Override
    public UpUser getOwner() {
        return owner;
    }

    @Override
    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

//    public UpApplication getApplication() {
//        return application;
//    }
//
//    public void setApplication(UpApplication application) {
//        this.application = application;
//    }
    
    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public UpOperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(UpOperationType operationType) {
        this.operationType = operationType;
    }

    public UpOperationLevel getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(UpOperationLevel operationLevel) {
        this.operationLevel = operationLevel;
    }

    public UpOperationStatus getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(UpOperationStatus operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getTargetTable() {
        return targetTable;
    }

    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable;
    }

    public Integer getTargetId() {
        return targetId;
    }

    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorStackTrace() {
        return errorStackTrace;
    }

    public void setErrorStackTrace(String errorStackTrace) {
        this.errorStackTrace = errorStackTrace;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public Date getRequestTm() {
        return requestTm;
    }

    public void setRequestTm(Date requestTm) {
        this.requestTm = requestTm;
    }

    public Integer getCostMS() {
        return costMS;
    }

    public void setCostMS(Integer costMS) {
        this.costMS = costMS;
    }
}
