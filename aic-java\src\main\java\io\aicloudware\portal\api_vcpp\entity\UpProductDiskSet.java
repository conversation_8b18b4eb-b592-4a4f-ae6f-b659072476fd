package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductDiskSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;

@Entity
@Table(name = "up_product_disk_set")
@Access(AccessType.FIELD)
public class UpProductDiskSet extends BaseUpEntity<UpProductDiskSetBean> {

	private static final long serialVersionUID = -2070501743577914098L;

	@Column(name = "product_code")
	private String productCode;
	
	@Column(name = "payment_type")
	private String paymentType;

	@JoinColumn(name = "disk_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem diskProductItem;

	@Column(name = "unit")
	private Integer unit;
	
	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "max_value")
	private Integer maxValue;
	
	@Column(name = "min_value")
	private Integer minValue;
	
	@Column(name = "step")
	private Integer step;
	
	@Column(name = "enabled")
	private Boolean enabled;
	
	@Column(name = "disk_type")
    @Enumerated(EnumType.STRING)
    private DiskType diskType;
	
	@Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ProductDiskSetType type;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public UpProductItem getDiskProductItem() {
		return diskProductItem;
	}

	public void setDiskProductItem(UpProductItem diskProductItem) {
		this.diskProductItem = diskProductItem;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}

	public DiskType getDiskType() {
		return diskType;
	}

	public void setDiskType(DiskType diskType) {
		this.diskType = diskType;
	}

	public ProductDiskSetType getType() {
		return type;
	}

	public void setType(ProductDiskSetType type) {
		this.type = type;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

}
