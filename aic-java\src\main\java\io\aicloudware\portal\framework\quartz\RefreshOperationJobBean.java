package io.aicloudware.portal.framework.quartz;

import java.util.List;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.utility.Logger;

public class RefreshOperationJobBean extends QuartzJobBean {
    protected final Logger logger = Logger.getLogger(getClass());

    @Autowired
    private IUpSystemConfigService configService;
    
    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        List<JobExecutionContext> jobs;
        try {
            jobs = jobExecutionContext.getScheduler().getCurrentlyExecutingJobs();
            for (JobExecutionContext job : jobs) {
                if (job.getTrigger().equals(jobExecutionContext.getTrigger())
                        && !job.getFireInstanceId().equals(jobExecutionContext.getFireInstanceId())) {
//                    logger.debug("There's another instance running, so leaving " + this);
                    return;
                }
            }
        } catch (SchedulerException e) {
            logger.trace("TaskJobBean.executeInternal() error", e);
            return;
        }
        ThreadCache.setSystemAdminLogin();
        configService.refreshConfig(UpSystemConfigKey.operation_control);
    }
}
