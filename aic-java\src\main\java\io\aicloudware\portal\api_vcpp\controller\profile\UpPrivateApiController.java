package io.aicloudware.portal.api_vcpp.controller.profile;

import io.aicloudware.portal.api_up.service.IUpTaskService;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.framework.bean.PageBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.utility.IpUtil;
import io.aicloudware.portal.platform_vcd.service.ISpOrgService;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/privateApi")
public class UpPrivateApiController extends BaseController {

	private Long ESTIMATED_PROVISIONING_TIME_IN_SECOND=300L;
	
	@Autowired
    protected ISpOrgService spTenantService;

	@Autowired
	private IUpTaskService upTaskService;

	@RequestMapping(value = "/redeploy/{id}", method = RequestMethod.GET)
	@ResponseBody
	public ResponseBean createOrg(@ApiParam(value = "对象ID") @PathVariable String id,HttpServletRequest request) {
		if(!IpUtil.checkIpAddress(request, ipWhiteList)) {
			throw new RuntimeException();
		}
		upTaskService.redeploy(Integer.valueOf(id));
		return ResponseBean.success(true);
	}
	
//	/**
//	 * 配置
//	 * @param resourceType
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/init")
//    @ResponseBody
//	public ResponseBean init(HttpServletRequest request,Integer orgId) {
//		this.checkIpAddress(request);
//		Map<String,Object> datas = new HashMap<>();
//        
//        UpProductVmSetBean[] vmSets = productService.queryVmSetByOrg(orgId);
//        
//        datas.put("vmSets", vmSets);
//        
//		return ResponseBean.success(datas);
//	}
//	
//	@RequestMapping(value = "/saveCloudServer", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean save(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
//		this.checkIpAddress(request);
//		ThreadCache.setUserLogin(bean.getOwnerId());
//		if(bean.getCloudDiskList()!=null && bean.getCloudDiskList().length > 0) {
//			// TODO  无用
////			UpProductDiskSetBean diskSet = productService.getDiskSet();
//			UpProductDiskSetBean diskSet = null;
//			for(UpOrderCloudDiskBean disk : bean.getCloudDiskList()) {
//				disk.setDiskConfigId(diskSet.getId());
//			}
//		}
//		
//		if(bean.getElasticIpList()!=null && bean.getElasticIpList().length > 0 && bean.getElasticIpList()[0]!=null) {
//			UpProductBandwidthSetBean bandWidthSet = productService.getBandwidthSetByOrg(ThreadCache.getOrgId());
//			bean.getElasticIpList()[0].setBandwidthConfigId(bandWidthSet.getId());
//		}
//		
//		return ResponseBean.success(cloudServerService.save(bean));
//	}
//	
//	@RequestMapping(value = "/queryVm", method = RequestMethod.POST)
//    @ApiOperation(notes = "/queryVm", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryVm(@ApiParam(value = "查询条件") @RequestBody SpVmSearchBean searchBean,HttpServletRequest request) {
//		checkIpAddress(request);
//		ThreadCache.setUserLogin(searchBean.getBean().getOwnerId());
//		SpVm entity = BeanCopyUtil.copy(searchBean.getBean(), SpVm.class);
//        entity.setSpOrg(new SpOrg());
//        entity.getSpOrg().setId(ThreadCache.getOrgId());
//        entity.setOwner(null);
//        if(StringUtils.isNotEmpty(entity.getName())) {
//        	SpVmBean fuzzyBean = new SpVmBean();
//        	fuzzyBean.setName(entity.getName());
//        	fuzzyBean.setIpAddress(entity.getName());
//        	entity.setName(null);
//        	searchBean.setFuzzyBean(fuzzyBean);
//        }
//        
//        try {
//            SpVmBean[] entityList = spVmService.query(searchBean, entity);
//            SpVmResultBean result = new SpVmResultBean();
//            fillPageInfo(searchBean, result);
//            result.setDataList(entityList);
//            return ResponseBean.success(result);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return ResponseBean.success(null);
//    }
//	
//	@RequestMapping(value = "/queryLoadBalancer", method = RequestMethod.POST)
//    @ApiOperation(notes = "/queryLoadBalancer", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpLoadBalancerResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryLoadBalancer(@ApiParam(value = "查询条件") @RequestBody SpLoadBalancerSearchBean searchBean,HttpServletRequest request) {
//		checkIpAddress(request);
//    	if (searchBean.getBean() == null) {
//    		searchBean.setBean(new SpLoadBalancerBean());
//    	}
//    	SpLoadBalancer entity = BeanCopyUtil.copy(searchBean.getBean(), SpLoadBalancer.class);
//    	AssertUtil.check(entity.getSpOrg() != null && entity.getSpOrg().getId() != null, "租户信息异常！");
//    	SpOrg org = entity.getSpOrg();
//    	
//    	SpLoadBalancerBean[] entityArray = commonService.query(searchBean, entity,SpLoadBalancerBean.class);
//    	List<SpLoadBalancerBean> entityList = new ArrayList<>();
//    	for (SpLoadBalancerBean bean: entityArray) {
//    		if (bean.getSpOrgId()==org.getId()) {
//    			entityList.add(bean);
//    		}
//    	}
//
//    	SpVmSearchBean vmSearchBean = new SpVmSearchBean();
//    	vmSearchBean.setPageSize(Integer.MAX_VALUE);
////    	vmSearchBean.getBean().setSpTenantId(ThreadCache.getOrgId());
//        SpVm vm = BeanCopyUtil.copy(vmSearchBean.getBean(), SpVm.class);
//        vm.setSpOrg(org);
//        if(StringUtils.isNotEmpty(entity.getName())) {
//        	SpLoadBalancerBean fuzzyBean = new SpLoadBalancerBean();
//        	fuzzyBean.setName(entity.getName());
//        	entity.setName(null);
//        	searchBean.setFuzzyBean(fuzzyBean);
//        }
//        //entity.getSpOrg().setId(3);
//        SpVmBean[] vmEntityList = spVmService.query(vmSearchBean, vm);
//        Map<String, String> vmIpToNameMap = new HashMap<>();
//        for (SpVmBean vmBean:vmEntityList) {
//        	vmIpToNameMap.put(vmBean.getIpAddress(), vmBean.getName());
//        }
//        
//        for (SpLoadBalancerBean lb:entityList) {
//        	if (lb.getLoadBalancerPoolList() != null) {
//        		for (SpLoadBalancerPoolBean pb:lb.getLoadBalancerPoolList()) {
//        			if (pb.getLoadBalancerMemberList() != null) {
//        				for (SpLoadBalancerMemberBean lbMember:pb.getLoadBalancerMemberList()) {
//        					String name = vmIpToNameMap.get(lbMember.getIpAddress());
//        					if (name == null) {
//        						lbMember.setVmName("");
//        					} else {
//        						lbMember.setVmName(name);
//        					}
//        					lbMember.setHealthStatus("UP");
//        					
//        				}
//        			}
//        		}
//        	}
//        }
//        
//        
//		ResultListBean<SpLoadBalancerBean> result = Utility.newInstance(SpLoadBalancerResultBean.class);
//		fillPageInfo(searchBean, result);
//		result.setDataList(entityList.toArray(new SpLoadBalancerBean[0]));
//		return ResponseBean.success(result);
//    }
//
//	@RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class)})
//    @ResponseBody
//    public ResponseBean getVm(@ApiParam(value = "对象ID") @PathVariable Integer id,HttpServletRequest request) {
//		checkIpAddress(request);
//        SpVmBean bean = commonService.load(SpVm.class, SpVmBean.class, id);
//        bean.setOsPassword(EncryptUtil.decryptWithRSA(bean.getOsPassword()));
//        if (SpDeployStatus.INIT.equals(bean.getDeployStatus())) {
//            int progres = 0;
//            long provisioningTime = System.currentTimeMillis()-bean.getCreateTm().getTime();
//            if (provisioningTime<0) {
//                provisioningTime = 0;
//            }
//            progres = (int)(100-(ESTIMATED_PROVISIONING_TIME_IN_SECOND-provisioningTime/1000)*100/ESTIMATED_PROVISIONING_TIME_IN_SECOND);
//            if (progres>=99) {
//                progres = 99;
//            }
//            bean.setProgres(progres);
//        } else if (SpDeployStatus.COMPLETE.equals(bean.getDeployStatus())) {
//            bean.setProgres(100);
//        } else if (SpDeployStatus.ERROR.equals(bean.getDeployStatus())) {
//            bean.setProgres(99);
//        }
//        return ResponseBean.success(bean);
//    }
//	
//	@RequestMapping(value = "/power_off/{id}", method = RequestMethod.POST)
//    @ApiOperation(notes = "/power_off/{id}", httpMethod = "POST", value = "虚拟机关机")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
//    @ResponseBody
//    public ResponseBean powerOff(@ApiParam(value = "对象ID") @PathVariable String id , HttpServletRequest request) {
//		checkIpAddress(request);
//        return ResponseBean.success(spVmService.powerOff(id));
//    }
//	
//	@RequestMapping(value = "/deleteVm", method = RequestMethod.POST)
//    @ApiOperation(notes = "/deleteVm", httpMethod = "POST", value = "删除虚机")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
//    @ResponseBody
//    public ResponseBean deleteVm(@RequestBody SpVmBean bean, HttpServletRequest request) {
//		checkIpAddress(request);
//		ThreadCache.setUserLogin(bean.getOwnerId());
//        return ResponseBean.success(spVmService.deleteVm(bean.getSpUuid()));
//    }
//	
//	@RequestMapping(value = "/getLoadBalance/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/getLoadBalance/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpLoadBalancerBean.class)})
//    @ResponseBody
//    public ResponseBean getLoadBalance(@ApiParam(value = "对象ID") @PathVariable Integer id, HttpServletRequest request) {
//		checkIpAddress(request);
//		return ResponseBean.success(commonService.load(SpLoadBalancer.class, SpLoadBalancerBean.class, id));
//    }
//	
//	@RequestMapping(value = "/update_pool/{id}", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_pool/{id}", httpMethod = "POST", value = "修改池实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpLoadBalancerPoolBean.class)})
//    @ResponseBody
//    public ResponseBean updatePool(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                          @ApiParam(value = "实例对象") @Valid @RequestBody SpLoadBalancerPoolBean bean,
//                                          BindingResult bindingResult, HttpServletRequest request) {
//		checkIpAddress(request);
//    	SpLoadBalancerPool loadBalancerPool = BeanCopyUtil.copy(bean, SpLoadBalancerPool.class);
//    	loadBalancerService.updateLBPool(loadBalancerPool);
//        return ResponseBean.success("");
//    }
//	
//	@RequestMapping(value = "/addVPC", method = RequestMethod.POST)
//    @ApiOperation(notes = "/addVPC", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVPCBean.class)})
//    @ResponseBody
//    public ResponseBean addVPC(@ApiParam(value = "实例对象") @Valid @RequestBody SpVPCBean bean, HttpServletRequest request) {
//		checkIpAddress(request);
//        SpVPC spVPC = BeanCopyUtil.copy(bean, SpVPC.class);
//        spVPC.setSpUuid(spVPC.getName());
//        spVPC.setSpOrg(commonService.load(SpOrg.class, bean.getSpOrgId()));
//        List<SpOVDCNetwork> ovdcNetworks = new ArrayList<>();
//        SpVPCBean vpcBean = BeanCopyUtil.copy2Bean(spVPC, SpVPCBean.class);
//
//        for (SpOVDCNetworkBean ovdcNetworkBean : bean.getOvdcNetworkList()) {
//            SpOVDCNetwork ovdcNetwork = BeanCopyUtil.copy(ovdcNetworkBean, SpOVDCNetwork.class);
//            ovdcNetworks.add(ovdcNetwork);
//        }
//        // spVPC = vpcService.add(spVPC);
//        List<SpOVDCNetwork> ovdcNetworkList = vpcService.addNetwork(spVPC, ovdcNetworks);
//        
//        // SpVPCBean vpcBean = BeanCopyUtil.copy2Bean(spVPC, SpVPCBean.class);
//        List<SpOVDCNetworkBean> ovdcNetworkBeanList = new ArrayList<>();
//        for (SpOVDCNetwork ovdcNetwork : ovdcNetworkList) {
//            ovdcNetworkBeanList.add(BeanCopyUtil.copy2Bean(ovdcNetwork, SpOVDCNetworkBean.class));
//        }
//        vpcBean.setOvdcNetworkList(ovdcNetworkBeanList.toArray(new SpOVDCNetworkBean[0]));
//        return ResponseBean.success(vpcBean);
//    }
//	
//	@RequestMapping(value = "/deleteVPC/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/deleteVPC/{id}", httpMethod = "GET", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVPCBean.class)})
//    @ResponseBody
//    public ResponseBean deleteVPC(@ApiParam(value = "对象ID") @PathVariable Integer id, HttpServletRequest request) {
//		checkIpAddress(request);
//		vpcService.deleteVPC(id);
//        return ResponseBean.success(true);
//    }
//	
//	@RequestMapping(value = "/deleteVPCList", method = RequestMethod.POST)
//	@ApiOperation(notes = "/deleteVPCList", httpMethod = "POST", value = "批量删除实例")
//	@ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//	@ResponseBody
//	public ResponseBean deleteVPCList(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, HttpServletRequest request) {
//		checkIpAddress(request);
//		for(Integer id : bean.getIdList()) {
//			vpcService.deleteVPC(id);
//		}
//        return ResponseBean.success(true);
//	}
//	
//	@RequestMapping(value = "/{id}/metrics/{peroid}/{ownerId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/{id}/metrics/{peroid}/{ownerId}", httpMethod = "GET", value = "查询监控数据")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回类型", response = ArrayList.class)})
//    @ResponseBody
//    public ArrayList<SpMetricsBean> metrics(@ApiParam(value = "对象ID") @PathVariable Integer id, @ApiParam(value = "时间") @PathVariable Integer peroid, @ApiParam(value = "所有者") @PathVariable Integer ownerId, HttpServletRequest request) {
//		checkIpAddress(request);
//		ThreadCache.setUserLogin(ownerId);
//    	ArrayList<SpMetricsBean> list = elasticIpService.queryIpHistoryMetrics(id, new Float(peroid)/100);
//        return list;
//    }
//	
//	
	private void fillPageInfo(PageBean orig, PageBean dest) {
        dest.setPageNum(orig.getPageNum());
        dest.setPageSize(orig.getPageSize());
        dest.setPageCount(orig.getPageCount());
        dest.setRecordCount(orig.getRecordCount());
    }
}
