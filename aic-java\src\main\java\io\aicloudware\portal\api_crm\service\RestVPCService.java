package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_crm.framework.bean.RestV2SubnetBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpOVDCNetworkBean;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpIpRange;
import io.aicloudware.portal.platform_vcd.entity.SpIpScope;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.service.ISpVPCService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@Transactional
public class RestVPCService extends BaseService implements IRestVPCService {

	@Autowired
	private ISpVPCService spVPCService;


	@Override
	public SpOVDCNetworkBean addNetwork(RestV2SubnetBean bean) {
		SpVPC vpc = dao.load(SpVPC.class, bean.getVpcId().intValue());
		buildSubnet(bean, vpc);
		SpIpRange spIpRange = new SpIpRange();
		spIpRange.setIpBegin(bean.getIpBegin());
		spIpRange.setIpEnd(bean.getIpEnd());
		spIpRange.setRegion(ThreadCache.getRegion());

		SpIpScope spIpScope = new SpIpScope();
		spIpScope.setGateway(bean.getGateway());
		spIpScope.setNetMask(bean.getNetMask());
		spIpScope.setDns1(bean.getDns1());
		spIpScope.setDns2(bean.getDns2());
		spIpScope.setIpRangeList(Collections.singletonList(spIpRange));
		spIpScope.setRegion(ThreadCache.getRegion());

		SpOVDCNetwork ovdcNetwork = new SpOVDCNetwork();
		ovdcNetwork.setName(bean.getName());
		ovdcNetwork.setRegion(ThreadCache.getRegion());
		ovdcNetwork.setIpScopeList(Collections.singletonList(spIpScope));

		List<SpOVDCNetwork> ovdcNetworkList = spVPCService.addNetwork(vpc, Collections.singletonList(ovdcNetwork));
		return BeanCopyUtil.copy(ovdcNetworkList.get(0), SpOVDCNetworkBean.class);
	}

	private void buildSubnet(RestV2SubnetBean bean, SpVPC vpc){
		String[] segments = bean.getNetworkSegment().split("/");
		String maskBit = segments[1];
		String[] values = segments[0].split("\\.");
		Integer ipItem01 = Integer.valueOf(values[0]);
		Integer ipItem02 = Integer.valueOf(values[1]);
		Integer ipItem03 = Integer.valueOf(values[2]);
		Integer ipItem04 = Integer.valueOf(values[3]);

		if(StringUtils.isNotEmpty(vpc.getNetworkSegment())){
			AssertUtil.check(Integer.valueOf(vpc.getNetworkSegment().split("\\.")[0]).compareTo(ipItem01), "当前子网网段在VPC:" + vpc.getName() + " 中不可用，请重试");
		}else{
			vpc.setNetworkSegment(bean.getNetworkSegment());
			dao.update(vpc, "networkSegment");
		}

		if(ipItem01.compareTo(192) == 0){
			AssertUtil.check(ipItem02.compareTo(168) == 0 &&
							ipItem03.compareTo(0) >= 0 && ipItem03.compareTo(253) <= 0 &&
							ipItem04.compareTo(0) == 0 && maskBit.equals("24"),
					"子网网段参数异常！");
		}else if(ipItem01.compareTo(172) == 0){
			if (maskBit.equals("16")) {
			AssertUtil.check(ipItem02.compareTo(16) >= 0 &&
							ipItem02.compareTo(31) <= 0 && ipItem03.compareTo(0) == 0 &&
							ipItem04.compareTo(0) == 0,
					"子网网段参数异常！");
			} else if (maskBit.equals("24")) {
				AssertUtil.check(ipItem02.compareTo(16) >= 0 &&
								ipItem02.compareTo(31) <= 0 &&
								ipItem03.compareTo(0) >= 0 && ipItem03.compareTo(255) <=0 &&
								ipItem04.compareTo(0) == 0,
						"子网网段参数异常！");
			}
		}else{
			AssertUtil.check(false, "子网网段参数异常！");
		}

		if(maskBit.equals("16")){
			bean.setNetMask("***********");
			bean.setGateway(ipItem01 + "." + ipItem02 + ".0.1");
			bean.setIpBegin(ipItem01 + "." + ipItem02 + ".0.10");
			bean.setIpEnd(ipItem01 + "." + ipItem02 + ".253.250");
		}else{
			bean.setNetMask("*************");
			bean.setGateway(ipItem01 + "." + ipItem02 + "." + ipItem03 + ".1");
			bean.setIpBegin(ipItem01 + "." + ipItem02 + "." + ipItem03 + ".10");
			bean.setIpEnd(ipItem01 + "." + ipItem02 + "." + ipItem03 + ".250");
		}
	}
}
