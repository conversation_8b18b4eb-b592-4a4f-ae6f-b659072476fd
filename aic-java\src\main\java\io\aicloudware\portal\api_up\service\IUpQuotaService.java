package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpQuota;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaListBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaSearchBean;
import io.aicloudware.portal.framework.sdk.contants.SpResourceType;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.service.ITaskRunnable;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public interface IUpQuotaService extends ITaskRunnable {

    void notifyRefreshUsage();

    void sync(Integer orgId, SpRegionEntity region);

    UpQuotaBean[] queryTemplate(UpQuotaSearchBean searchBean);

    void saveTemplate(UpQuotaBean bean);

    UpQuotaBean adminDetail(Integer id);

    void userQuotaSave(UpQuotaListBean listBean);

    List<UpQuotaBean> overviewQuota(Integer id);

    void checkQuota(SpService service, SpResourceType resourceType, Integer appSystemId, Integer orgId, Integer quotaToAdd, Integer usedQuota) ;

    UpQuota syncQuotaUsage(SpService service, SpResourceType resourceType, Integer appSystemId, Integer orgId, Integer usedQuota);

    UpQuotaBean userDetail(String resourceType);

    SpService getService(SpResourceType type);

    List<UpQuotaBean> appSystemQuota(Integer appSystemId);
}
