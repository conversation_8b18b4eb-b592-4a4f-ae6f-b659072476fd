package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "K8S集群节点池")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderK8sClusterNodePoolBean.class})
public class UpOrderK8sClusterNodePoolBean extends RecordBean {

	@ApiModelProperty(value = "规格")
	private String flavor;

	@ApiModelProperty(value = "集群ID")
	private Integer clusterId;

	@ApiModelProperty(value = "集群名称")
	private String clusterName;

	@ApiModelProperty(value = "资源区域")
	private String resourceRegion;

	@ApiModelProperty(value = "密码")
	private String password;

	@ApiModelProperty(value = "根卷大小")
	private Integer rootVolumeSize;

	@ApiModelProperty(value = "数据卷大小列表")
	private String dataVolumeSize;

	public String getFlavor() {
		return flavor;
	}

	public void setFlavor(String flavor) {
		this.flavor = flavor;
	}

	public Integer getClusterId() {
		return clusterId;
	}

	public void setClusterId(Integer clusterId) {
		this.clusterId = clusterId;
	}

	public String getClusterName() {
		return clusterName;
	}

	public void setClusterName(String clusterName) {
		this.clusterName = clusterName;
	}

	public String getResourceRegion() {
		return resourceRegion;
	}

	public void setResourceRegion(String resourceRegion) {
		this.resourceRegion = resourceRegion;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Integer getRootVolumeSize() {
		return rootVolumeSize;
	}

	public void setRootVolumeSize(Integer rootVolumeSize) {
		this.rootVolumeSize = rootVolumeSize;
	}

	public String getDataVolumeSize() {
		return dataVolumeSize;
	}

	public void setDataVolumeSize(String dataVolumeSize) {
		this.dataVolumeSize = dataVolumeSize;
	}
}
