package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpQuotaLog;
import io.aicloudware.portal.api_up.service.IUpQuotaLogService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


@Controller
@RequestMapping("/quota_log")
@Api(value = "/quota_log", description = "资源配额", position = 508)
public class UpQuotaLogController extends BaseUpController<UpQuotaLog, UpQuotaLogBean, UpQuotaLogResultBean> {

    @Autowired
    private IUpQuotaLogService upQuotaLogService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpQuotaLogSearchBean searchBean) {
        UpQuotaLog entity = BeanCopyUtil.copy(searchBean.getBean(), UpQuotaLog.class);
        entity.setRegion(ThreadCache.getRegion());
        SpOrg org = commonService.load(SpOrg.class, ThreadCache.getOrgId());
        entity.setOrg(org);
        if (StringUtils.isNotEmpty(entity.getName())) {
            UpQuotaLogBean fuzzyBean = new UpQuotaLogBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            UpQuotaLogBean[] entityList = upQuotaLogService.query(searchBean, entity);
            UpQuotaLogResultBean result = new UpQuotaLogResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(notes = "/save", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean save(@ApiParam(value = "查询条件") @RequestBody UpQuotaLogBean bean) {
        upQuotaLogService.save(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean delete(@PathVariable Integer id) {
        return ResponseBean.success(null);
    }

}
