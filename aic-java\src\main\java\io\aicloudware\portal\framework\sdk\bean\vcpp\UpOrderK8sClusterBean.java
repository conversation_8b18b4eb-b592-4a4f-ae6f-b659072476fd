package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterContainerNetworkMode;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "K8S集群")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderK8sClusterBean.class})
public class UpOrderK8sClusterBean extends RecordBean {

	@ApiModelProperty(value = "规格")
	private String flavor;

	@ApiModelProperty(value = "主机网络VPCID")
	private Integer hostNetworkVpcId;

	@ApiModelProperty(value = "主机网络子网ID")
	private Integer hostNetworkSubnetId;

	@ApiModelProperty(value = "主机网络安全组ID")
	private Integer hostNetworkSecurityGroupId;

	@ApiModelProperty(value = "容器网络模式")
	private SpK8sClusterContainerNetworkMode containerNetworkMode;

	@ApiModelProperty(value = "集群ID")
	private Integer clusterId;

	@ApiModelProperty(value = "集群名称")
	private String clusterName;

	public String getFlavor() {
		return flavor;
	}

	public void setFlavor(String flavor) {
		this.flavor = flavor;
	}

	public Integer getHostNetworkVpcId() {
		return hostNetworkVpcId;
	}

	public void setHostNetworkVpcId(Integer hostNetworkVpcId) {
		this.hostNetworkVpcId = hostNetworkVpcId;
	}

	public Integer getHostNetworkSubnetId() {
		return hostNetworkSubnetId;
	}

	public void setHostNetworkSubnetId(Integer hostNetworkSubnetId) {
		this.hostNetworkSubnetId = hostNetworkSubnetId;
	}

	public Integer getHostNetworkSecurityGroupId() {
		return hostNetworkSecurityGroupId;
	}

	public void setHostNetworkSecurityGroupId(Integer hostNetworkSecurityGroupId) {
		this.hostNetworkSecurityGroupId = hostNetworkSecurityGroupId;
	}

	public SpK8sClusterContainerNetworkMode getContainerNetworkMode() {
		return containerNetworkMode;
	}

	public void setContainerNetworkMode(SpK8sClusterContainerNetworkMode containerNetworkMode) {
		this.containerNetworkMode = containerNetworkMode;
	}

	public Integer getClusterId() {
		return clusterId;
	}

	public void setClusterId(Integer clusterId) {
		this.clusterId = clusterId;
	}

	public String getClusterName() {
		return clusterName;
	}

	public void setClusterName(String clusterName) {
		this.clusterName = clusterName;
	}
}
