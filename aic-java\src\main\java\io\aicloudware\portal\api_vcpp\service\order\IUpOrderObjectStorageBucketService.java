package io.aicloudware.portal.api_vcpp.service.order;

import org.springframework.web.multipart.MultipartFile;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.SpObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderObjectStorageBucketBean;

public interface IUpOrderObjectStorageBucketService {
	
	SpObjectStorageBucketBean[] queryObjectStorageBucket(Integer userId);
	/**
	 * 保存
	 * @param bean
	 * @return
	 */
	Integer save(UpOrderObjectStorageBucketBean bean, UpUser applyUser);
	
	Integer change(UpOrderObjectStorageBucketBean bean, UpUser applyUser);
	
	Integer quotaSave(UpOrderObjectStorageBucketBean bean, UpUser applyUser);
	
	String createBucket(UpOrderObjectStorageBucketBean bean, UpUser user) ;
	
	String uploadBucketFile(UpOrderObjectStorageBucketBean bean, UpUser applyUser, MultipartFile file);	

}
