package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmProvisionType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "外部虚机")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpExtVmBean.class})
public class SpExtVmBean extends SpRecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "虚机说明")
    private String vmComment;

    @ApiModelProperty(value = "CPU(核)")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存(GB)")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘（GB）")
    private Integer diskGB;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "创建时间")
    private Date createDt;

    @ApiModelProperty(value = "到期时间")
    private Date expireDt;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "电源状态")
    private SpVmPowerStatus powerStatus;

    @ApiModelProperty(value = "置备类型(THIN,THICK)")
    private SpVmProvisionType provisionType;

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;

    @ApiModelProperty(value = "虚机创建时间", position = 170)
    private Date createTm;
    
    @ApiModelProperty(value = "UUID")
    private String spUuid;

    @ApiModelProperty(value = "主机名")
    private String hostName;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public String getVmComment() {
        return vmComment;
    }

    public void setVmComment(String vmComment) {
        this.vmComment = vmComment;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    public Date getExpireDt() {
        return expireDt;
    }

    public void setExpireDt(Date expireDt) {
        this.expireDt = expireDt;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public SpVmPowerStatus getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(SpVmPowerStatus powerStatus) {
        this.powerStatus = powerStatus;
    }

    public SpVmProvisionType getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(SpVmProvisionType provisionType) {
        this.provisionType = provisionType;
    }

//    public String getRegion() {
//        return region;
//    }
//
//    public void setRegion(String region) {
//        this.region = region;
//    }
//
//    public String getDomain() {
//        return domain;
//    }
//
//    public void setDomain(String domain) {
//        this.domain = domain;
//    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

    public String getSpUuid() {
        return spUuid;
    }

    public void setSpUuid(String spUuid) {
        this.spUuid = spUuid;
    }

}
