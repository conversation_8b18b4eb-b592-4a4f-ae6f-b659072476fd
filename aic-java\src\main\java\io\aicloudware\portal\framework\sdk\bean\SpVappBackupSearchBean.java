package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vAppBackup查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVappBackupSearchBean extends SearchBean<SpVappBackupBean> {

    private Boolean onlySearchUnManaged = Boolean.FALSE;

    @ApiModelProperty(value = "部署ID列表", hidden = true)
    private Integer[] deploymentIdList;

    @ApiModelProperty(value = "申请单ID列表", hidden = true)
    private Integer[] applicationIdList;

    public Boolean getOnlySearchUnManaged() {
        return onlySearchUnManaged;
    }

    public void setOnlySearchUnManaged(Boolean onlySearchUnManaged) {
        this.onlySearchUnManaged = onlySearchUnManaged;
    }

    public Integer[] getDeploymentIdList() {
        return deploymentIdList;
    }

    public void setDeploymentIdList(Integer[] deploymentIdList) {
        this.deploymentIdList = deploymentIdList;
    }

    public Integer[] getApplicationIdList() {
        return applicationIdList;
    }

    public void setApplicationIdList(Integer[] applicationIdList) {
        this.applicationIdList = applicationIdList;
    }
}
