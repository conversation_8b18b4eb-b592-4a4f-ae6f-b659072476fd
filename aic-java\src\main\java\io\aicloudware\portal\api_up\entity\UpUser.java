package io.aicloudware.portal.api_up.entity;

import java.util.Arrays;
import java.util.Set;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.aicloudware.portal.api_rest.framework.entity.RestCustom;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.hibernate.criterion.DetachedCriteria;

import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.entity.IDisplayNameEntity;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserSearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.criterion.Restrictions;

@Entity
@Table(name = "up_user")
@Access(AccessType.FIELD)
public final class UpUser extends BaseEntity<UpUserBean> implements IDisplayNameEntity<UpUserBean> {

	@Column(name = "display_name")
    private String displayName;
	
	@Column(name = "password")
    private String password;
	
    @Column(name = "email")
    private String email;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "telephone")
    private String telephone;

    @Column(name = "comment",length = ApiConstants.STRING_MAX_LENGTH)
    private String comment;

    @Column(name = "uc_id")
    private Integer ucId;
    
    @Column(name = "sso_user_id")
    private String ssoUserId;
    
    @Column(name = "username")
    private String username;
    
    @Column(name = "is_system_admin")
    private Boolean systemAdmin;
    
    @Column(name = "is_tenant_admin")
    private Boolean tenantAdmin;
    
    @Column(name = "is_delete")
    private Boolean isDelete;
    
    @Column(name = "is_verify")
    private Boolean isVerify;
    
    @Column(name = "type")
    private String type;

	@JoinColumn(name = "org_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpOrg org;

	@JoinColumn(name = "custom_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private RestCustom custom;
    
    @Column(name = "is_arrearage")
    private Boolean isArrearage;
    
    @Column(name = "suspend")
    private Boolean suspend;

	@Transient
    private SpRegionEntity region;
    
    @Transient
    private String timeMillis;
    
    @Column(name = "failure_number")
    private Integer failureNumber;
    
    @Column(name = "failure_time")
    private Long failureTime;
    
    public UpUser() {
    }

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpUserBean> searchBean, Set<String> aliasSet) {
        UpUserSearchBean bean = (UpUserSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getStatusList())) {
            DaoUtil.addInValues(criteria, "status", Arrays.asList(bean.getStatusList()));
        }
		criteria.add(Restrictions.ne("name", "system-admin"));
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }
    
    public UpUser(Integer id) {
        super(id);
    }

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Integer getUcId() {
		return ucId;
	}

	public void setUcId(Integer ucId) {
		this.ucId = ucId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Boolean getTenantAdmin() {
		return tenantAdmin;
	}

	public void setTenantAdmin(Boolean tenantAdmin) {
		this.tenantAdmin = tenantAdmin;
	}

	public Boolean getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}

	public Boolean getIsVerify() {
		return isVerify;
	}

	public void setIsVerify(Boolean isVerify) {
		this.isVerify = isVerify;
	}

	public Boolean getSystemAdmin() {
		return systemAdmin;
	}

	public void setSystemAdmin(Boolean systemAdmin) {
		this.systemAdmin = systemAdmin;
	}

	public SpOrg getOrg() {
		return org;
	}

	public void setOrg(SpOrg org) {
		this.org = org;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Boolean getIsArrearage() {
		return isArrearage;
	}

	public void setIsArrearage(Boolean isArrearage) {
		this.isArrearage = isArrearage;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Boolean getSuspend() {
		return suspend;
	}

	public void setSuspend(Boolean suspend) {
		this.suspend = suspend;
	}

	public String getSsoUserId() {
		return ssoUserId;
	}

	public void setSsoUserId(String ssoUserId) {
		this.ssoUserId = ssoUserId;
	}

	public SpRegionEntity getRegion() {
		return region;
	}

	public void setRegion(SpRegionEntity region) {
		this.region = region;
	}

	public String getTimeMillis() {
		return timeMillis;
	}

	public void setTimeMillis(String timeMillis) {
		this.timeMillis = timeMillis;
	}

	public Integer getFailureNumber() {
		return failureNumber;
	}

	public void setFailureNumber(Integer failureNumber) {
		this.failureNumber = failureNumber;
	}

	public Long getFailureTime() {
		return failureTime;
	}

	public void setFailureTime(Long failureTime) {
		this.failureTime = failureTime;
	}

	public RestCustom getCustom() {
		return custom;
	}

	public void setCustom(RestCustom custom) {
		this.custom = custom;
	}
}
