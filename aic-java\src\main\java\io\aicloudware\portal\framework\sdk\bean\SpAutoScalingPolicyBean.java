package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpAutoScalingRemovePolicy;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "弹性伸缩策略")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpAutoScalingPolicyBean.class})
public class SpAutoScalingPolicyBean extends SpRecordBean {

    @ApiModelProperty(value = "最小实例数(台)")
	private Integer minValue;
	
    @ApiModelProperty(value = "最大实例数(台)")
	private Integer maxValue;
	
    @ApiModelProperty(value = "负载均衡ID")
	private Integer spLoadBalancerId;
    
    @ApiModelProperty(value = "负载均衡名称")
	private Integer spLoadBalancerName;
	
    @ApiModelProperty(value = "实例移除策略")
	private SpAutoScalingRemovePolicy removePolicy;
    
    @ApiModelProperty(value = "默认冷却时间(秒)")
    private Long removeCD;

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getSpLoadBalancerId() {
		return spLoadBalancerId;
	}

	public void setSpLoadBalancerId(Integer spLoadBalancerId) {
		this.spLoadBalancerId = spLoadBalancerId;
	}

	public Integer getSpLoadBalancerName() {
		return spLoadBalancerName;
	}

	public void setSpLoadBalancerName(Integer spLoadBalancerName) {
		this.spLoadBalancerName = spLoadBalancerName;
	}

	public SpAutoScalingRemovePolicy getRemovePolicy() {
		return removePolicy;
	}

	public void setRemovePolicy(SpAutoScalingRemovePolicy removePolicy) {
		this.removePolicy = removePolicy;
	}

	public Long getRemoveCD() {
		return removeCD;
	}

	public void setRemoveCD(Long removeCD) {
		this.removeCD = removeCD;
	}
}
