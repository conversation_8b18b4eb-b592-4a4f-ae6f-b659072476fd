package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpApprovalStatus;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "待办审批单查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApprovalHistorySearchBean extends SearchBean<UpApprovalHistoryBean> {

    @ApiModelProperty(value = "审批流程ID列表")
    private Integer[] processIdList;

    @ApiModelProperty(value = "审批状态列表")
    private UpApprovalStatus[] approvalStatusList;

    public Integer[] getProcessIdList() {
        return processIdList;
    }

    public void setProcessIdList(Integer[] processIdList) {
        this.processIdList = processIdList;
    }

    public UpApprovalStatus[] getApprovalStatusList() {
        return approvalStatusList;
    }

    public void setApprovalStatusList(UpApprovalStatus[] approvalStatusList) {
        this.approvalStatusList = approvalStatusList;
    }

}
