package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_crm.framework.bean.RestV2CloudServerBean;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.*;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Transactional
public class RestCloudServerService extends BaseService implements IRestCloudServerService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService upOrderQuotaService;

	@Autowired
	private IUpProductService upProductService;

	@Autowired
	private ISpRegionService spRegionService;

	private UpOrderCloudServerBean buildServerBean(UpOrderQuotaDetail quotaDetail, RestV2CloudServerBean bean){
		UpOrderCloudServerBean cloudServerBean = new UpOrderCloudServerBean();

		Integer cpu = null;
		Integer memory = null;
		Integer disk = null;
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			cpu = quotaDetail.getCpu();
			memory = quotaDetail.getMemoryG();
			disk = quotaDetail.getDiskG();

			UpOrderCloudDiskBean diskBean = new UpOrderCloudDiskBean();
			diskBean.setDiskGB(disk);
			diskBean.setType(SpVmDiskType.system);
			cloudServerBean.setCloudDiskList(new UpOrderCloudDiskBean[]{diskBean});

		}else {
			UpProductVmSetBean vmSetBean = upProductService.getVmSetByCode(quotaDetail.getProductCode());
			AssertUtil.check(vmSetBean, "产品编码异常");
			cpu = vmSetBean.getCpuUnit();
			memory = vmSetBean.getMemoryUnit();
		}

		cloudServerBean.setName(bean.getName());
		cloudServerBean.setOwnerId(ThreadCache.getUserId());
		cloudServerBean.setAccount(bean.getAccount());
		cloudServerBean.setPassword(bean.getPassword());
		cloudServerBean.setHostname(bean.getHostname());
		cloudServerBean.setServerType(bean.getServerType() == null ? UpOrderSystemEnums.ServerType.standard : bean.getServerType());
		cloudServerBean.setCpu(cpu);
		cloudServerBean.setMemory(memory);
		cloudServerBean.setImageId(bean.getImageId());
		cloudServerBean.setNetworkId(bean.getNetworkId());
		cloudServerBean.setVpcId(bean.getVpcId());
		cloudServerBean.setKeyType(bean.getKeyType() == null ? UpOrderSystemEnums.KeyType.password : bean.getKeyType());

		cloudServerBean.setAmount(1);
		return cloudServerBean;
	}

	@Override
	public Integer save(RestV2CloudServerBean restBean) {
		logger.info("ThreadCache.getOrgId() "+ThreadCache.getOrgId() + " ThreadCache.getRegion()"+ThreadCache.getRegion().getName()+ " DoorOrderItemId "+restBean.getDoorOrderItemId());
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), restBean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.NEW
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.SERVER
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion() == ThreadCache.getRegion(),"地市信息异常！");

		UpOrderCloudServerBean bean = this.buildServerBean(quotaDetail, restBean);

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(bean.getServerType(), "请选择云主机类型！");

		AssertUtil.check(bean.getImageId(), "请选择镜像！");
		AssertUtil.check(bean.getVpcId(), "请选择专有网络！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
		AssertUtil.check(bean.getNetworkId(), "请选择子网！");
		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");

		AssertUtil.check(bean.getKeyType(), "请选择密钥方式！");
		if (bean.getKeyType().equals(KeyType.password)) {
			AssertUtil.check(bean.getPassword(), "请输入密码！");
			bean.setAccount(user.getName());
			AssertUtil.check(Pattern.compile("(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,20}").matcher(bean.getPassword()).find(),"密码不符合规则");
		} else if (bean.getKeyType().equals(KeyType.keypair)) {
			AssertUtil.check(bean.getKeyId(), "请选择密钥对！");
		}
		AssertUtil.check(bean.getName(), "请输入实例名！");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getHostname()), "请输入主机名！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getHostname()), "主机名只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getHostname()), "主机名必须包含字母！");
		AssertUtil.check(bean.getHostname().length() <= 15, "主机名长度不能超过15个字符！");
//		AssertUtil.check((bean.getCloudDiskList() != null && bean.getCloudDiskList().length >= 1), "请输入磁盘大小！");
		AssertUtil.check(bean.getAmount() != null && bean.getAmount() >= 1, "请输入购买数量！");
		AssertUtil.check(bean.getAmount() <= 20, "云服务器一次购买数量不能超过20台！");
//		Boolean hasDisk = false;
//		for (UpOrderCloudDiskBean disk : bean.getCloudDiskList()) {
//			if (disk.getType().equals(SpVmDiskType.system) && disk.getDiskGB() != null && disk.getDiskGB() != 0) {
//				hasDisk = true;
//			}
//		}
//		AssertUtil.check(hasDisk, "缺失系统盘！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_cloud_server, user.getId()) == 0, "您有未完成的云服务器申请！");

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_cloud_server);
		order.setName("[" + OrderType.new_cloud_server + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setPaymentType(bean.getPaymentType());
		order.setSpOrg(user.getOrg());
		order.setNumber(bean.getAmount());
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);


		List<UpOrderCloudServer> entitys = new ArrayList<>();
		List<SpVapp> vappEntitys = new ArrayList<>();
		SpVappTemplate template = null;

		for (int i = 0; i < bean.getAmount();) {
			if(template == null){
				template = this.dao.load(SpVappTemplate.class, bean.getImageId());
			}
			String name = bean.getName() + "-" + String.format("%03d", ++i);
			String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));
			UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
			entity.setRegion(order.getRegion());
			entity.setTaskSequence(i*100);
			entity.setOwner(user);
			entity.setName(name + "-" + random);
			entity.setOrder(order);
			entity.setSpOrg(user.getOrg());
			updatePrebuildImage(entity);
			entity.setCpu(bean.getCpu());
			entity.setMemory(bean.getMemory());
			entity.setServerType(bean.getServerType());

			// 处理磁盘
			List<UpOrderCloudDisk> disks = new ArrayList<>();

			UpOrderCloudDiskBean diskBean = null;
			if(bean.getCloudDiskList() != null){
				diskBean = bean.getCloudDiskList()[0];
			}else{
				diskBean = new UpOrderCloudDiskBean();
				diskBean.setDiskGB(template.getDiskSize().intValue());
			}

			if (diskBean.getDiskGB() == null || diskBean.getDiskGB() == 0) {
				Long diskGB = template.getDiskSize()/1024/1024/1024;
				diskBean.setDiskGB(diskGB.intValue());
			}
			UpOrderCloudDisk disk = BeanCopyUtil.copy(diskBean, UpOrderCloudDisk.class);
			disk.setTaskSequence(i*100+0*10);
			disk.setChargeType(CloudStorageChargeType.hour);
			disk.setOrderCloudServer(entity);

			disk.setType(SpVmDiskType.system);
			disk.setDiskType(DiskType.hdd);

//				disk.setType(diskBean.getType() == null ? SpVmDiskType.mount : diskBean.getType());
			disk.setName(name + "-" + random + "-" + 0);
			disk.setDiskNumber(0);
			disk.setOwner(user);
			disk.setPaymentType(bean.getPaymentType());
			disk.setOrder(order);
			disk.setSpOrg(user.getOrg());
			disk.setRegion(order.getRegion());
			disks.add(disk);
			if(disk.getType().equals(SpVmDiskType.system)) {
				order.setSystemDiskNum(disk.getDiskGB());
			}


			entity.setCloudDiskList(disks);

			SpVapp vapp = this.addVapp(template, entity);
			vapp.setOrder(order);
			vappEntitys.add(vapp);
			entity.setVm(vapp.getVmList().get(0));
			entitys.add(entity);
		}

		this.dao.insert(order);
		this.dao.insert(vappEntitys);
		this.dao.insert(entitys);
		for(SpVapp vapp : vappEntitys) {
			for(UpOrderCloudServer orderServer : entitys) {
				if(vapp.getVmList().get(0).getId().equals(orderServer.getVm().getId())) {
					vapp.setOrderCloudServer(orderServer);
					break;
				}
			}
		}
		this.dao.update(vappEntitys, "orderCloudServer");
		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(ThreadCache.getRegion());
		dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
	}

	private void updatePrebuildImage(UpOrderCloudServer entity) {
//		if (SpRegion.isEdgeSite(entity.getRegion())) {
		if(spRegionService.isEdgeSite(entity.getRegion())) {
			logger.info("region:"+entity.getRegion()+" updatePrebuildImage from:"+entity.getImageId());
			SpVappTemplate template = this.dao.load(SpVappTemplate.class, entity.getImageId());
			if (template != null) {
				logger.info("template region "+ template.getRegion().getName() + " ThreadCache.getRegion() " + ThreadCache.getRegion().getName() + " "+ spRegionService.CIDCRP35().equals(template.getRegion()));
			}
			AssertUtil.check(template != null && (spRegionService.CIDCRP35().equals(template.getRegion()) || template.getRegion().equals(ThreadCache.getRegion())), "镜像数据异常");
			String templateName = template.getName();
			String catalogName = template.getCatalog().getName();
			if ("iaas".equals(catalogName) || "redis".equals(catalogName) || "mysql".equals(catalogName) || "mysql_pxc".equals(catalogName)) {
				String prebuildCatalogName = catalogName + "-" + entity.getRegion().name();
				logger.info("prebuild catalog name:"+prebuildCatalogName);
				List<Integer> templateIds = queryDao.querySql("select tplt.id from sp_vapp_template tplt, sp_catalog cat where tplt.catalog_id=cat.id " +
								" and tplt.status='active' and cat.name=:catName and tplt.name=:tpltName",
						MapUtil.of("catName", prebuildCatalogName, "tpltName", templateName));
				if (templateIds != null && templateIds.size()>0) {
					logger.info("found prebuild template id:"+templateIds.get(0));
					entity.setImageId(templateIds.get(0));
				}
			}
		}
	}

	@Override
	public Integer update(RestV2CloudServerBean restBean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), restBean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.UPDATE
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.SERVER
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");

		UpOrderCloudServerBean bean = this.buildServerBean(quotaDetail, restBean);

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");

		AssertUtil.check(bean.getVmId(), "请选择变更虚机！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.cloud_server_update, user.getId()) == 0, "您有未完成的云服务器变更单！");
		SpVm vm = dao.load(SpVm.class, bean.getVmId());
		AssertUtil.check(vm.getSpOrg().getId().equals(ThreadCache.getOrgId()),"云服务器无操作权限！");
		UpOrder order = new UpOrder();
		order.setRegion(vm.getRegion());
		order.setType(OrderType.cloud_server_update);
		order.setName("[" + OrderType.cloud_server_update + "]" + vm.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);

		UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
		entity.setTaskSequence(100);
		entity.setOwner(user);
		entity.setName("update-" + vm.getName() + "-" + System.currentTimeMillis());
		entity.setOrder(order);
		entity.setSpOrg(user.getOrg());
		entity.setCpu(bean.getCpu());
		entity.setMemory(bean.getMemory());
		entity.setServerType(bean.getServerType());
		entity.setVm(vm);
		entity.setUpdateVapp(vm.getSpVapp());
		entity.setRegion(order.getRegion());

		// 处理磁盘
		SpVmDisk dbDisk = vm.getDiskList().stream().filter(d -> {
			return d.getType() == SpVmDiskType.system;
		}).collect(Collectors.toList()).get(0);

		UpOrderCloudDisk disk = BeanCopyUtil.copy(bean.getCloudDiskList()[0], UpOrderCloudDisk.class);
		AssertUtil.check(dbDisk.getDiskGB().compareTo(disk.getDiskGB()) <= 0, "系统盘仅支持扩容。当前系统盘由" +dbDisk.getDiskGB() + "G变更为" + disk.getDiskGB() + "G，操作异常");

		disk.setName(dbDisk.getName());
		disk.setTaskSequence(1*100+1*10);
		disk.setChargeType(CloudStorageChargeType.hour);
		disk.setOrderCloudServer(entity);
		disk.setType(SpVmDiskType.system);
		disk.setDiskType(DiskType.hdd);
		disk.setDiskNumber(0);
		disk.setDiskGB(disk.getDiskGB() == null ? dbDisk.getDiskGB() : disk.getDiskGB());
		disk.setOwner(user);
		disk.setPaymentType(bean.getPaymentType());
		disk.setOrder(order);
		disk.setSpOrg(user.getOrg());
		disk.setRegion(order.getRegion());

		entity.setCloudDiskList(Collections.singletonList(disk));

		this.dao.insert(order);
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
	}

	private SpVapp addVapp(SpVappTemplate template, UpOrderCloudServer cloudServer) {
		SpVapp spVapp = new SpVapp();
		spVapp.setName(cloudServer.getName());
		//spVapp.setOrderCloudServer(cloudServer);
		spVapp.setOwner(cloudServer.getOwner());
		spVapp.setSpOrg(cloudServer.getSpOrg());
		spVapp.setDeployStatus(SpDeployStatus.INIT);

		if (template != null) {
			logger.info("template id "+template.getId() + " region "+template.getRegion() + " "+(template.getRegion() == null || spRegionService.CIDCRP35().equals(template.getRegion()) || template.getRegion().equals(ThreadCache.getRegion())));
		}
		AssertUtil.check(template != null && (template.getRegion() == null || spRegionService.CIDCRP35().equals(template.getRegion()) || template.getRegion().equals(ThreadCache.getRegion())), "镜像数据异常");
		spVapp.setVappTemplate(template);

		List<SpVm> vmList = new ArrayList<SpVm>();
		SpVm spVm = new SpVm();
		spVm.setName(cloudServer.getName());
		spVm.setSpOrg(cloudServer.getSpOrg());
		spVm.setCpuNum(cloudServer.getCpu());
		spVm.setMemoryGB(cloudServer.getMemory());
		spVm.setVappTemplate(template);
//        SpVappTemplateMachine templateMachine = this.dao.load(SpVappTemplateMachine.class,
//                cloudServer.getTemplateMachine().getId());
//        spVm.setTemplateMachine(templateMachine);
		spVm.setPowerStatus(SpVmPowerStatus.power_on);
		spVm.setOwner(cloudServer.getOwner());
		spVm.setOrder(cloudServer.getOrder());
		spVm.setDeployStatus(SpDeployStatus.INIT);
		//spVm.setVmType(VcdOperationCommon.getVmType(cloudServer.getOrder()));
		spVm.setVmType(OrderUtil.getVmType(cloudServer.getOrder()!=null?cloudServer.getOrder().getType():null));
		spVm.setHostName(cloudServer.getHostname());
		spVm.setRegion(ThreadCache.getRegion());
		List<SpVmDisk> spvmDiskList = new ArrayList<SpVmDisk>();
		int number = 0;
		int diskGB = 0;

		for (UpOrderCloudDisk disk : cloudServer.getCloudDiskList()) {
			SpVmDisk spvmDisk = new SpVmDisk();
			spvmDisk.setSpOrg(disk.getSpOrg());
			spvmDisk.setName(disk.getName());
			spvmDisk.setDiskGB(disk.getDiskGB());
			spvmDisk.setDiskNumber(number++);
			spvmDisk.setDiskLabel(disk.getName());
			spvmDisk.setDiskPath("");
			spvmDisk.setType(disk.getType());
			spvmDisk.setOrder(disk.getOrder());
			spvmDisk.setRegion(ThreadCache.getRegion());
			spvmDiskList.add(spvmDisk);
			diskGB += spvmDisk.getDiskGB();
		}
		spVm.setDiskList(spvmDiskList);
		spVm.setDiskGB(diskGB);

		vmList.add(spVm);
		spVapp.setVmType(spVm.getVmType());
		spVapp.setVmList(vmList);
		spVapp.setRegion(ThreadCache.getRegion());
		return spVapp;
	}
}
