package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "年度起始日期设置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpYearlyStartDateSetSearchBean {
    private String yearly_start_date;

    public String getYearly_start_date() {
        return yearly_start_date;
    }

    public void setYearly_start_date(String yearly_start_date) {
        this.yearly_start_date = yearly_start_date;
    }


}
