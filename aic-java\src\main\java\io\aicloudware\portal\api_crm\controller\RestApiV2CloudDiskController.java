package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.framework.bean.RestV2CloudDiskBean;
import io.aicloudware.portal.api_crm.service.IRestCloudDiskService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskSearchBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.entity.SpVmDisk;
import io.aicloudware.portal.platform_vcd.service.ISpVmDiskService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2/cloud_disk")
@Api(value = "/api/v2/cloud_disk", description = "REST API V2")
public class RestApiV2CloudDiskController extends BaseEntityController {

    @Autowired
    private IRestCloudDiskService restCloudDiskService;

    @Autowired
    private ISpVmDiskService spVmDiskService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody RestV2CloudDiskBean bean, HttpServletRequest request) {
        AssertUtil.check(bean.getCloudServerId(), "请选择云服务器！");
        commonService.load(SpVm.class, bean.getCloudServerId(), ThreadCache.getOrgId());
        return ResponseBean.success(restCloudDiskService.save(bean));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpVmDiskSearchBean searchBean) {
        SpVmDisk entity = BeanCopyUtil.copy(searchBean.getBean(), SpVmDisk.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());

        if(StringUtils.isNotEmpty(entity.getName())) {
            SpVmDiskBean fuzzyBean = new SpVmDiskBean();
            fuzzyBean.setDiskLabel(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        SpVmDiskBean[] entityList = spVmDiskService.query(searchBean, entity);
        SpVmDiskResultBean result = new SpVmDiskResultBean();
        fillPageInfo(searchBean, result);
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(notes = "/delete", httpMethod = "POST", value = "删除实例")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.STORAGE, description = "删除")
    public ResponseBean delete(@ApiParam(value = "对象ID") @RequestBody SpVmDiskBean disk) {
        spVmDiskService.deleteDisk(disk);
        return ResponseBean.success(true);
    }

}
