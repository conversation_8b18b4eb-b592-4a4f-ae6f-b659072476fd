package io.aicloudware.portal.api_vcpp.service.order;

import java.math.BigDecimal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderFileStorage;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.entity.UpProductDiskSet;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpFileStorageBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderFileStorageBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.FileStorageType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpFileStorage;

@Service
@Transactional
public class UpOrderFileStorageService extends BaseService implements IUpOrderFileStorageService {

	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Override
	public SpFileStorageBean[] queryFileStorage(Integer userId) {
		return BeanCopyUtil.copy2BeanList(dao.list(SpFileStorage.class,"spOrg.id",dao.load(UpUser.class, userId).getOrg().getId()),SpFileStorageBean.class);
	}
	
	@Override
	public Integer save(UpOrderFileStorageBean bean, UpUser applyUser) {
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean.getSizeG(), "请输入容量！");
		
		UpOrderFileStorage entity = BeanCopyUtil.copy(bean, UpOrderFileStorage.class);

		AssertUtil.check(orderService.queryActiveOrder(OrderType.file_storage, user.getId()) == 0, "您有未完成的文件存储申请！");
		
		entity.setName(bean.getName()+ "_" + String.format("%04d", (int) (Math.random() * 1000)));

		UpOrderQuotaDetail quotaDetail = bean.getQuotaDetailId() == null ? null : dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());

//		AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= bean.getSizeG(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
//		AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= bean.getSizeG(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.file_storage);
		order.setName("[" + OrderType.file_storage + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSystemDiskNum(0);
		order.setDiskNum(entity.getSizeG().intValue());
//		order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(entity.getSizeG())));
		order.setDiskPrice(BigDecimal.ZERO);
		if(quotaDetail == null || quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
			UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
			AssertUtil.check(diskSet != null && diskSet.getEnabled() && diskSet.getType().equals(ProductDiskSetType.file_storage), "云盘配置信息异常！");
			order.setDiskSet(diskSet);
		}
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(quotaDetail);
		}
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
    }

	@Override
	public Integer change(UpOrderFileStorageBean bean, UpUser applyUser) {
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getSpFileStorageId(), "请选择文件存储！");
		
		SpFileStorage fileStorage = dao.load(SpFileStorage.class, bean.getSpFileStorageId());
		AssertUtil.check(user.getOrg().getId().equals(fileStorage.getSpOrg().getId()), "文件存储所属租户信息异常！");
		
		
		AssertUtil.check(bean.getSizeG(), "请输入容量！");
		
		AssertUtil.check(orderService.queryActiveOrder(OrderType.file_storage_change, user.getId()) == 0, "您有未完成的文件存储变更申请！");

		UpOrderQuotaDetail quotaDetail = bean.getQuotaDetailId() == null ? null : dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());

		UpOrder order = new UpOrder();
		order.setRegion(fileStorage.getRegion());
		order.setType(OrderType.file_storage_change);
		order.setName("[" + OrderType.file_storage_change + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSystemDiskNum(0);
		order.setDiskNum(bean.getSizeG().intValue());
		if(quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
			UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
			AssertUtil.check(diskSet != null && diskSet.getEnabled() && diskSet.getType().equals(ProductDiskSetType.file_storage), "云盘配置信息异常！");
			AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= bean.getSizeG(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
			AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= bean.getSizeG(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
			order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(bean.getSizeG())));
			order.setDiskSet(diskSet);
		}
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		this.dao.insert(order);

		UpOrderFileStorage entity = new UpOrderFileStorage();
		entity.setName(fileStorage.getName());
		entity.setSizeG(bean.getSizeG());
		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}

	@Override
	public Integer quotaSave(UpOrderFileStorageBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
//		AssertUtil.check(user.getOrg() != null && bean.getSpOrgId() != null && user.getOrg().getId().equals(bean.getSpOrgId()), "用户信息异常！");
		
		AssertUtil.check(bean.getSizeG(), "请输入容量！");
		AssertUtil.check(bean.getDiskConfigId(), "缺少存储产品配置！");
//		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		bean.setOwnerId(user.getId());
		bean.setType(FileStorageType.nfs);
		UpOrderFileStorage entity = BeanCopyUtil.copy(bean, UpOrderFileStorage.class);
		AssertUtil.check(orderService.queryActiveOrder(OrderType.file_storage, user.getId()) == 0, "您有未完成的文件存储申请！");
		
		entity.setName(user.getOrg().getName() + "-" + System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000)));
		entity.setRegion(ThreadCache.getRegion());
		UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
		AssertUtil.check(diskSet != null && diskSet.getEnabled() ,"存储配置信息异常！");
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.file_storage);
		order.setName("[" + OrderType.file_storage + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
//		order.setPaymentType(bean.getPaymentType());
		order.setDiskNum(entity.getSizeG().intValue());
//		order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(entity.getDiskGB())));
		order.setDiskPrice(BigDecimal.ZERO);
		order.setDiskSet(diskSet);
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		quotaService.deployQuotaDetail(bean.getQuotaDetailId());
//		orderMQService.createOrderMQ(order, Lists.newArrayList(entity));
		return order.getId();
	}
}
