package io.aicloudware.portal.framework.common;

import java.util.List;
import java.util.Map;

import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpVimServer;

public class EndpointUtil {

    public static String getVcIP(SpVimServer endpoint) {
        String vcIP = endpoint.getServerUrl();
        vcIP = vcIP.substring(vcIP.indexOf("://") + "://".length());
        vcIP = vcIP.substring(0, vcIP.indexOf(vcIP.contains(":") ? ":" : "/"));
        return vcIP;
    }

    public static Map<String, SpVimServer> getAvailableEndpointMap() {
        List<SpVimServer> endpointList = BeanFactory.getCloudDao().list(SpVimServer.class);
        return ListUtil.map(endpointList, (dataMap, endpoint) -> {
            if (Utility.isNotEmpty(endpoint.getServerUrl())
                    && Utility.isNotEmpty(endpoint.getUsername())
                    && Utility.isNotEmpty(endpoint.getPassword())
                    && !RecordStatus.deleted.equals(endpoint.getStatus())
                    && !dataMap.containsKey(endpoint.getServerUrl())) {
                dataMap.put(endpoint.getServerUrl(), endpoint);
            }
        });
    }
}
