package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudIamOperation;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpProject;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional

public class UpProjectService extends BaseService implements IUpProjectService {

	@Autowired
	private ISpRegionService spRegionService;

	@Override
	public void sync(Integer orgId, SpRegionEntity region) {
		syncProject(orgId, region);
	}

	private void syncProject(Integer orgId, SpRegionEntity region) {
		SpOrg org = dao.load(SpOrg.class, orgId);
		List<SpProject> serverDataList = JointEcloudIamOperation.getInstance(org.getUsername(), org.getPassword(), region).listProject(new ArrayList<>(spRegionService.getRegionMap().values()));

		List<SpProject> tableDataList = queryDao.list(SpProject.class, MapUtil.of("status", RecordStatus.active, "status", RecordStatus.active));
		List<SpProject> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, (o1, o2) -> o1.getSpUuid().equals(o2.getSpUuid()) ? 0 : 1);
		List<SpProject> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, (o1, o2) -> o1.getSpUuid().equals(o2.getSpUuid()) ? 0 : 1);
		dataToAdd.forEach( o -> {
			dao.insert(o);
		} );
		dataToDel.forEach( o -> {
			dao.delete(UpProductVmSet.class, o.getId());
		} );
	}


}
