package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "快照列表查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpSnapshotSearchBean extends SearchBean<SpSnapshotBean> {

    @ApiModelProperty(value = "虚机ID列表", hidden = true)
    private String[] vmUuidList;

    public String[] getVmUuidList() {
        return vmUuidList;
    }

    public void setVmUuidList(String[] vmUuidList) {
        this.vmUuidList = vmUuidList;
    }

}
