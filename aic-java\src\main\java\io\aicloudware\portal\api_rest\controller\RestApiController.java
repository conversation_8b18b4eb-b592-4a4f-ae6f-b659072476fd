package io.aicloudware.portal.api_rest.controller;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.annotation.RestLogAspect;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.swagger.annotations.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_rest.framework.annotation.IpValidate;
import io.aicloudware.portal.api_rest.framework.annotation.RestRequestBodyDecode;
import io.aicloudware.portal.api_rest.framework.bean.RestResponseBean;
import io.aicloudware.portal.api_rest.service.IRestApiService;
import io.aicloudware.portal.api_vcpp.controller.BaseController;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/rest")
@Api(value = "/rest", description = "REST API")
public class RestApiController extends BaseController {

	@Autowired
    private IRestApiService restApiService;
	
	@RequestMapping(value = "/user/add", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "user", type = "add")
    @RestLogAspect
    public RestResponseBean userAdd(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.userAdd(params);
    }
	
	@RequestMapping(value = "/user/delete", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "user", type = "delete")
    @RestLogAspect
    public RestResponseBean userDelete(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
    	return restApiService.userDelete(params);
    }
	
	@RequestMapping(value = "/quota/add", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "quota", type = "add")
    @RestLogAspect
    public RestResponseBean quotaAdd(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.quotaAdd(params);
    }
	
	@RequestMapping(value = "/quota/change", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "quota", type = "change")
    @RestLogAspect
    public RestResponseBean quotaChange(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.quotaChange(params);
    }
	
//	@RequestMapping(value = "/quota/cancel", method = RequestMethod.POST)
//    @ResponseBody
//    @IpValidate(module = "quota", type = "cancel")
//    public RestResponseBean quotaCancel(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
//		return restService.quotaCancel(params);
//    }
	
	@RequestMapping(value = "/org/add", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "org", type = "add")
    @RestLogAspect
    public RestResponseBean orgAdd(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.orgAdd(params);
    }
	
	@RequestMapping(value = "/org/start", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "org", type = "start")
    @RestLogAspect
    public RestResponseBean orgStart(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.orgStart(params);
    }
	
	@RequestMapping(value = "/org/stop", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "org", type = "stop")
    @RestLogAspect
    public RestResponseBean orgStop(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.orgStop(params);
    }
	
	@RequestMapping(value = "/org/delete", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "org", type = "cancel")
    @RestLogAspect
    public RestResponseBean orgDelete(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.orgDelete(params);
    }
	
	@RequestMapping(value = "/vapp/vmmetrics", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "vapp", type = "vmmetrics")
    @RestLogAspect
    public RestResponseBean vappVmmetrics(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.vappVmmetrics(params);
    }
	
	@RequestMapping(value = "/vapp/netmetrics", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "vapp", type = "netmetrics")
    @RestLogAspect
    public RestResponseBean vappNetmetrics(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
		return restApiService.vappNetmetrics(params);
    }

    @RequestMapping(value = "/reservation/inquire", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "reservation", type = "inquire")
    @RestLogAspect
    public RestResponseBean reservationInquire(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
        return restApiService.reservationInquire(params);
    }

    @RequestMapping(value = "/ping/{ip}/{ovdcId}/{type}", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "ping", type = "")
    @RestLogAspect
    public ResponseBean ping(@PathVariable Integer ovdcId, @PathVariable String ip, @PathVariable String type, HttpServletRequest request) {
        return ResponseBean.success(restApiService.ping(ovdcId, ip, type));
//        String str = "PING *********** (***********) 56(84) bytes of data.\n" +
//                "64 bytes from ***********: icmp_seq=1 ttl=63 time=1.83 ms\n" +
//                "64 bytes from ***********: icmp_seq=2 ttl=63 time=0.448 ms\n" +
//                "64 bytes from ***********: icmp_seq=3 ttl=63 time=0.508 ms\n" +
//                "64 bytes from ***********: icmp_seq=4 ttl=63 time=1.12 ms\n" +
//                "64 bytes from ***********: icmp_seq=5 ttl=63 time=0.618 ms\n" +
//                "\n" +
//                "--- *********** ping statistics ---\n" +
//                "5 packets transmitted, 5 received, 0% packet loss, time 4004ms\n" +
//                "rtt min/avg/max/mdev = 0.448/0.905/1.831/0.520 ms";
//        return ResponseBean.success(str);
    }

//    @RequestMapping(value = "/createVPCMessage", method = RequestMethod.POST)
//    @ResponseBody
//    @IpValidate(module = "reservation", type = "inquire")
    public RestResponseBean createVPCMessage(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
        return restApiService.createVPCMessage(params);
    }

    @RequestMapping(value = "/createOrderMessage", method = RequestMethod.POST)
    @ResponseBody
//    @IpValidate(module = "reservation", type = "inquire")
    public RestResponseBean createOrderMessage(@RestRequestBodyDecode JSONObject params, HttpServletRequest request) {
        return restApiService.createOrderMessage(params);
    }
}
