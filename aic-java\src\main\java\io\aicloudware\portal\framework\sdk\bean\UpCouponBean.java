package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "礼品券")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpCouponBean.class})
public class UpCouponBean extends SpRecordBean {

}
