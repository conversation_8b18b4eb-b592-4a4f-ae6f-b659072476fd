package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpIpStatus;
import io.aicloudware.portal.framework.bean.SpRecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "弹性公网IP绑定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpElasticIpBean extends SpRecordBean {

	@ApiModelProperty(value = "vdcId")
	private String vdcId;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "关联虚机")
    private String assignedVm;

	@ApiModelProperty(value = "PortId")
	private String portId;

	@ApiModelProperty(value = "私网地址")
	private String privateIpAddress;
    
    @ApiModelProperty(value = "IP状态")
    private SpIpStatus ipStatus;
    
    @ApiModelProperty(value = "带宽")
    private Integer bandwidth;
    
    @ApiModelProperty(value = "访问互联网出口")
    private Boolean snat;
    
    @ApiModelProperty(value = "IP绑定")
	private SpIpBindingBean[] ipBinding;
    
    @ApiModelProperty(value = "备案")
    private Boolean isWebsiteApprove;

	public String getPrivateIpAddress() {
		return privateIpAddress;
	}

	public void setPrivateIpAddress(String privateIpAddress) {
		this.privateIpAddress = privateIpAddress;
	}

	public String getPortId() {
		return portId;
	}

	public void setPortId(String portId) {
		this.portId = portId;
	}

	public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpIpStatus getIpStatus() {
        return ipStatus;
    }

    public void setIpStatus(SpIpStatus status) {
        this.ipStatus = status;
    }

    public String getAssignedVm() {
        return assignedVm;
    }

    public void setAssignedVm(String assignedVm) {
        this.assignedVm = assignedVm;
    }

	public String getVdcId() {
		return vdcId;
	}

	public void setVdcId(String vdcId) {
		this.vdcId = vdcId;
	}

	public SpIpBindingBean[] getIpBinding() {
		return ipBinding;
	}

	public void setIpBinding(SpIpBindingBean[] ipBinding) {
		this.ipBinding = ipBinding;
	}

	public Integer getBandwidth() {
		return bandwidth;
	}

	public void setBandwidth(Integer bandwidth) {
		this.bandwidth = bandwidth;
	}

	public Boolean getSnat() {
		return snat;
	}

	public void setSnat(Boolean snat) {
		this.snat = snat;
	}

	public Boolean getIsWebsiteApprove() {
		return isWebsiteApprove;
	}

	public void setIsWebsiteApprove(Boolean isWebsiteApprove) {
		this.isWebsiteApprove = isWebsiteApprove;
	}

}
