package io.aicloudware.portal.framework.dao;

import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.common.CommonUtil;
import io.aicloudware.portal.framework.common.SdkConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.entity.IResourceEntity;
import io.aicloudware.portal.framework.entity.ISpResourceEntity;
import io.aicloudware.portal.framework.executor.IExecutorA;
import io.aicloudware.portal.framework.hibernate.PropertyFilter;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.contants.UpMainMenuType;
import io.aicloudware.portal.framework.sdk.contants.UpRightType;
import io.aicloudware.portal.framework.service.ICloudService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.SpIpRange;
import io.aicloudware.portal.platform_vcd.entity.SpIpUsage;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;

import java.lang.reflect.Field;
import java.util.*;

public class DaoUtil {
    private static final Logger logger = Logger.getLogger(DaoUtil.class);

    public static void addEntityQuery(DetachedCriteria criteria, IEntity entity, Set<String> aliasSet) {
        for (Field field : Utility.describeFieldMap(entity).values()) {
            if (IEntity.class.isAssignableFrom(Utility.getFieldType(field))) {
                IEntity value = (IEntity) Utility.getFieldValue(field, entity);
                if (value != null) {
                    if (Utility.isNotZero(value.getId())) {
                        criteria.add(Restrictions.eq(field.getName() + ".id", value.getId()));
                    } else if (Utility.isNotEmpty(value.getName())) {
                        criteria.add(Restrictions.eq(field.getName() + ".name", value.getName()));
                        aliasSet.add(field.getName());
                    }
                }
            }
        }
    }

    public static void addFuzzyQuery(DetachedCriteria criteria, RecordBean fuzzyBean, Set<String> aliasSet) {
        if (fuzzyBean != null) {
            List<Criterion> criterionList = new ArrayList<>();
            Map<String, Field> fieldMap = Utility.describeFieldMap(fuzzyBean);
            for (Field field : fieldMap.values()) {
                if (String.class.equals(Utility.getFieldType(field))) {
                    String value = (String) Utility.getFieldValue(field, fuzzyBean);
                    if (Utility.isNotEmpty(value)) {
                        String name = field.getName();
                        if (name.endsWith("Name") && fieldMap.containsKey(name.replace("Name", "Id"))) {
                            name = name.replace("Name", ".name");
                        }
                        if (name.endsWith("DisplayName") && fieldMap.containsKey(name.replace("DisplayName", "Id"))) {
                            name = name.replace("DisplayName", ".displayName");
                        }
                        criterionList.add(Restrictions.ilike(name, value, MatchMode.ANYWHERE));
                        if (name.contains(".")) {
                            aliasSet.add(name.substring(0, name.indexOf(".")));
                        }
                    }
                }
            }
            if (Utility.isNotEmpty(criterionList)) {
                criteria.add(Restrictions.or(criterionList.toArray(new Criterion[criterionList.size()])));
            }
        }
    }

    public static void addInValues(DetachedCriteria criteria, String name, List<?> values) {
        if (Utility.isNotEmpty(values)) {
            final int maxSize = 1000;
            if (values.size() == 1) {
                criteria.add(Restrictions.eq(name, values.get(0)));
            } else if (values.size() == 2) {
                criteria.add(Restrictions.or(Restrictions.eq(name, values.get(0)),
                        Restrictions.eq(name, values.get(1))));
            } else if (values.size() <= maxSize) {
                criteria.add(Restrictions.in(name, values));
            } else {
                List<Criterion> criterionList = new ArrayList<>();
                for (int i = 0; i <= (values.size() - 1) / maxSize; i++) {
                    List<Object> valueList = new ArrayList<>(maxSize);
                    for (int j = i * maxSize; j < Math.min(values.size(), (i + 1) * maxSize); j++) {
                        valueList.add(values.get(j));
                        criterionList.add(Restrictions.in(name, valueList));
                    }
                }
                criteria.add(Restrictions.or(criterionList.toArray(new Criterion[criterionList.size()])));
            }
        }
    }

    public static void addDataScope(UpMainMenuType mainMenuType, DetachedCriteria criteria, Set<String> aliasSet) {
        if (ThreadCache.getUser() != null && !ThreadCache.isSystemAdminLogin()) {
            //criteria.add(Restrictions.eq("environment", ThreadCache.getUser().getEnvironment()));
        }
        if (mainMenuType != null && Utility.isNotZero(ThreadCache.getUserId()) && !ThreadCache.isSystemAdminLogin()) {
            List<UpRightBean> rightList = BeanFactory.getRightService().findUserRightList();
            if (UpMainMenuType.my_cloud.equals(mainMenuType)) {
                Criterion criterion = Restrictions.eq("owner.id", ThreadCache.getUserId());
                criterion = Restrictions.or(criterion, Restrictions.eq("appSystem.owner.id", ThreadCache.getUserId()));
                aliasSet.add("appSystem");
                List<Integer> appSystemIdList = CommonUtil.getTargetIdList(rightList, UpRightType.entrust_manage_app_system);
                if (Utility.isNotEmpty(appSystemIdList)) {
                    criterion = Restrictions.or(criterion, Restrictions.in("appSystem.id", appSystemIdList));
                }
                criteria.add(criterion);
            } else {
                if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_system)) {
                } else if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_tenant)) {
                } else if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_owner)) {
                    Criterion criterion = Restrictions.eq("owner.id", ThreadCache.getUserId());
                    criterion = Restrictions.or(criterion, Restrictions.eq("appSystem.owner.id", ThreadCache.getUserId()));
                    aliasSet.add("appSystem");
                    List<Integer> appSystemIdList = CommonUtil.getTargetIdList(rightList, UpRightType.entrust_manage_app_system);
                    if (Utility.isNotEmpty(appSystemIdList)) {
                        criterion = Restrictions.or(criterion, Restrictions.in("appSystem.id", appSystemIdList));
                    }
                    criteria.add(criterion);
                } else {
                    AssertUtil.check(false, "没找到数据查看范围权限");
                }
            }
        }
    }

    public static void addDataScope(StringBuilder hql, Map<String, Object> map, UpMainMenuType mainMenuType) {
        if (!ThreadCache.isSystemAdminLogin()) {
            hql.append(" and t.environment = :environment ");
            //map.put("environment", ThreadCache.getUser().getEnvironment());
        }
        if (mainMenuType != null && Utility.isNotZero(ThreadCache.getUserId()) && !ThreadCache.isSystemAdminLogin()) {
            List<UpRightBean> rightList = BeanFactory.getRightService().findUserRightList();
            if (UpMainMenuType.my_cloud.equals(mainMenuType)) {
                hql.append(" and (t.owner.id = :userId ");
                hql.append("or t.appSystem.owner.id = :userId ");
                map.put("userId", ThreadCache.getUserId());
                List<Integer> appSystemIdList = CommonUtil.getTargetIdList(rightList, UpRightType.entrust_manage_app_system);
                if (Utility.isNotEmpty(appSystemIdList)) {
                    hql.append("or t.appSystem.id in (:appSystemIdList) ");
                    map.put("appSystemIdList", appSystemIdList);
                }
                hql.append(")");
            } else {
                if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_system)) {
                } else if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_tenant)) {
                } else if (CommonUtil.isHasRight(rightList, UpRightType.data_scope_owner)) {
                    hql.append(" and ((t.owner.id = :userId ");
                    hql.append("or t.appSystem.owner.id = :userId) ");
                    map.put("userId", ThreadCache.getUserId());
                    List<Integer> appSystemIdList = CommonUtil.getTargetIdList(rightList, UpRightType.entrust_manage_app_system);
                    appSystemIdList.add(1);
                    if (Utility.isNotEmpty(appSystemIdList)) {
                        hql.append("or t.appSystem.id in (:appSystemIdList) ");
                        map.put("appSystemIdList", appSystemIdList);
                    }
                    hql.append(")");
                } else {
                    AssertUtil.check(false, "没找到数据查看范围权限");
                }
            }
        }
    }

    public static void setParentEntity(IEntity parent, IEntity child) {
        if (parent != null && child != null) {
            for (Field field : Utility.describeFieldMap(child).values()) {
                if (Utility.getFieldType(field).isAssignableFrom(parent.getClass())) {
                    if (PropertyFilter.isParentEntity.doExecute(field)) {
                        Utility.setFieldValue(field, child, parent);
                    }
                }
            }
        }
    }

    public static void setEntityRegion(SpRegionEntity region, IEntity child) {
        if (region != null && child != null) {
            for (Field field : Utility.describeFieldMap(child).values()) {
                if (Utility.getFieldType(field).isAssignableFrom(region.getClass())) {
                    if (PropertyFilter.isParentEntity.doExecute(field)) {
                        Utility.setFieldValue(field, child, region);
                    }
                }
            }
        }
    }

    public static void cascadeParentEntity(IEntity entity, IExecutorA<IEntity> preExecutor, IExecutorA<IEntity> postExecutor) {
        if (preExecutor != null) {
            preExecutor.doExecute(entity);
        }
        if (entity != null) {
            for (Field field : Utility.describeFieldMap(entity).values()) {
                if (IEntity.class.isAssignableFrom(Utility.getFieldType(field))) {
                    IEntity parent = (IEntity) Utility.getFieldValue(field, entity);
                    cascadeParentEntity(parent, preExecutor, postExecutor);
                }
            }
        }
        if (postExecutor != null) {
            postExecutor.doExecute(entity);
        }
    }

    public static void cascadeChildEntity(IEntity entity, IExecutorA<IEntity> preExecutor, IExecutorA<IEntity> postExecutor) {
        if (preExecutor != null) {
            preExecutor.doExecute(entity);
        }
        if (entity != null) {
            for (Field field : Utility.describeFieldMap(entity).values()) {
                if (Collection.class.isAssignableFrom(Utility.getFieldType(field))
                        && IEntity.class.isAssignableFrom(Utility.getCollectionType(entity.getClass(), field))) {
                    Collection<IEntity> childCollection = (Collection<IEntity>) Utility.getFieldValue(field, entity);
                    if (Utility.isNotEmpty(childCollection)) {
                        for (IEntity child : childCollection) {
                            setParentEntity(entity, child);
                            cascadeChildEntity(child, preExecutor, postExecutor);
                        }
                    }
                }
            }
        }
        if (postExecutor != null) {
            postExecutor.doExecute(entity);
        }
    }

    public static <E extends IResourceEntity<B>, B extends RecordBean> List<E> getTableDataList(IDao dao, IEntity parent, Class<E> entityClass, Class<? extends SearchBean<B>> searchBeanClass) {
        SearchBean<B> searchBean = Utility.newInstance(searchBeanClass);
        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
        E entity = Utility.newInstance(entityClass);
        DaoUtil.setParentEntity(parent, entity);
        return dao.query(searchBean, entity);
    }

    public static <E extends IResourceEntity<B>, B extends RecordBean> List<E> getTableDataList(
            IDao dao, IEntity parent, Class<E> entityClass, Class<? extends SearchBean<B>> searchBeanClass, SpRegionEntity region) {
        SearchBean<B> searchBean = Utility.newInstance(searchBeanClass);
        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
        E entity = Utility.newInstance(entityClass);
        DaoUtil.setParentEntity(parent, entity);
        DaoUtil.setEntityRegion(region, entity);
        return dao.query(searchBean, entity);
    }

    public static <E extends IResourceEntity> void mergeDataList(IDao dao, List<E> tableDataList, List<E> serverDataList) {
        if (serverDataList == null) {
            return;
        }
        ListUtil.ConvertKey<E, String> convertKey = new ListUtil.ConvertKey<E, String>() {
            @Override
            public String getKey(E value) {
                return value.getSpUuid();
            }
        };
        Map<String, E> tableDataMap = ListUtil.map(tableDataList, convertKey);
        Map<String, E> serverDataMap = ListUtil.map(serverDataList, convertKey);
        for (String uuid : serverDataMap.keySet()) {
            E serverEntity = serverDataMap.get(uuid);
            if (tableDataMap.containsKey(uuid)) {
                E tableEntity = tableDataMap.get(uuid);
                if (!BeanCopyUtil.equals(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate)) {
                    if (tableEntity instanceof IDisplayName) {
                        String displayName = ((IDisplayName) tableEntity).getDisplayName();
                        BeanCopyUtil.copy(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate);
                        if (Utility.isNotEmpty(displayName) && displayName.endsWith(IDisplayName.DisplayName_Suffix)) {
                            ((IDisplayName) tableEntity).setDisplayName(displayName);
                        }
                    } else {
                        BeanCopyUtil.copy(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate);
                    }
                    dao.update(tableEntity);
                    //logger.info("Merge data update : " + tableEntity);
                } else {
                    BeanCopyUtil.copy(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate);
                }
                for (Field field : Utility.describeFieldMap(tableEntity).values()) {
                    if (Collection.class.isAssignableFrom(Utility.getFieldType(field))
                            && IResourceEntity.class.isAssignableFrom(Utility.getCollectionType(tableEntity.getClass(), field))) {
                        List<IResourceEntity> tableChildList = (List<IResourceEntity>) Utility.getFieldValue(field, tableEntity);
                        List<IResourceEntity> serverChildList = (List<IResourceEntity>) Utility.getFieldValue(field, serverEntity);
                        mergeDataList(dao, tableChildList, serverChildList);
                    }
                }
            } else {
                IResourceEntity tableEntity = loadTableEntity(serverEntity);
                if (Utility.isZero(tableEntity.getId())) {
                    dao.insert(tableEntity);
                    //logger.info("Merge data insert : " + tableEntity);
                } else {
                    for (Field field : Utility.describeFieldMap(tableEntity).values()) {
                        if (Collection.class.isAssignableFrom(Utility.getFieldType(field))
                                && IResourceEntity.class.isAssignableFrom(Utility.getCollectionType(tableEntity.getClass(), field))) {
                            List<IResourceEntity> tableChildList = (List<IResourceEntity>) Utility.getFieldValue(field, tableEntity);
                            List<IResourceEntity> serverChildList = (List<IResourceEntity>) Utility.getFieldValue(field, serverEntity);
                            mergeDataList(dao, tableChildList, serverChildList);
                        }
                    }
                    dao.update(tableEntity);
                    //logger.info("Merge data update : " + tableEntity);
                }
            }
        }
        for (String uuid : tableDataMap.keySet()) {
            if (!serverDataMap.containsKey(uuid)) {
                E tableEntity = tableDataMap.get(uuid);
                logger.info("Merge data delete : " + tableEntity.getClass()+ " id:"+tableEntity.getId());
                dao.delete(tableEntity.getClass(), tableEntity.getId());
                //logger.info("Merge data delete : " + tableEntity);
            }
        }
    }

    private static IResourceEntity loadTableEntity(IResourceEntity serverEntity) {
        Class<? extends ISpResourceEntity> type = null;
        if (serverEntity instanceof SpIpRange) {
            type = SpIpRange.class;
        } else if (serverEntity instanceof SpIpUsage) {
            type = SpIpUsage.class;
        }
        if (type != null) {
            ISpResourceEntity tableEntity = ICloudService.getEntityByUuid(type, ((ISpResourceEntity) serverEntity).getSpOrg(), serverEntity.getSpUuid(), true);
            if (tableEntity != null && Utility.isNotZero(tableEntity.getId())) {
                if (tableEntity instanceof IDisplayName) {
                    String displayName = ((IDisplayName) tableEntity).getDisplayName();
                    BeanCopyUtil.copy(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate);
                    if (Utility.isNotEmpty(displayName) && displayName.endsWith(IDisplayName.DisplayName_Suffix)) {
                        ((IDisplayName) tableEntity).setDisplayName(displayName);
                    }
                } else {
                    BeanCopyUtil.copy(serverEntity, tableEntity, PropertyFilter.isCopyOnUpdate);
                }
                return tableEntity;
            }
        }
        return serverEntity;
    }
}
