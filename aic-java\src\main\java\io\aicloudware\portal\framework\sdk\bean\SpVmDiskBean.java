package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "虚机磁盘")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVmDiskBean.class})
public class SpVmDiskBean extends SpRecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "存储ID")
    private Integer storageId;

    @ApiModelProperty(value = "存储名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String storageName;

    @ApiModelProperty(value = "存储显示名称")
    private String storageDisplayName;

    @ApiModelProperty(value = "存储策略ID")
    private Integer policyId;

    @ApiModelProperty(value = "存储策略名称")
    private String policyName;

    @ApiModelProperty(value = "存储策略显示名称")
    private String policyDisplayName;

    @ApiModelProperty(value = "磁盘序号")
    private Integer diskNumber;

    @ApiModelProperty(value = "驱动器盘符/挂载路径")
    private String diskPath;

    @ApiModelProperty(value = "磁盘大小(GB)")
    private Integer diskGB;

    @ApiModelProperty(value = "磁盘标签")
    private String diskLabel;

    @ApiModelProperty(value = "类型")
    private SpVmDiskType type;
    
    @ApiModelProperty(value = "虚机UUID")
    private String vmUuid;
    
    @ApiModelProperty(value = "所有者")
    private Integer ownerId;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer blueprintMachineId) {
        this.vmId = blueprintMachineId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public Integer getStorageId() {
        return storageId;
    }

    public void setStorageId(Integer storageId) {
        this.storageId = storageId;
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = storageName;
    }

    public Integer getDiskNumber() {
        return diskNumber;
    }

    public void setDiskNumber(Integer diskNumber) {
        this.diskNumber = diskNumber;
    }

    public String getDiskPath() {
        return diskPath;
    }

    public void setDiskPath(String diskPath) {
        this.diskPath = diskPath;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getDiskLabel() {
        return diskLabel;
    }

    public void setDiskLabel(String diskLabel) {
        this.diskLabel = diskLabel;
    }

    public Integer getPolicyId() {
        return policyId;
    }

    public void setPolicyId(Integer policyId) {
        this.policyId = policyId;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public String getStorageDisplayName() {
        return storageDisplayName;
    }

    public void setStorageDisplayName(String storageDisplayName) {
        this.storageDisplayName = storageDisplayName;
    }

    public String getPolicyDisplayName() {
        return policyDisplayName;
    }

    public void setPolicyDisplayName(String policyDisplayName) {
        this.policyDisplayName = policyDisplayName;
    }

    public SpVmDiskType getType() {
        return type;
    }

    public void setType(SpVmDiskType type) {
        this.type = type;
    }

    public String getVmUuid() {
        return vmUuid;
    }

    public void setVmUuid(String vmUuid) {
        this.vmUuid = vmUuid;
    }

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}
    
}
