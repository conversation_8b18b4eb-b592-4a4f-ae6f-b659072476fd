package io.aicloudware.portal.framework.quartz;

import io.aicloudware.portal.framework.hibernate.ConnectionWrapper;
import io.aicloudware.portal.framework.utility.Utility;
import org.quartz.JobPersistenceException;
import org.quartz.impl.jdbcjobstore.JobStoreTX;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class QuartzJobStore extends JobStoreTX {
    private boolean init_db = true;

    @Override
    protected Connection getConnection() throws JobPersistenceException {
        Connection connection = super.getConnection();
        if (init_db) {
            init_db = false;
            try {
                initDB(new ConnectionWrapper(connection));
            } catch (SQLException e) {
                throw new JobPersistenceException("initDB error", e);
            }
        }
        return connection;
    }

    private void initDB(Connection connection) throws SQLException {
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery("select tablename from pg_tables where schemaname = 'public' order by tablename");
        if (resultSet.next()) {
            return;
        }
        resultSet.close();
        statement.close();
        String sql = "create table qrtz_job_details (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    job_name  varchar(200) not null,\n" +
                "    job_group varchar(200) not null,\n" +
                "    description varchar(250) null,\n" +
                "    job_class_name   varchar(250) not null, \n" +
                "    is_durable bool not null,\n" +
                "    is_nonconcurrent bool not null,\n" +
                "    is_update_data bool not null,\n" +
                "    requests_recovery bool not null,\n" +
                "    job_data bytea null,\n" +
                "    primary key (sched_name,job_name,job_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    job_name  varchar(200) not null, \n" +
                "    job_group varchar(200) not null,\n" +
                "    description varchar(250) null,\n" +
                "    next_fire_time bigint null,\n" +
                "    prev_fire_time bigint null,\n" +
                "    priority integer null,\n" +
                "    trigger_state varchar(16) not null,\n" +
                "    trigger_type varchar(8) not null,\n" +
                "    start_time bigint not null,\n" +
                "    end_time bigint null,\n" +
                "    calendar_name varchar(200) null,\n" +
                "    misfire_instr smallint null,\n" +
                "    job_data bytea null,\n" +
                "    primary key (sched_name,trigger_name,trigger_group),\n" +
                "    foreign key (sched_name,job_name,job_group) \n" +
                "\treferences qrtz_job_details(sched_name,job_name,job_group) \n" +
                ");\n" +
                "\n" +
                "create table qrtz_simple_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    repeat_count bigint not null,\n" +
                "    repeat_interval bigint not null,\n" +
                "    times_triggered bigint not null,\n" +
                "    primary key (sched_name,trigger_name,trigger_group),\n" +
                "    foreign key (sched_name,trigger_name,trigger_group) \n" +
                "\treferences qrtz_triggers(sched_name,trigger_name,trigger_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_cron_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    cron_expression varchar(120) not null,\n" +
                "    time_zone_id varchar(80),\n" +
                "    primary key (sched_name,trigger_name,trigger_group),\n" +
                "    foreign key (sched_name,trigger_name,trigger_group) \n" +
                "\treferences qrtz_triggers(sched_name,trigger_name,trigger_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_simprop_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    str_prop_1 varchar(512) null,\n" +
                "    str_prop_2 varchar(512) null,\n" +
                "    str_prop_3 varchar(512) null,\n" +
                "    int_prop_1 int null,\n" +
                "    int_prop_2 int null,\n" +
                "    long_prop_1 bigint null,\n" +
                "    long_prop_2 bigint null,\n" +
                "    dec_prop_1 numeric(13,4) null,\n" +
                "    dec_prop_2 numeric(13,4) null,\n" +
                "    bool_prop_1 bool null,\n" +
                "    bool_prop_2 bool null,\n" +
                "    primary key (sched_name,trigger_name,trigger_group),\n" +
                "    foreign key (sched_name,trigger_name,trigger_group) \n" +
                "    references qrtz_triggers(sched_name,trigger_name,trigger_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_blob_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    blob_data bytea null,\n" +
                "    primary key (sched_name,trigger_name,trigger_group),\n" +
                "    foreign key (sched_name,trigger_name,trigger_group) \n" +
                "        references qrtz_triggers(sched_name,trigger_name,trigger_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_calendars (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    calendar_name  varchar(200) not null, \n" +
                "    calendar bytea not null,\n" +
                "    primary key (sched_name,calendar_name)\n" +
                ");\n" +
                "\n" +
                "\n" +
                "create table qrtz_paused_trigger_grps (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    trigger_group  varchar(200) not null, \n" +
                "    primary key (sched_name,trigger_group)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_fired_triggers (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    entry_id varchar(95) not null,\n" +
                "    trigger_name varchar(200) not null,\n" +
                "    trigger_group varchar(200) not null,\n" +
                "    instance_name varchar(200) not null,\n" +
                "    fired_time bigint not null,\n" +
                "    sched_time bigint not null,\n" +
                "    priority integer not null,\n" +
                "    state varchar(16) not null,\n" +
                "    job_name varchar(200) null,\n" +
                "    job_group varchar(200) null,\n" +
                "    is_nonconcurrent bool null,\n" +
                "    requests_recovery bool null,\n" +
                "    primary key (sched_name,entry_id)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_scheduler_state (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    instance_name varchar(200) not null,\n" +
                "    last_checkin_time bigint not null,\n" +
                "    checkin_interval bigint not null,\n" +
                "    primary key (sched_name,instance_name)\n" +
                ");\n" +
                "\n" +
                "create table qrtz_locks (\n" +
                "    sched_name varchar(120) not null,\n" +
                "    lock_name  varchar(40) not null, \n" +
                "    primary key (sched_name,lock_name)\n" +
                ");\n" +
                "\n" +
                "create index idx_qrtz_j_req_recovery on qrtz_job_details(sched_name,requests_recovery);\n" +
                "create index idx_qrtz_j_grp on qrtz_job_details(sched_name,job_group);\n" +
                "\n" +
                "create index idx_qrtz_t_j on qrtz_triggers(sched_name,job_name,job_group);\n" +
                "create index idx_qrtz_t_jg on qrtz_triggers(sched_name,job_group);\n" +
                "create index idx_qrtz_t_c on qrtz_triggers(sched_name,calendar_name);\n" +
                "create index idx_qrtz_t_g on qrtz_triggers(sched_name,trigger_group);\n" +
                "create index idx_qrtz_t_state on qrtz_triggers(sched_name,trigger_state);\n" +
                "create index idx_qrtz_t_n_state on qrtz_triggers(sched_name,trigger_name,trigger_group,trigger_state);\n" +
                "create index idx_qrtz_t_n_g_state on qrtz_triggers(sched_name,trigger_group,trigger_state);\n" +
                "create index idx_qrtz_t_next_fire_time on qrtz_triggers(sched_name,next_fire_time);\n" +
                "create index idx_qrtz_t_nft_st on qrtz_triggers(sched_name,trigger_state,next_fire_time);\n" +
                "create index idx_qrtz_t_nft_misfire on qrtz_triggers(sched_name,misfire_instr,next_fire_time);\n" +
                "create index idx_qrtz_t_nft_st_misfire on qrtz_triggers(sched_name,misfire_instr,next_fire_time,trigger_state);\n" +
                "create index idx_qrtz_t_nft_st_misfire_grp on qrtz_triggers(sched_name,misfire_instr,next_fire_time,trigger_group,trigger_state);\n" +
                "\n" +
                "create index idx_qrtz_ft_trig_inst_name on qrtz_fired_triggers(sched_name,instance_name);\n" +
                "create index idx_qrtz_ft_inst_job_req_rcvry on qrtz_fired_triggers(sched_name,instance_name,requests_recovery);\n" +
                "create index idx_qrtz_ft_j_g on qrtz_fired_triggers(sched_name,job_name,job_group);\n" +
                "create index idx_qrtz_ft_jg on qrtz_fired_triggers(sched_name,job_group);\n" +
                "create index idx_qrtz_ft_t_g on qrtz_fired_triggers(sched_name,trigger_name,trigger_group);\n" +
                "create index idx_qrtz_ft_tg on qrtz_fired_triggers(sched_name,trigger_group);\n";

        for (String _sql : sql.split(";")) {
            _sql = _sql.trim().replace("\n", "");
            if (Utility.isNotEmpty(_sql)) {
                statement = connection.createStatement();
                statement.execute(_sql);
                statement.close();
            }
        }
    }
}
