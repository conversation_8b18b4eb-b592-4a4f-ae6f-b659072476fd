package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡云服务器关联")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderBalanceServerRelationBean.class})
public class UpOrderBalanceServerRelationBean extends SpRecordBean {

	@ApiModelProperty(value = "云服务器ID")
	private Integer cloudServerId;
	
	@ApiModelProperty(value = "负载均衡ID")
	private Integer orderLoadBalanceId;

	@ApiModelProperty(value = "权重")
	private Integer weight;

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public Integer getOrderLoadBalanceId() {
		return orderLoadBalanceId;
	}

	public void setOrderLoadBalanceId(Integer orderLoadBalanceId) {
		this.orderLoadBalanceId = orderLoadBalanceId;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}
	
}
