package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VPC")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVPCBean.class})
public class SpVPCBean extends SpRecordBean {
	
	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "租户ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "网段")
	private String networkSegment;

	@ApiModelProperty(value = "RouterId")
	private String routerId;

	@ApiModelProperty(value = "ecStatus")
	private String ecStatus;

	@ApiModelProperty(value = "项目ID")
	private String projectId;

	@ApiModelProperty(value = "租户ID")
	private String tenantId;
	
	private SpVPCRelationBean[] vpcRelation;
	
    @ApiModelProperty(value = "计算网络列表")
    private SpOVDCNetworkBean[] ovdcNetworkList;

    public SpOVDCNetworkBean[] getOvdcNetworkList() {
		return ovdcNetworkList;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public void setOvdcNetworkList(SpOVDCNetworkBean[] ovdcNetworkList) {
		this.ovdcNetworkList = ovdcNetworkList;
	}

	public SpVPCRelationBean[] getVpcRelation() {
		return vpcRelation;
	}

	public void setVpcRelation(SpVPCRelationBean[] vpcRelation) {
		this.vpcRelation = vpcRelation;
	}

	public String getNetworkSegment() {
		return networkSegment;
	}

	public void setNetworkSegment(String networkSegment) {
		this.networkSegment = networkSegment;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public String getRouterId() {
		return routerId;
	}

	public void setRouterId(String routerId) {
		this.routerId = routerId;
	}

    public String getEcStatus() {
        return ecStatus;
    }

    public void setEcStatus(String ecStatus) {
        this.ecStatus = ecStatus;
    }
}
