package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "部署参数（蓝图动态表单属性）")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, ReqPropertyXaasBean.class})
public class ReqPropertyXaasBean extends RecordBean {

    @ApiModelProperty(value = "部署请求ID")
    private Integer reqDeploymentId;

    @ApiModelProperty(value = "部署请求名称")
    private String reqDeploymentName;

    @ApiModelProperty(value = "蓝图动态表单属性ID")
    private Integer propertyId;

    @ApiModelProperty(value = "蓝图动态表单属性名称")
    private String propertyName;

    @ApiModelProperty(value = "蓝图动态表单属性显示名称")
    private String propertyDisplayName;

    @ApiModelProperty(value = "属性值")
    private String value;

    public Integer getReqDeploymentId() {
        return reqDeploymentId;
    }

    public void setReqDeploymentId(Integer reqDeploymentId) {
        this.reqDeploymentId = reqDeploymentId;
    }

    public String getReqDeploymentName() {
        return reqDeploymentName;
    }

    public void setReqDeploymentName(String reqDeploymentName) {
        this.reqDeploymentName = reqDeploymentName;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getPropertyDisplayName() {
        return propertyDisplayName;
    }

    public void setPropertyDisplayName(String propertyDisplayName) {
        this.propertyDisplayName = propertyDisplayName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
