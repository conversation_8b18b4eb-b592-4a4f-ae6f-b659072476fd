package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "申请单变更日志查询结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApplicationChangeLogResultBean extends ResultListBean<UpApplicationChangeLogBean> {

}
