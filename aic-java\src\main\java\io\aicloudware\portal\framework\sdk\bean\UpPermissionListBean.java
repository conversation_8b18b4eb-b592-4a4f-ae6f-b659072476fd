package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.UpDataListBean;
import io.aicloudware.portal.framework.sdk.contants.UpPermissionType;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "资源价格批量操作对象")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpPermissionListBean extends UpDataListBean<UpPermissionBean> {
    private Integer userTemplateId;
    private Integer userId;

    public Integer getUserTemplateId() {
        return userTemplateId;
    }

    public void setUserTemplateId(Integer userTemplateId) {
        this.userTemplateId = userTemplateId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
