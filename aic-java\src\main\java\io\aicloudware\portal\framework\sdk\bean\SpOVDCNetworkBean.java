package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpNetworkType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "OVDC网络配置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOVDCNetworkBean.class})
public class SpOVDCNetworkBean extends SpRecordBean {

	@ApiModelProperty(value = "OVDC ID")
	private Integer spOrgId;

    @ApiModelProperty(value = "OVDC ID")
    private Integer ovdcId;

    @ApiModelProperty(value = "OVDC名称")
    private String ovdcName;
    
    @ApiModelProperty(value = "网络状态")
    private String networkStatus;

	@ApiModelProperty(value = "RouterId")
	private String routerId;
    
    @ApiModelProperty(value = "类型")
    private SpNetworkType type;

    private SpIpScopeBean[] ipScopeList;

	public Integer getOvdcId() {
		return ovdcId;
	}

	public void setOvdcId(Integer ovdcId) {
		this.ovdcId = ovdcId;
	}

	public String getOvdcName() {
		return ovdcName;
	}

	public void setOvdcName(String ovdcName) {
		this.ovdcName = ovdcName;
	}

	public SpIpScopeBean[] getIpScopeList() {
		return ipScopeList;
	}

	public void setIpScopeList(SpIpScopeBean[] ipScopeList) {
		this.ipScopeList = ipScopeList;
	}

	public String getNetworkStatus() {
		return networkStatus;
	}

	public void setNetworkStatus(String networkStatus) {
		this.networkStatus = networkStatus;
	}

	public SpNetworkType getType() {
		return type;
	}

	public void setType(SpNetworkType type) {
		this.type = type;
	}


	public String getRouterId() {
		return routerId;
	}

	public void setRouterId(String routerId) {
		this.routerId = routerId;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}
}
