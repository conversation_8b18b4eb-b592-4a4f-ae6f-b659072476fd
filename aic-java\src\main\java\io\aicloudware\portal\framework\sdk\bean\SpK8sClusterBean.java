package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterContainerNetworkMode;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterPhase;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterSubType;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "K8S集群")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpK8sClusterBean.class})
public class SpK8sClusterBean extends SpRecordBean {

	@ApiModelProperty(value = "业务租户ID", position = 40)
    private SpK8sClusterType k8sClusterType;

    @ApiModelProperty(value = "k8s_cluster_sub_type")
    private SpK8sClusterSubType k8sClusterSubType;

    @ApiModelProperty(value ="uid")
    private String uid;

    @ApiModelProperty(value = "创建时间戳")
    private Date creationTimestamp;

    @ApiModelProperty(value = "更新时间戳")
    private Date updateTimestamp;

    @ApiModelProperty(value ="规格")
    private String flavor;

    @ApiModelProperty(value ="版本")
    private String version;

    @ApiModelProperty(value ="平台版本")
    private String platformVersion;

    @ApiModelProperty(value = "VPC名称")
    private String spVpcName;

    @ApiModelProperty(value = "VPCID")
    private Integer spVpcId;

    @ApiModelProperty(value = "子网名称")
    private String subnetName;

    @ApiModelProperty(value = "子网ID")
    private Integer subnetId;

    @ApiModelProperty(value = "安全组名称")
    private String securityGroupName;

    @ApiModelProperty(value = "安全组ID")
    private Integer securityGroupId;

    @ApiModelProperty(value = "控制平面安全组名称")
    private String controlSecurityGroupName;

    @ApiModelProperty(value = "控制平面安全组ID")
    private Integer controlSecurityGroupId;

    @ApiModelProperty(value ="phase")
    private SpK8sClusterPhase phase;

    @ApiModelProperty(value ="containerNetworkMode")
    private SpK8sClusterContainerNetworkMode containerNetworkMode;

    @ApiModelProperty(value = "container_network_cidr")
    private String containerNetworkCidr;

    @ApiModelProperty(value = "service_network_ipv4_cidr")
    private String serviceNetworkIpv4Cidr;

    @ApiModelProperty(value = "authentication_mode")
    private String authenticationMode;

    @ApiModelProperty(value = "kube_proxy_mode")
    private String kubeProxyMode;

    @ApiModelProperty(value = "集群节点列表")
    private SpK8sClusterNodeBean[] nodeList;

    public SpK8sClusterPhase getPhase() {
        return phase;
    }

    public void setPhase(SpK8sClusterPhase phase) {
        this.phase = phase;
    }

    public String getSpVpcName() {
        return spVpcName;
    }

    public void setSpVpcName(String spVpcName) {
        this.spVpcName = spVpcName;
    }

    public Integer getSpVpcId() {
        return spVpcId;
    }

    public void setSpVpcId(Integer spVpcId) {
        this.spVpcId = spVpcId;
    }

    public String getSubnetName() {
        return subnetName;
    }

    public void setSubnetName(String subnetName) {
        this.subnetName = subnetName;
    }

    public Integer getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(Integer subnetId) {
        this.subnetId = subnetId;
    }

    public String getSecurityGroupName() {
        return securityGroupName;
    }

    public void setSecurityGroupName(String securityGroupName) {
        this.securityGroupName = securityGroupName;
    }

    public Integer getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(Integer securityGroupId) {
        this.securityGroupId = securityGroupId;
    }

    public String getControlSecurityGroupName() {
        return controlSecurityGroupName;
    }

    public void setControlSecurityGroupName(String controlSecurityGroupName) {
        this.controlSecurityGroupName = controlSecurityGroupName;
    }

    public Integer getControlSecurityGroupId() {
        return controlSecurityGroupId;
    }

    public void setControlSecurityGroupId(Integer controlSecurityGroupId) {
        this.controlSecurityGroupId = controlSecurityGroupId;
    }

    public String getPlatformVersion() {
        return platformVersion;
    }

    public void setPlatformVersion(String platformVersion) {
        this.platformVersion = platformVersion;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getFlavor() {
        return flavor;
    }

    public void setFlavor(String flavor) {
        this.flavor = flavor;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public Date getCreationTimestamp() {
        return creationTimestamp;
    }

    public void setCreationTimestamp(Date creationTimestamp) {
        this.creationTimestamp = creationTimestamp;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public SpK8sClusterSubType getK8sClusterSubType() {
        return k8sClusterSubType;
    }

    public void setK8sClusterSubType(SpK8sClusterSubType k8sClusterSubType) {
        this.k8sClusterSubType = k8sClusterSubType;
    }

    public SpK8sClusterType getK8sClusterType() {
        return k8sClusterType;
    }

    public void setK8sClusterType(SpK8sClusterType k8sClusterType) {
        this.k8sClusterType = k8sClusterType;
    }

    public SpK8sClusterContainerNetworkMode getContainerNetworkMode() {
        return containerNetworkMode;
    }

    public void setContainerNetworkMode(SpK8sClusterContainerNetworkMode containerNetworkMode) {
        this.containerNetworkMode = containerNetworkMode;
    }

    public String getContainerNetworkCidr() {
        return containerNetworkCidr;
    }

    public void setContainerNetworkCidr(String containerNetworkCidr) {
        this.containerNetworkCidr = containerNetworkCidr;
    }

    public String getServiceNetworkIpv4Cidr() {
        return serviceNetworkIpv4Cidr;
    }

    public void setServiceNetworkIpv4Cidr(String serviceNetworkIpv4Cidr) {
        this.serviceNetworkIpv4Cidr = serviceNetworkIpv4Cidr;
    }

    public String getAuthenticationMode() {
        return authenticationMode;
    }

    public void setAuthenticationMode(String authenticationMode) {
        this.authenticationMode = authenticationMode;
    }

    public String getKubeProxyMode() {
        return kubeProxyMode;
    }

    public void setKubeProxyMode(String kubeProxyMode) {
        this.kubeProxyMode = kubeProxyMode;
    }

    public SpK8sClusterNodeBean[] getNodeList() {
        return nodeList;
    }

    public void setNodeList(SpK8sClusterNodeBean[] nodeList) {
        this.nodeList = nodeList;
    }
}
