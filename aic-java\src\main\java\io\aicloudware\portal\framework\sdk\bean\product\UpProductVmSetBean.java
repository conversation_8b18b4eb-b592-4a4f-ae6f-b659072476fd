package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpSecurityVersion;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VM产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductVmSetBean extends RecordBean {
	
	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "CPU单位数")
	private Integer cpuUnit;
	
	@ApiModelProperty(value = "CPU价格")
	private BigDecimal cpuPrice;
	
	@ApiModelProperty(value = "内存单位数")
	private Integer memoryUnit;
	
	@ApiModelProperty(value = "内存价格")
	private BigDecimal memoryPrice;
	
	@ApiModelProperty(value = "单位数")
	private Integer diskUnit;
	
	@ApiModelProperty(value = "价格")
	private BigDecimal diskPrice;
	
	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;
	
	@ApiModelProperty(value = "云服务类型")
	private ServerType serverType;

	@ApiModelProperty(value = "info")
	private String info;
	
	@ApiModelProperty(value = "云安全产品编码")
	private String securityCode;

	@ApiModelProperty(value = "tempalte")
	private Integer templateId;

	@ApiModelProperty(value = "template")
	private String templateName;

	private UpOrderSystemEnums.ProductVmSetType type;

	@ApiModelProperty(value = "云安全版本")
	private SpSecurityVersion securityVersion;
	
	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getCpuUnit() {
		return cpuUnit;
	}

	public void setCpuUnit(Integer cpuUnit) {
		this.cpuUnit = cpuUnit;
	}

	public BigDecimal getCpuPrice() {
		return cpuPrice;
	}

	public void setCpuPrice(BigDecimal cpuPrice) {
		this.cpuPrice = cpuPrice;
	}

	public Integer getMemoryUnit() {
		return memoryUnit;
	}

	public void setMemoryUnit(Integer memoryUnit) {
		this.memoryUnit = memoryUnit;
	}

	public BigDecimal getMemoryPrice() {
		return memoryPrice;
	}

	public void setMemoryPrice(BigDecimal memoryPrice) {
		this.memoryPrice = memoryPrice;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public ServerType getServerType() {
		return serverType;
	}

	public void setServerType(ServerType serverType) {
		this.serverType = serverType;
	}

	public Integer getDiskUnit() {
		return diskUnit;
	}

	public void setDiskUnit(Integer diskUnit) {
		this.diskUnit = diskUnit;
	}

	public BigDecimal getDiskPrice() {
		return diskPrice;
	}

	public void setDiskPrice(BigDecimal diskPrice) {
		this.diskPrice = diskPrice;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public Integer getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	public String getSecurityCode() {
		return securityCode;
	}

	public void setSecurityCode(String securityCode) {
		this.securityCode = securityCode;
	}

	public SpSecurityVersion getSecurityVersion() {
		return securityVersion;
	}

	public void setSecurityVersion(SpSecurityVersion securityVersion) {
		this.securityVersion = securityVersion;
	}

	public UpOrderSystemEnums.ProductVmSetType getType() {
		return type;
	}

	public void setType(UpOrderSystemEnums.ProductVmSetType type) {
		this.type = type;
	}
}
