package io.aicloudware.portal.framework.sdk.bean;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "审批场景设定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApprovalSceneBean extends RecordBean {
    @ApiModelProperty(value = "应用系统所属部门ID", position = 60)
    private Integer appSystemGroupId;

    @ApiModelProperty(value = "应用系统所属部门名称", position = 70)
    private String appSystemGroupName;

    @ApiModelProperty(value = "应用系统类别ID", position = 80)
    private Integer appSystemTypeId;

    @ApiModelProperty(value = "应用系统类别名称", position = 90)
    private String appSystemTypeName;

    @ApiModelProperty(value = "申请类别", position = 100)
    private UpApplicationType applicationType;

    @ApiModelProperty(value = "审批流程ID", position = 110)
    private Integer approvalProcessId;

    @ApiModelProperty(value = "审批流程名称", position = 120)
    private String approvalProcessName;

    @ApiModelProperty(value = "优先级", position = 130)
    private Integer priority;

    public Integer getAppSystemGroupId() {
        return appSystemGroupId;
    }

    public void setAppSystemGroupId(Integer appSystemGroupId) {
        this.appSystemGroupId = appSystemGroupId;
    }

    public String getAppSystemGroupName() {
        return appSystemGroupName;
    }

    public void setAppSystemGroupName(String appSystemGroupName) {
        this.appSystemGroupName = appSystemGroupName;
    }

    public Integer getAppSystemTypeId() {
        return appSystemTypeId;
    }

    public void setAppSystemTypeId(Integer appSystemTypeId) {
        this.appSystemTypeId = appSystemTypeId;
    }

    public String getAppSystemTypeName() {
        return appSystemTypeName;
    }

    public void setAppSystemTypeName(String appSystemTypeName) {
        this.appSystemTypeName = appSystemTypeName;
    }

    public UpApplicationType getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(UpApplicationType applicationType) {
        this.applicationType = applicationType;
    }

    public Integer getApprovalProcessId() {
        return approvalProcessId;
    }

    public void setApprovalProcessId(Integer approvalProcessId) {
        this.approvalProcessId = approvalProcessId;
    }

    public String getApprovalProcessName() {
        return approvalProcessName;
    }

    public void setApprovalProcessName(String approvalProcessName) {
        this.approvalProcessName = approvalProcessName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

}
