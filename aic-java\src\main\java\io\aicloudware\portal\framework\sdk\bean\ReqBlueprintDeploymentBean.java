package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpVmProvisionType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "蓝图部署请求")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, ReqBlueprintDeploymentBean.class})
public class ReqBlueprintDeploymentBean extends RecordBean {

    @ApiModelProperty(value = "同时申请多个相同的多机蓝图时必填，作为区分虚机分组的唯一标识")
    private Integer rootDeploymentId;

    @ApiModelProperty(value = "蓝图ID")
    private Integer blueprintId;

    @ApiModelProperty(value = "蓝图名称")
    private String blueprintDisplayName;

    @ApiModelProperty(value = "蓝图机器关系ID")
    private Integer blueprintMachineRelationId;

    @ApiModelProperty(value = "蓝图机器名称")
    private String blueprintMachineDisplayName;

    @ApiModelProperty(value = "预留ID")
    private Integer reservationId;

    @ApiModelProperty(value = "预留名称")
    private String reservationDisplayName;

    @ApiModelProperty(value = "虚机说明")
    private String vmComment;

    @ApiModelProperty(value = "CPU(核)")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存(GB)")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘（GB）")
    private Integer diskGB;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "置备类型(THIN,THICK)")
    private SpVmProvisionType provisionType;

    @ApiModelProperty(value = "磁盘列表")
    private SpVmDiskBean[] diskList;

    @ApiModelProperty(value = "网络列表")
    private SpVmNetworkBean[] networkList;

    @ApiModelProperty(value = "属性组列表")
    private ReqPropertyGroupBean[] propertyGroupList;

    @ApiModelProperty(value = "XAAS服务属性列表")
    private ReqPropertyXaasBean[] propertyXaasList;

    public Integer getRootDeploymentId() {
        return rootDeploymentId;
    }

    public void setRootDeploymentId(Integer rootDeploymentId) {
        this.rootDeploymentId = rootDeploymentId;
    }

    public Integer getBlueprintId() {
        return blueprintId;
    }

    public void setBlueprintId(Integer blueprintId) {
        this.blueprintId = blueprintId;
    }

    public String getBlueprintDisplayName() {
        return blueprintDisplayName;
    }

    public void setBlueprintDisplayName(String blueprintDisplayName) {
        this.blueprintDisplayName = blueprintDisplayName;
    }

    public Integer getBlueprintMachineRelationId() {
        return blueprintMachineRelationId;
    }

    public void setBlueprintMachineRelationId(Integer blueprintMachineRelationId) {
        this.blueprintMachineRelationId = blueprintMachineRelationId;
    }

    public String getBlueprintMachineDisplayName() {
        return blueprintMachineDisplayName;
    }

    public void setBlueprintMachineDisplayName(String blueprintMachineDisplayName) {
        this.blueprintMachineDisplayName = blueprintMachineDisplayName;
    }

    public Integer getReservationId() {
        return reservationId;
    }

    public void setReservationId(Integer reservationId) {
        this.reservationId = reservationId;
    }

    public String getReservationDisplayName() {
        return reservationDisplayName;
    }

    public void setReservationDisplayName(String reservationDisplayName) {
        this.reservationDisplayName = reservationDisplayName;
    }

    public String getVmComment() {
        return vmComment;
    }

    public void setVmComment(String vmComment) {
        this.vmComment = vmComment;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpVmProvisionType getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(SpVmProvisionType provisionType) {
        this.provisionType = provisionType;
    }

    public SpVmDiskBean[] getDiskList() {
        return diskList;
    }

    public void setDiskList(SpVmDiskBean[] diskList) {
        this.diskList = diskList;
    }

    public SpVmNetworkBean[] getNetworkList() {
        return networkList;
    }

    public void setNetworkList(SpVmNetworkBean[] networkList) {
        this.networkList = networkList;
    }

    public ReqPropertyGroupBean[] getPropertyGroupList() {
        return propertyGroupList;
    }

    public void setPropertyGroupList(ReqPropertyGroupBean[] propertyGroupList) {
        this.propertyGroupList = propertyGroupList;
    }

    public ReqPropertyXaasBean[] getPropertyXaasList() {
        return propertyXaasList;
    }

    public void setPropertyXaasList(ReqPropertyXaasBean[] propertyXaasList) {
        this.propertyXaasList = propertyXaasList;
    }
}
