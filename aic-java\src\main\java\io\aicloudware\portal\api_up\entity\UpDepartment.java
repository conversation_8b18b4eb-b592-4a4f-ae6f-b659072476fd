package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IDisplayNameEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentBean;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "up_department")
@Access(AccessType.FIELD)
public final class UpDepartment extends BaseUpEntity<UpDepartmentBean> implements IDisplayNameEntity<UpDepartmentBean> {

    public UpDepartment() {
    }

    public UpDepartment(Integer id) {
        super(id);
    }

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "description")
    private String description;

    @Column(name = "department_code")
    private String departmentCode;

    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "is_active")
    private Boolean isActive;

    // 父部门关系 - 支持子部门结构
    @EntityProperty(isCopyOnUpdate = false)
    @JoinColumn(name = "parent_department_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpDepartment parentDepartment;

    // 子部门列表
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "parentDepartment")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "sort_order, id")
    private List<UpDepartment> childDepartments;

    // 部门用户关系列表
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "department")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "id")
    private List<UpDepartmentUserRelation> departmentUserRelations;

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public UpDepartment getParentDepartment() {
        return parentDepartment;
    }

    public void setParentDepartment(UpDepartment parentDepartment) {
        this.parentDepartment = parentDepartment;
    }

    public List<UpDepartment> getChildDepartments() {
        return childDepartments;
    }

    public void setChildDepartments(List<UpDepartment> childDepartments) {
        this.childDepartments = childDepartments;
    }

    public List<UpDepartmentUserRelation> getDepartmentUserRelations() {
        return departmentUserRelations;
    }

    public void setDepartmentUserRelations(List<UpDepartmentUserRelation> departmentUserRelations) {
        this.departmentUserRelations = departmentUserRelations;
    }
}
