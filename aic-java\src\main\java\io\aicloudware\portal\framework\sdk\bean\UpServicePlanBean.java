package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpServerConnectionType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "服务计划")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpServicePlanBean.class})
public class UpServicePlanBean extends SpRecordBean {

	@ApiModelProperty(value = "类型")
	private UpServicePlanType servicePlanType;

	@ApiModelProperty(value = "code")
	private String servicePlanCode;

	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	@ApiModelProperty(value = "自动开始日期")
	private Date autoEffectiveDate;

	@ApiModelProperty(value = "自动结束日期")
	private Date autoExpiryDate;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "修改前服务计划")
	private Integer sourceServicePlanId;

	@ApiModelProperty(value = "修改前服务计划")
	private String sourceServicePlanName;

	private UpServicePlanRegionRelationBean[] servicePlanRegionRelations;

	@ApiModelProperty(value = "服务计划配件")
	private UpServicePlanItemBean[] servicePlanItems;

//	@ApiModelProperty(value = "订阅")
//	private UpServicePlanSubscription servicePlanSubscription;

	private String cloudConnectionUuid;
	private String cloudConnectionName;
	private SpServerConnectionType cloudConnectionType;

	public UpServicePlanType getServicePlanType() {
		return servicePlanType;
	}

	public void setServicePlanType(UpServicePlanType servicePlanType) {
		this.servicePlanType = servicePlanType;
	}

	public String getServicePlanCode() {
		return servicePlanCode;
	}

	public void setServicePlanCode(String servicePlanCode) {
		this.servicePlanCode = servicePlanCode;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Date getAutoEffectiveDate() {
		return autoEffectiveDate;
	}

	public void setAutoEffectiveDate(Date autoEffectiveDate) {
		this.autoEffectiveDate = autoEffectiveDate;
	}

	public Date getAutoExpiryDate() {
		return autoExpiryDate;
	}

	public void setAutoExpiryDate(Date autoExpiryDate) {
		this.autoExpiryDate = autoExpiryDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getSourceServicePlanId() {
		return sourceServicePlanId;
	}

	public void setSourceServicePlanId(Integer sourceServicePlanId) {
		this.sourceServicePlanId = sourceServicePlanId;
	}

	public String getSourceServicePlanName() {
		return sourceServicePlanName;
	}

	public void setSourceServicePlanName(String sourceServicePlanName) {
		this.sourceServicePlanName = sourceServicePlanName;
	}

	public UpServicePlanItemBean[] getServicePlanItems() {
		return servicePlanItems;
	}

	public void setServicePlanItems(UpServicePlanItemBean[] servicePlanItems) {
		this.servicePlanItems = servicePlanItems;
	}

	public UpServicePlanRegionRelationBean[] getServicePlanRegionRelations() {
		return servicePlanRegionRelations;
	}

	public void setServicePlanRegionRelations(UpServicePlanRegionRelationBean[] servicePlanRegionRelations) {
		this.servicePlanRegionRelations = servicePlanRegionRelations;
	}

	public String getCloudConnectionUuid() {
		return cloudConnectionUuid;
	}

	public void setCloudConnectionUuid(String cloudConnectionUuid) {
		this.cloudConnectionUuid = cloudConnectionUuid;
	}

	public String getCloudConnectionName() {
		return cloudConnectionName;
	}

	public void setCloudConnectionName(String cloudConnectionName) {
		this.cloudConnectionName = cloudConnectionName;
	}

	public SpServerConnectionType getCloudConnectionType() {
		return cloudConnectionType;
	}

	public void setCloudConnectionType(SpServerConnectionType cloudConnectionType) {
		this.cloudConnectionType = cloudConnectionType;
	}
}
