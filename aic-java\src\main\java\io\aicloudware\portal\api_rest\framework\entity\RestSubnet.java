package io.aicloudware.portal.api_rest.framework.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_rest.framework.bean.RestSubnetBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;

@Entity
@Table(name = "rest_subnet")
@Access(AccessType.FIELD)
public class RestSubnet extends BaseUpEntity<RestSubnetBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1677088501508824420L;

	@JoinColumn(name = "vpc_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestVpc vpc;
	
	@JoinColumn(name = "vpc_info_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestVpcInfo vpcInfo;
	
	@Column(name = "subnet_segment")
	private String subnetSegment;
	
	@Column(name = "operation_type")
	private String operationType; //1=新增，2=删除

	public RestVpc getVpc() {
		return vpc;
	}

	public void setVpc(RestVpc vpc) {
		this.vpc = vpc;
	}

	public String getSubnetSegment() {
		return subnetSegment;
	}

	public void setSubnetSegment(String subnetSegment) {
		this.subnetSegment = subnetSegment;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public RestVpcInfo getVpcInfo() {
		return vpcInfo;
	}

	public void setVpcInfo(RestVpcInfo vpcInfo) {
		this.vpcInfo = vpcInfo;
	}
	
}
	