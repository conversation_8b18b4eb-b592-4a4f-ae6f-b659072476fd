package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ChargePeriod;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ElasticIpChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ResourceType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_elastic_ip")
@Access(AccessType.FIELD)
//public class UpOrderElasticIp extends BaseUpEntity<UpOrderElasticIpBean> implements IRequestEntity<UpOrderElasticIpBean> {
public class UpOrderElasticIp extends UpOrderProduct<UpOrderElasticIpBean> implements IOrderEntity{
	
	@Column(name = "payment_type")
    @Enumerated(EnumType.STRING)
	private PaymentType paymentType;
	
	@Column(name = "charge_type")
    @Enumerated(EnumType.STRING)
	private ElasticIpChargeType chargeType;
	
	@Column(name = "bandwidth")
	private Integer bandwidth;
	
	@Column(name = "charge_period")
    @Enumerated(EnumType.STRING)
	private ChargePeriod chargePeriod;
	
	@Column(name = "resource_type")
    @Enumerated(EnumType.STRING)
	private ResourceType resourceType;
	
	@JoinColumn(name = "order_cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrderCloudServer orderCloudServer;
	
	@JoinColumn(name = "order_load_balance_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrderLoadBalance orderLoadBalance;
	
	@Column(name = "elastic_ip")
	private String elasticIP;
	
	@Column(name = "cloud_server_id")
	private Integer cloudServerId;
	
	@Column(name = "load_balance_id")
	private Integer loadBalanceId;
	
	@JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
	
	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
	
	@Column(name = "bandwidth_config_id")
	private Integer bandwidthConfigId;
	
	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
    @Column(name = "task_sequence")
    private Integer taskSequence;

	@Override
	public UpAppSystem getAppSystem() {
		return appSystem;
	}

	@Override
	public void setAppSystem(UpAppSystem appSystem) {
		this.appSystem = appSystem;
	}

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "app_system_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpAppSystem appSystem;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public ElasticIpChargeType getChargeType() {
		return chargeType;
	}

	public void setChargeType(ElasticIpChargeType chargeType) {
		this.chargeType = chargeType;
	}

	public Integer getBandwidth() {
		return bandwidth;
	}

	public void setBandwidth(Integer bandwidth) {
		this.bandwidth = bandwidth;
	}

	public ChargePeriod getChargePeriod() {
		return chargePeriod;
	}

	public void setChargePeriod(ChargePeriod chargePeriod) {
		this.chargePeriod = chargePeriod;
	}

	public ResourceType getResourceType() {
		return resourceType;
	}

	public void setResourceType(ResourceType resourceType) {
		this.resourceType = resourceType;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public UpOrderCloudServer getOrderCloudServer() {
		return orderCloudServer;
	}

	public void setOrderCloudServer(UpOrderCloudServer orderCloudServer) {
		this.orderCloudServer = orderCloudServer;
	}

	public UpOrderLoadBalance getOrderLoadBalance() {
		return orderLoadBalance;
	}

	public void setOrderLoadBalance(UpOrderLoadBalance orderLoadBalance) {
		this.orderLoadBalance = orderLoadBalance;
	}

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public Integer getLoadBalanceId() {
		return loadBalanceId;
	}

	public void setLoadBalanceId(Integer loadBalanceId) {
		this.loadBalanceId = loadBalanceId;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public Integer getBandwidthConfigId() {
		return bandwidthConfigId;
	}

	public void setBandwidthConfigId(Integer bandwidthConfigId) {
		this.bandwidthConfigId = bandwidthConfigId;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

    public Integer getTaskSequence() {
        return taskSequence;
    }

    public void setTaskSequence(Integer taskSequence) {
        this.taskSequence = taskSequence;
    }

	public String getElasticIP() {
		return elasticIP;
	}

	public void setElasticIP(String elasticIP) {
		this.elasticIP = elasticIP;
	}
    
    

}
