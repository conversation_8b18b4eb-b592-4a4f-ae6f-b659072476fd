package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemSubType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.math.BigDecimal;

@ApiModel(value = "服务计划配件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpServicePlanItemBean.class})
public class UpServicePlanItemBean extends SpRecordBean {

    @ApiModelProperty(value = "配件类型")
    private UpServicePlanItemType servicePlanItemType;

    @ApiModelProperty(value = "配件类型")
    private String servicePlanItemTypeTxt;

    @ApiModelProperty(value = "子类型")
    private UpServicePlanItemSubType servicePlanItemSubType;

    private String servicePlanItemSubTypeTxt;

    @ApiModelProperty(value = "数量")
    private Integer amount;

    @ApiModelProperty(value = "服务计划")
    private UpServicePlan servicePlan;

    private Integer imageId;
    private String imageName;

    public UpServicePlanItemType getServicePlanItemType() {
        return servicePlanItemType;
    }

    public void setServicePlanItemType(UpServicePlanItemType servicePlanItemType) {
        this.servicePlanItemType = servicePlanItemType;
    }

    public UpServicePlanItemSubType getServicePlanItemSubType() {
        return servicePlanItemSubType;
    }

    public void setServicePlanItemSubType(UpServicePlanItemSubType servicePlanItemSubType) {
        this.servicePlanItemSubType = servicePlanItemSubType;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public UpServicePlan getServicePlan() {
        return servicePlan;
    }

    public void setServicePlan(UpServicePlan servicePlan) {
        this.servicePlan = servicePlan;
    }

    public Integer getImageId() {
        return imageId;
    }

    public void setImageId(Integer imageId) {
        this.imageId = imageId;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getServicePlanItemTypeTxt() {
        return servicePlanItemTypeTxt;
    }

    public void setServicePlanItemTypeTxt(String servicePlanItemTypeTxt) {
        this.servicePlanItemTypeTxt = servicePlanItemTypeTxt;
    }

    public String getServicePlanItemSubTypeTxt() {
        return servicePlanItemSubTypeTxt;
    }

    public void setServicePlanItemSubTypeTxt(String servicePlanItemSubTypeTxt) {
        this.servicePlanItemSubTypeTxt = servicePlanItemSubTypeTxt;
    }
}
