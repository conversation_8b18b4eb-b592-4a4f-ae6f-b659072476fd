package io.aicloudware.portal.api_rest.service;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public interface IRestMessageService{

	void createOrderMessage(UpOrder order);

	void createVPCMessage(SpVPC vpc, List<SpOVDCNetwork> ovdcNetworkList);
	
	void sendMessage();
	
	void createMontorMessage(String customNo, String spUuid, String title, String content);

	void updateInstanceMessage(SpRegionEntity region, BaseSpEntity entity, Boolean isSuccess, String content, String ssoUserId, String code, String subCode, UpProductSystemEnums.QuotaDetailChannel channel);
	
	void deleteInstanceMessage(Class clz, Integer id);

	Boolean isWebsiteApprove(String customNo, String ip);

	void createOrgMessage(String customNo, String messageType, Boolean isSuccess, String remark);


}
