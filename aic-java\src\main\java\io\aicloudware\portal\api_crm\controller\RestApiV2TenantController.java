package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestTenantService;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserResultBean;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.LinkedHashMap;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2/tenant")
@Api(value = "/api/v2/tenant", description = "REST API V2")
public class RestApiV2TenantController extends BaseUpController<UpUser, UpUserBean, UpUserResultBean> {

    @Autowired
    private IRestTenantService restTenantService;

    @Autowired
    private ISpRegionService spRegionService;


    @RequestMapping(value = "/regions", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean changeRegion() {
        LinkedHashMap<String, String> regionMap = new LinkedHashMap<>();
        for (SpRegionBean region : spRegionService.list()) {
            regionMap.put(region.getName(), region.getTitle());
        }

        return ResponseBean.success(regionMap);
    }

}
