package io.aicloudware.portal.api_vcpp.service.order;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderBalanceServerRelation;
import io.aicloudware.portal.api_vcpp.entity.UpOrderElasticIp;
import io.aicloudware.portal.api_vcpp.entity.UpOrderLoadBalance;
import io.aicloudware.portal.api_vcpp.entity.UpOrderMonitor;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.service.finance.IUpFinanceRechargeService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Service
@Transactional
public class UpOrderLoadBalanceService extends BaseService implements IUpOrderLoadBalanceService {

	@Autowired
	private IUpFinanceRechargeService financeRechargeService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Override
	public Integer save(UpOrderLoadBalanceBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getPaymentType(), "请选择付费方式！");
		AssertUtil.check(bean.getName(), "请输入实例名！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getName()), "实例名只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getName()), "实例名必须包含字母！");
//		AssertUtil.check(bean.getElasticIpList().length>0 && bean.getElasticIpList()[0].getId() !=null,"请输入公网IP！");
		
		AssertUtil.check(bean.getVip() !=null,"请输入VIP！");
//		UpUser user = ThreadCache.getUser();
//		String sql = "select 1 from sp_ip_binding where sp_org_id= " + user.getOrg().getId() + " and status <> '" + RecordStatus.deleted
//				+ "' and  elastic_ip_id="+bean.getElasticIpList()[0].getId();
//		List<Object[]> datas = queryDao.querySql(sql, null);
//		AssertUtil.check(datas.size()==0,"公网IP已使用！");
		
		AssertUtil.check(bean.getVpcId(), "请选择VPC！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在或已删除！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC数据异常！");
		AssertUtil.check(bean.getBalanceServerRelationList() != null && bean.getBalanceServerRelationList().length >= 1 , "请添加云服务器！");
//		AssertUtil.check(bean.getLoadBalanceConfigId(), "缺少负载均衡产品配置！");

		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_load_balance, user.getId()) == 0, "您有未完成的负载均衡申请！");
		String name = bean.getName()+"-"+System.currentTimeMillis() + String.format("%04d",(int)(Math.random()*1000));
		UpOrderLoadBalance entity = BeanCopyUtil.copy(bean, UpOrderLoadBalance.class);
		entity.setOwner(user);
		entity.setName(name);
		entity.setSpOrg(user.getOrg());
		if(entity.getBalanceServerRelationList()!=null && entity.getBalanceServerRelationList().size()>0) {
			Map<Integer,Object> map = new HashMap<>();
			for(UpOrderBalanceServerRelation relation : entity.getBalanceServerRelationList()) {
				AssertUtil.check(relation, "添加云服务器异常！");
				AssertUtil.check(relation.getCloudServerId(), "请选择云服务器！");
				AssertUtil.check(relation.getWeight() !=null, "请输入权重！");
				AssertUtil.check(relation.getWeight() >=0 && relation.getWeight() <= 100, "请输入0-100的权重数字！");
				AssertUtil.check(!map.containsKey(relation.getCloudServerId()),"重复选择了同一台云服务器！");
				map.put(relation.getCloudServerId(),"");
				
				SpVm vm = this.dao.load(SpVm.class,relation.getCloudServerId());
				AssertUtil.check(vm, "云服务器信息异常！");
				AssertUtil.check(vm.getSpOrg().getId().equals(user.getOrg().getId()),"云服务器所属用户组异常！");
//				String sql = "select count(1) from sp_ip_binding where vm_id = "+relation.getCloudServerId()+" and status <> '"+RecordStatus.deleted+"'";
//				int num = ListUtil.first(this.queryDao.querySql(sql, null));
//				AssertUtil.check(num == 0 , "所选云服务器已绑定负载均衡！");
				
				relation.setName(name);
			}
		}
		
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_load_balance);
		order.setName("[" + OrderType.new_load_balance + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setPaymentType(bean.getPaymentType());
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		if(entity.getMonitorList()!=null && entity.getMonitorList().size()>0) {
			for(UpOrderMonitor monitor : entity.getMonitorList()) {
				AssertUtil.check(monitor, "添加监听异常！");
				AssertUtil.check(monitor.getName(), "请输入监听名称！");
				AssertUtil.check(monitor.getFrontLayer(), "请选择前端协议！");
				AssertUtil.check(monitor.getFrontPort(), "请输入前端端口号！");
				AssertUtil.check(monitor.getBalanceArithmetic(), "请选择均衡算法！");
				AssertUtil.check(monitor.getBackendLayer(), "请选择后端云服务器协议！");
				AssertUtil.check(monitor.getBackendPort(), "请选择后端云服务器端口号！");
				AssertUtil.check(monitor.getInterval(), "输入间隔时间！");
				AssertUtil.check(monitor.getTimeout(), "输入超时时间！");
				AssertUtil.check(monitor.getMaxNumber(), "输入最大重试次数！");
				monitor.setOrder(order);
				monitor.setName(name);
				monitor.setOrg(user.getOrg());
				monitor.setRegion(order.getRegion());
			}
		}
		
		UpOrderElasticIp newElasticIp = null;
		
		entity.setOrder(order);
		if(entity.getVip()!=null) {
			List<Object[]> vsList = queryDao.querySql("select * from sp_load_balancer_virtual_server where sp_org_id="+user.getOrg().getId()+" and status='"+RecordStatus.active+"' and ip_address='"+entity.getVip()+"'",null);
			AssertUtil.check(vsList.size()==0,"所选VIP已被使用！");
//			AssertUtil.check(entity.getElasticIpList().get(0)!=null , "请输入正确的弹性公网IP信息！");
//			UpOrderElasticIp elasticIpEntity = entity.getElasticIpList().get(0);
//			
//			if(elasticIpEntity.getId()==null) {
//				elasticIpEntity.setChargePeriod(ChargePeriod.hour);
//				elasticIpEntity.setName(name);
//				elasticIpEntity.setOwner(user);
//				elasticIpEntity.setPaymentType(bean.getPaymentType());
//				elasticIpEntity.setResourceType(ResourceType.loadBalance);
//				elasticIpEntity.setOrderLoadBalance(entity);
//				elasticIpEntity.setOrder(order);
//				elasticIpEntity.setSpOrg(user.getOrg());
//				entity.getElasticIpList().set(0, elasticIpEntity);
//				AssertUtil.check(elasticIpEntity.getBandwidthConfigId(), "缺少带宽产品配置！");
//				
//				UpProductBandwidthSet bandwidthSet = this.dao.load(UpProductBandwidthSet.class, elasticIpEntity.getBandwidthConfigId());
//				AssertUtil.check(bandwidthSet != null && bandwidthSet.getEnabled(), "带宽产品配置信息异常！");
//				order.setBandwidthNum(elasticIpEntity.getBandwidth());
//				order.setBandwidthPrice(bandwidthSet.getPrice().multiply(BigDecimal.valueOf(elasticIpEntity.getBandwidth())));
//				order.setBandwidthSet(bandwidthSet);
//				newElasticIp = elasticIpEntity;
////				UpProductLoadBalanceSet loadBalanceSet = this.dao.load(UpProductLoadBalanceSet.class, entity.getLoadBalanceConfigId());
////				AssertUtil.check(loadBalanceSet != null && loadBalanceSet.getEnabled(), "负载均衡产品配置信息异常！");
////				order.setLoadBalanceNum(elasticIpEntity.getBandwidth());
////				order.setLoadBalancePrice(loadBalanceSet.getPrice().multiply(BigDecimal.valueOf(elasticIpEntity.getBandwidth())));
//			}else if(elasticIpEntity.getId()!=null) {
//				SpElasticIp elasticIp = this.dao.load(SpElasticIp.class,elasticIpEntity.getId());
//				AssertUtil.check(elasticIp, "弹性公网IP信息异常！");
//				AssertUtil.check(elasticIp.getSpOrg().getId().equals(user.getOrg().getId()),"弹性公网IP所属用户组异常！");
//				if(elasticIp.getIpBinding()!=null && elasticIp.getIpBinding().size()>0) {
//					for(SpIpBinding binding: elasticIp.getIpBinding()) {
//						AssertUtil.check(binding.getStatus().equals(RecordStatus.active),"所选弹性公网IP已被使用！");
//					}
//				}
//				List<Object[]> vsList = queryDao.querySql("select * from sp_load_balancer_virtual_server where sp_org_id="+elasticIp.getSpOrg().getId()+" and status='"+RecordStatus.active+"' and ip_address='"+elasticIp.getIpAddress()+"'",null);
//				AssertUtil.check(vsList.size()==0,"所选弹性公网IP已被使用！");
//				entity.setElasticIpList(null);
////				UpProductLoadBalanceSet loadBalanceSet = this.dao.load(UpProductLoadBalanceSet.class, entity.getLoadBalanceConfigId());
////				AssertUtil.check(loadBalanceSet != null && loadBalanceSet.getEnabled(), "负载均衡产品配置信息异常！");
//				entity.setElasticIpId(elasticIpEntity.getId());
//				entity.setElasticIpList(null);
//			}
		}
		
		// 
		this.dao.insert(order);
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setSpOrg(user.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);

        quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}
	
}
