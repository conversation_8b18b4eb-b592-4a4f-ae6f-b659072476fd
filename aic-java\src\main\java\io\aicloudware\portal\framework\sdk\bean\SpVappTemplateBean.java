package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VAppTemplate")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVappTemplateBean.class})
public class SpVappTemplateBean extends SpRecordBean {

    @ApiModelProperty(value = "所属服务目录分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "所属服务目录分类名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String categoryName;

    @ApiModelProperty(value = "所属服务目录分类显示名称")
    private String categoryDisplayName;

    @ApiModelProperty(value = "描述")
    private String description;
    
    @ApiModelProperty(value = "磁盘大小")
    private Long diskSize;
    
    @ApiModelProperty(value = "CPU")
    private Long cpu;
    
    @ApiModelProperty(value = "内存")
    private Long memory;
    
    @ApiModelProperty(value = "虚拟机ID")
	private String vmId;
	
    @ApiModelProperty(value = "虚拟机HREF")
	private String vmHref;
    
    @ApiModelProperty(value = "虚机列表")
    private SpVappTemplateMachineBean[] vmList;

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryDisplayName() {
        return categoryDisplayName;
    }

    public void setCategoryDisplayName(String categoryDisplayName) {
        this.categoryDisplayName = categoryDisplayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

	public Long getDiskSize() {
		return diskSize;
	}

	public void setDiskSize(Long diskSize) {
		this.diskSize = diskSize;
	}

	public Long getCpu() {
		return cpu;
	}

	public void setCpu(Long cpu) {
		this.cpu = cpu;
	}

	public Long getMemory() {
		return memory;
	}

	public void setMemory(Long memory) {
		this.memory = memory;
	}

	public String getVmId() {
		return vmId;
	}

	public void setVmId(String vmId) {
		this.vmId = vmId;
	}

	public String getVmHref() {
		return vmHref;
	}

	public void setVmHref(String vmHref) {
		this.vmHref = vmHref;
	}

    public SpVappTemplateMachineBean[] getVmList() {
        return vmList;
    }

    public void setVmList(SpVappTemplateMachineBean[] vmList) {
        this.vmList = vmList;
    }
    
	
    
}
