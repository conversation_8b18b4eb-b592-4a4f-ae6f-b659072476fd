package io.aicloudware.portal.api_vcpp.controller.order;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.entity.UpProductCceSet;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderCloudServerService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderK8sClusterService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterNodePoolBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.*;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * K8s集群申请
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/k8scluster_nodepool")
public class UpOrderK8sClusterNodePoolController extends BaseController {

	@Autowired
	private IUpOrderK8sClusterService upOrderK8sClusterService;

	@Autowired
	private IUpOrderCloudServerService cloudServerService;
	
	@Autowired
	private IUpProductService productService;

	@Autowired
	private IUpOrderService orderService;
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudServerChargeType type : CloudServerChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
 		Map<String, String> serverType = new LinkedHashMap<String, String>();
 		for (ServerType type : ServerType.values()) {
			 if(type.toString().indexOf("k8s") >= 0){
				 serverType.put(type.toString(), type.getTitle());
			 }
 		}
 		datas.put("serverStandardType", serverType);
        
 		Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        Map<String, String> diskStandardType = new LinkedHashMap<String, String>();
        for (DiskType type : DiskType.values()) {
        	diskStandardType.put(type.toString(), type.getTitle());
        }
        datas.put("diskStandardType", diskStandardType);

		List<UpProductCceSet> entitys = productService.queryCceSetByOrg(id);

		Map<ServerType, List<UpProductCceSet>> productCceMap = entitys.stream().collect(Collectors.groupingBy(UpProductCceSet::getServerType, Collectors.toList()));

		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到虚拟机配置，请联系管理员");
		LinkedHashMap<Object, Object> productSet = ListUtil.map(entitys, (vmSetMap, vmSet) -> {
			if (vmSetMap.containsKey(vmSet.getServerType())) {
				((List<UpProductVmSetBean>) vmSetMap.get(vmSet.getServerType())).add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
			} else {
				List<UpProductVmSetBean> list = new ArrayList<>();
				list.add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
				vmSetMap.put(vmSet.getControlNodeUnit(), list);
			}
		});
        // 配置
        datas.put("productCceMap", productCceMap);
        datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.vm_disk));
        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(id));

        // 镜像
   //     datas.put("images", orderService.getTemplatesByCatalog(ThreadCache.getRegion(), ThreadCache.getOrgId(), CatalogType.iaas, CatalogType.local));

        // 网络
        datas.put("vpc", orderService.getVPC(id));

        // 密钥对
        datas.put("keyPairs", cloudServerService.getKeyPairs(id));

        // 账户
        datas.put("account",  user.getName());

        // 弹性公网IP-计费方式
        Map<String, String> elasticIpChargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	elasticIpChargeType.put(type.toString(), type.getTitle());
        }
        datas.put("elasticIpChargeType", elasticIpChargeType);

        // 密钥方式
        Map<String, String> keyType = new LinkedHashMap<String, String>();
        for (KeyType type : KeyType.values()) {
        	keyType.put(type.toString(), type.getTitle());
        }
        datas.put("keyType", keyType);

		return ResponseBean.success(datas);
	}

	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderK8sClusterNodePoolBean bean, HttpServletRequest request) {
		return ResponseBean.success(upOrderK8sClusterService.saveNodePool(bean, ThreadCache.getUser()));
	}

}
