package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "审批流程设定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApprovalProcessBean extends RecordBean {

    @ApiModelProperty(value = "流程描述")
    private String description;

    @ApiModelProperty(value = "流程节点列表")
    private UpApprovalProcessNodeBean[] approvalProcessNodeList;

    @ApiModelProperty(value = "审批场景列表")
    private UpApprovalSceneBean[] approvalSceneList;

    @ApiModelProperty(value = "是否使用中")
    private Boolean isUsed;

    public UpApprovalProcessNodeBean[] getApprovalProcessNodeList() {
        return approvalProcessNodeList;
    }

    public void setApprovalProcessNodeList(UpApprovalProcessNodeBean[] approvalProcessNodeList) {
        this.approvalProcessNodeList = approvalProcessNodeList;
    }

    public UpApprovalSceneBean[] getApprovalSceneList() {
        return approvalSceneList;
    }

    public void setApprovalSceneList(UpApprovalSceneBean[] approvalSceneList) {
        this.approvalSceneList = approvalSceneList;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(Boolean isUsed) {
        this.isUsed = isUsed;
    }

}
