package io.aicloudware.portal.framework.controller;


import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.common.MessageUtil;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.CellStyleBean;
import io.aicloudware.portal.framework.utility.CsvUtil;
import io.aicloudware.portal.framework.utility.ExcelUtil;
import io.aicloudware.portal.framework.utility.PDFUtil;
import io.aicloudware.portal.framework.sdk.contants.UpExportFileType;
import io.aicloudware.portal.framework.bean.PageBean;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.SystemConfigUtil;
import io.aicloudware.portal.framework.exception.ValidateException;
import io.aicloudware.portal.framework.utility.FileUtil;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.executor.IExecutorAAA;
import io.aicloudware.portal.framework.utility.Utility;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;

import javax.servlet.http.HttpServletRequest;
import java.io.FileOutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Controller
public abstract class BaseEntityController<E extends IEntity<B>, B extends RecordBean, RL extends ResultListBean<B>> extends BaseController {

    protected final Type[] types = Utility.getClassTypes(getClass());

    protected Class<E> getEntityType() {
        return (Class<E>) types[0];
    }

    protected Class<B> getBeanType() {
        return (Class<B>) types[1];
    }

    protected Class<RL> getResultType() {
        return (Class<RL>) types[2];
    }

    protected B[] doQuery(SearchBean<B> search, E entity) {
        return commonService.query(search, entity, getBeanType());
    }

    protected B doLoad(Integer id) {
        return commonService.load(getEntityType(), getBeanType(), id);
    }

    protected ResponseBean queryEntity(SearchBean<B> search) {
        E entity = BeanCopyUtil.copy(search.getBean(), getEntityType());
        B[] entityList = doQuery(search, entity);
        ResultListBean<B> result = Utility.newInstance(getResultType());
        fillPageInfo(search, result);
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }

    protected ResponseBean getEntity(Integer id) {
        return ResponseBean.success(doLoad(id));
    }

    protected final void handleValidateResult(BindingResult bindingResult) {
        if (bindingResult != null && bindingResult.hasErrors()) {
            List<String> resultList = new ArrayList<>();
            List<String> resultSortList = new ArrayList<>();
            for (ObjectError objectError : bindingResult.getAllErrors()) {
                String defaultMessage = StringUtils.trimToEmpty(objectError.getDefaultMessage());
                if (objectError instanceof FieldError) {
                    FieldError fieldError = (FieldError) objectError;
                    String field = fieldError.getField();
                    String line = Utility.EMPTY;
                    if (field.contains(".")) {
                        line = (Integer.parseInt(field.substring(field.indexOf("[") + 1, field.indexOf("]"))) + 1) + "-";
                        field = field.substring(field.indexOf(".") + 1);
                    }
                    String rejectedValue = String.valueOf(fieldError.getRejectedValue());
                    if ("typeMismatch".equals(fieldError.getCode())) {
                        defaultMessage = MessageUtil.format("validation.typeMismatch", line + field, rejectedValue);
                    }
                    if (defaultMessage.contains("{0}")) {
                        defaultMessage = defaultMessage.replace("{0}", line + field);
                    }
                    if (defaultMessage.contains("{1}")) {
                        defaultMessage = defaultMessage.replace("{1}", rejectedValue);
                    }
                    if (defaultMessage.contains("${validatedValue}")) {
                        defaultMessage = defaultMessage.replace("${validatedValue}", rejectedValue);
                    }
                    if (line.length() == 0) {
                        resultList.add(defaultMessage);
                    } else {
                        if (line.length() == 2) {
                            defaultMessage = "0" + defaultMessage;
                        }
                        resultSortList.add(defaultMessage);
                    }
                } else {
                    resultList.add(objectError.getDefaultMessage());
                }
            }
            Collections.sort(resultSortList);
            for (String result : resultSortList) {
                if (result.startsWith("0")) {
                    result = result.substring(1);
                }
                resultList.add(result);
            }
            throw new ValidateException(resultList);
        }
    }

    protected final void fillPageInfo(PageBean orig, PageBean dest) {
        dest.setPageNum(orig.getPageNum());
        dest.setPageSize(orig.getPageSize());
        dest.setPageCount(orig.getPageCount());
        dest.setRecordCount(orig.getRecordCount());
    }

    protected String export(LinkedHashMap<String, String> headMap, List<Map<String, Object>> exportData, UpExportFileType type, String fileName, HttpServletRequest request, IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor) {

        String exportFilePath = SystemConfigUtil.getExportFilePath();

        FileOutputStream fos = null;

        try {
            if (!FileUtil.checkExistFile(exportFilePath)) {
                FileUtil.createDirs(exportFilePath);
            }

            final String head = fileName + "_" + FormatUtil.formatDateTime(new Date());
            fileName = fileName + "_" + FileUtil.generateRandomFilename();

            if (UpExportFileType.pdf.equals(type)) {
                fos = new FileOutputStream(exportFilePath + fileName + "." + type.toString());
                float[] widths = getWidths(headMap);
                String logo = (request.isSecure() ? "https://" : "http://") + request.getServerName() + ":"
                        + request.getServerPort() + request.getContextPath() + "/swagger/images/logo.png";
                PDFUtil.createPDFFile(logo, head, widths, exportData, headMap, fos, executor);
            } else if (UpExportFileType.csv.equals(type)) {
                fos = new FileOutputStream(exportFilePath + fileName + "." + type.toString());
                CsvUtil.createCSVFile(exportData, headMap, fos);
            } else if (UpExportFileType.xlsx.equals(type) || UpExportFileType.xls.equals(type)) {
                fos = new FileOutputStream(exportFilePath + fileName + "." + type.toString());
                ExcelUtil excelUtil = new ExcelUtil(UpExportFileType.xls.equals(type));
                excelUtil.createExcelFile(exportData, headMap, fos, fileName, executor);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(fos);
        }

        return exportFilePath + fileName + "." + type.toString();
    }

    private float[] getWidths(LinkedHashMap<String, String> headMap) {

        AssertUtil.check(Utility.isNotEmpty(headMap), "表头不能为空");

        float[] widths = new float[headMap.size()];
        widths[0] = 15f;

        BigDecimal totalWidth = new BigDecimal("300");
        BigDecimal colNum = new BigDecimal(widths.length - 1);
        Float occupiedWidth = 0f;

        for (int i = 1; i < widths.length; i++) {
            if (i == widths.length - 1) {
                widths[i] = totalWidth.floatValue() - occupiedWidth;
            } else {
                widths[i] = totalWidth.divide(colNum, 2).floatValue();
                occupiedWidth = occupiedWidth + widths[i];
            }
        }

        return widths;
    }
}
