package io.aicloudware.portal.api_rest.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "用户")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class RestUserBean extends BaseRestBean{

	@ApiModelProperty(value = "用户")
	private String userName;
	
    @ApiModelProperty(value = "用户ID")
    private String userId;

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

}
