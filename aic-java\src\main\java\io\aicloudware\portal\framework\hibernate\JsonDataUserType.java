package io.aicloudware.portal.framework.hibernate;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.UserType;
 
import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

public class JsonDataUserType implements UserType{

	@Override
	public Object assemble(Serializable cached, Object arg1) throws HibernateException {
		return this.deepCopy( cached);
	}

	@Override
	public Object deepCopy(Object arg0) throws HibernateException {
		return arg0;
	}

	@Override
	public Serializable disassemble(Object arg0) throws HibernateException {
        return (String)this.deepCopy( arg0);
	}

	@Override
	public boolean equals(Object x, Object y) throws HibernateException {
        if( x== null){
        	 
            return y== null;
        }
 
        return x.equals( y);
	}

	@Override
	public int hashCode(Object x) throws HibernateException {
        return x.hashCode();
	}

	@Override
	public boolean isMutable() {
		return true;
	}

	@Override
	public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner) throws HibernateException, SQLException {
        if(rs.getString(names[0]) == null){
            return null;
        }
        return rs.getString(names[0]);
	}

	@Override
	public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session) throws HibernateException, SQLException {
        if (value == null) {
            st.setNull(index, Types.OTHER);
            return;
        }
 
        st.setObject(index, value, Types.OTHER);
		
	}

	@Override
	public Object replace(Object arg0, Object arg1, Object arg2) throws HibernateException {
		return arg0;
	}

	@Override
	public Class returnedClass() {
        return String.class;
	}

	@Override
	public int[] sqlTypes() {
        return new int[] { Types.JAVA_OBJECT};
	}
	

}
