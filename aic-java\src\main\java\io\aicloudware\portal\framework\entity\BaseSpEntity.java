package io.aicloudware.portal.framework.entity;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

@MappedSuperclass
public abstract class BaseSpEntity<B extends RecordBean> extends BaseEntity<B> implements ISpEntity<B> {

    @JoinColumn(name = "sp_org_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
    
    @Column(name = "sp_uuid")
    private String spUuid;
    
    @EntityProperty(isNullCopy = false)
	@JoinColumn(name = "order_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    //    @EntityProperty(isNullCopy = false)
//    @Column(name = "region")
//    @Enumerated(EnumType.STRING)
//    private SpRegion region;

    @Column(name = "region")
    private String oldRegion;

    @EntityProperty(isNullCopy = false)
    @JoinColumn(name = "region_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpRegionEntity region;

    @EntityProperty(isNullCopy = false)
    @JoinColumn(name = "app_system_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpAppSystem appSystem;

    protected BaseSpEntity() {
    }

    protected BaseSpEntity(Integer id) {
        super(id);
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        if (spOrg != null && Utility.isNotZero(spOrg.getId())) {
            spOrg = BeanFactory.getCloudDao().load(SpOrg.class, spOrg.getId());
        }
        this.spOrg = spOrg;
    }

	public String getSpUuid() {
		return spUuid;
	}

	public void setSpUuid(String spUuid) {
		this.spUuid = spUuid;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

    public String getOldRegion() {
        return oldRegion;
    }

    public void setOldRegion(String oldRegion) {
        this.oldRegion = oldRegion;
    }

    public SpRegionEntity getRegion() {
        return region;
    }

    public void setRegion(SpRegionEntity region) {
        this.region = region;
    }

    public UpAppSystem getAppSystem() {
        return appSystem;
    }

    public void setAppSystem(UpAppSystem appSystem) {
        this.appSystem = appSystem;
    }
}
