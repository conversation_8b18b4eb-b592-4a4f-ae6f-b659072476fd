package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderLoadBalanceBean.class})
public class UpOrderLoadBalanceBean extends SpRecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "付费方式")
	private PaymentType paymentType;
	
	@ApiModelProperty(value = "新增弹性公网IP")
	private UpOrderElasticIpBean[] elasticIpList;
	
	@ApiModelProperty(value = "已有弹性公网IP")
	private Integer elasticIpId;
	
	@ApiModelProperty(value = "VIP")
	private String vip;
	
	@ApiModelProperty(value = "专有网络")
	private Integer vpcId;
	
	@ApiModelProperty(value = "所有人ID")
	private Integer ownerId;
	
	@ApiModelProperty(value = "监听")
	private UpOrderMonitorBean[] monitorList;

	@ApiModelProperty(value = "云服务器关联")
	private UpOrderBalanceServerRelationBean[] balanceServerRelationList;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "负载均衡产品配置ID")
	private Integer loadBalanceConfigId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public UpOrderMonitorBean[] getMonitorList() {
		return monitorList;
	}

	public void setMonitorList(UpOrderMonitorBean[] monitorList) {
		this.monitorList = monitorList;
	}

	public UpOrderBalanceServerRelationBean[] getBalanceServerRelationList() {
		return balanceServerRelationList;
	}

	public void setBalanceServerRelationList(UpOrderBalanceServerRelationBean[] balanceServerRelationList) {
		this.balanceServerRelationList = balanceServerRelationList;
	}

	public UpOrderElasticIpBean[] getElasticIpList() {
		return elasticIpList;
	}

	public void setElasticIpList(UpOrderElasticIpBean[] elasticIpList) {
		this.elasticIpList = elasticIpList;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getElasticIpId() {
		return elasticIpId;
	}

	public void setElasticIpId(Integer elasticIpId) {
		this.elasticIpId = elasticIpId;
	}

	public Integer getLoadBalanceConfigId() {
		return loadBalanceConfigId;
	}

	public void setLoadBalanceConfigId(Integer loadBalanceConfigId) {
		this.loadBalanceConfigId = loadBalanceConfigId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public String getVip() {
		return vip;
	}

	public void setVip(String vip) {
		this.vip = vip;
	}

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}
	
}
