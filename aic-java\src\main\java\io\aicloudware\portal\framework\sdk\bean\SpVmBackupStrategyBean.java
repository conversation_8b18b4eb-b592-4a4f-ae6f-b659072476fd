package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmBackupStrategyType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.validation.GroupSequence;

@ApiModel(value = "虚机备份策略")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVmBackupStrategyBean.class})
public class SpVmBackupStrategyBean extends SpRecordBean {

    @ApiModelProperty(value = "云主机UUID")
    private String cloudServerUuid;
    
    @ApiModelProperty(value = "云主机")
    private String cloudServerName;
    
    @ApiModelProperty(value = "备份类型")
    private SpVmBackupStrategyType type;
    
    @Column(name = "备份时间")
    private String backupTime;
    
    @Column(name = "备份重复日期")
    private String backupDay;
    
    @Column(name = "自定义备份日期")
    private String backupDate;
    
    @Column(name = "备份保留数量")
    private Integer backupAmount;

    public String getCloudServerName() {
        return cloudServerName;
    }

    public void setCloudServerName(String cloudServerName) {
        this.cloudServerName = cloudServerName;
    }

    public String getCloudServerUuid() {
        return cloudServerUuid;
    }

    public void setCloudServerUuid(String cloudServerUuid) {
        this.cloudServerUuid = cloudServerUuid;
    }

    public SpVmBackupStrategyType getType() {
        return type;
    }

    public void setType(SpVmBackupStrategyType type) {
        this.type = type;
    }

    public String getBackupTime() {
        return backupTime;
    }

    public void setBackupTime(String backupTime) {
        this.backupTime = backupTime;
    }

    public String getBackupDay() {
        return backupDay;
    }

    public void setBackupDay(String backupDay) {
        this.backupDay = backupDay;
    }

    public String getBackupDate() {
        return backupDate;
    }

    public void setBackupDate(String backupDate) {
        this.backupDate = backupDate;
    }

    public Integer getBackupAmount() {
        return backupAmount;
    }

    public void setBackupAmount(Integer backupAmount) {
        this.backupAmount = backupAmount;
    }

}
