package io.aicloudware.portal.framework.bean;

import io.aicloudware.portal.framework.utility.Utility;

public interface IDisplayName {

    public static final String DisplayName_Suffix = "[DisplayName_Suffix]";

    public static String appendDisplayNameSuffix(String displayName) {
        if (Utility.isEmpty(displayName)) {
            return displayName;
        }
        if (!displayName.endsWith(DisplayName_Suffix)) {
            displayName += DisplayName_Suffix;
        }
        return displayName;
    }

    public static String removeDisplayNameSuffix(String displayName) {
        if (Utility.isEmpty(displayName)) {
            return displayName;
        }
        return displayName.replace(DisplayName_Suffix, Utility.EMPTY);
    }

    public String getDisplayName();

    public void setDisplayName(String displayName);

}
