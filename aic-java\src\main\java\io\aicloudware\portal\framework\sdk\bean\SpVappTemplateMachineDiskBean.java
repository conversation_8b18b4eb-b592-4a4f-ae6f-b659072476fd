package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "蓝图计算机磁盘")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVappTemplateMachineDiskBean extends SpRecordBean {

    @ApiModelProperty(value = "蓝图计算机ID")
    private Integer blueprintMachineId;

    @ApiModelProperty(value = "蓝图计算机名称")
    private String blueprintMachineName;

    @ApiModelProperty(value = "蓝图计算机显示名称")
    private String blueprintMachineDisplayName;

    @ApiModelProperty(value = "磁盘ID")
    private Long diskId;

    @ApiModelProperty(value = "磁盘序号")
    private Integer diskNumber;

    @ApiModelProperty(value = "驱动器盘符/挂载路径")
    private String diskPath;

    @ApiModelProperty(value = "磁盘大小(GB)")
    private Integer diskGB;

    @ApiModelProperty(value = "磁盘标签")
    private String diskLabel;

    public Integer getBlueprintMachineId() {
        return blueprintMachineId;
    }

    public void setBlueprintMachineId(Integer blueprintMachineId) {
        this.blueprintMachineId = blueprintMachineId;
    }

    public String getBlueprintMachineName() {
        return blueprintMachineName;
    }

    public void setBlueprintMachineName(String blueprintMachineName) {
        this.blueprintMachineName = blueprintMachineName;
    }

    public String getBlueprintMachineDisplayName() {
        return blueprintMachineDisplayName;
    }

    public void setBlueprintMachineDisplayName(String blueprintMachineDisplayName) {
        this.blueprintMachineDisplayName = blueprintMachineDisplayName;
    }

    public Long getDiskId() {
        return diskId;
    }

    public void setDiskId(Long diskId) {
        this.diskId = diskId;
    }

    public Integer getDiskNumber() {
        return diskNumber;
    }

    public void setDiskNumber(Integer diskNumber) {
        this.diskNumber = diskNumber;
    }

    public String getDiskPath() {
        return diskPath;
    }

    public void setDiskPath(String diskPath) {
        this.diskPath = diskPath;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getDiskLabel() {
        return diskLabel;
    }

    public void setDiskLabel(String diskLabel) {
        this.diskLabel = diskLabel;
    }

}
