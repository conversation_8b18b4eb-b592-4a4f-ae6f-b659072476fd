package io.aicloudware.portal.api_vcpp.service.profile;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpWorkSheet;
import io.aicloudware.portal.api_vcpp.entity.UpWorkSheetDetail;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetDetailBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetResultBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetSearchBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;

@Service
@Transactional
public class UpWorkSheetService extends BaseService implements IUpWorkSheetService {

	@Override
	public UpWorkSheetBean detail(Integer workSheetId, Integer userId) {
		AssertUtil.check(workSheetId != null, "请选择工单！");
		UpWorkSheet entity = this.dao.load(UpWorkSheet.class, workSheetId);
		AssertUtil.check(entity != null && entity.getUser().getId().equals(userId), "工单数据异常！");
		UpWorkSheetBean bean = BeanCopyUtil.copy(entity, UpWorkSheetBean.class);
		bean.setDetailList(BeanCopyUtil.copy2BeanList(entity.getWorkSheetDetailList(), UpWorkSheetDetailBean.class));
		return bean;
	}

	@Override
	public UpWorkSheetDetailBean save(UpWorkSheetDetailBean bean, Integer userId) {
		AssertUtil.check(bean, "工单数据异常！");
		String content = this.cleanXSS(bean.getContent());
		AssertUtil.check(StringUtils.isNotEmpty(content), "请输入工单内容！");
		UpUser user = this.dao.load(UpUser.class, userId);
		UpWorkSheetDetail detail = BeanCopyUtil.copy(bean, UpWorkSheetDetail.class);
		detail.setSendTm(new Date());
		detail.setUser(user);
		detail.setName(content.length() > 8 ? (content.substring(0, 8) + "...") : content);

		UpWorkSheet sheet = null;
		if (bean.getWorkSheet() != null && bean.getWorkSheet().getId() != null) {
			sheet = this.dao.load(UpWorkSheet.class, bean.getWorkSheet().getId());
			AssertUtil.check(sheet != null && !sheet.getStatus().equals(RecordStatus.deleted) && !sheet.getWorkSheetStatus().equals(UpWorkSheetStatus.close), "工单状态异常！");
			AssertUtil.check(sheet.getUser().getId().equals(userId), "无法操作他人工单！");
		} else if (bean.getWorkSheet() != null && bean.getWorkSheet().getId() == null) {
			sheet = detail.getWorkSheet();
			AssertUtil.check(sheet.getName(), "请输入工单标题！");
			AssertUtil.check(sheet.getType(), "请选择工单类型！");
			
			sheet.setUser(user);
			sheet.setWorkSheetStatus(UpWorkSheetStatus.submit);
			this.dao.insert(sheet);
			sheet.setCode(new SimpleDateFormat("yyyyMMdd").format(new Date()) + String.format("%04d", sheet.getId()));
			this.dao.update(sheet, "code");
		}
		detail.setWorkSheet(sheet);
		this.dao.insert(detail);
		return BeanCopyUtil.copy(detail, UpWorkSheetDetailBean.class);
	}

	@Override
	public UpWorkSheetBean update(UpWorkSheetBean bean, Integer userId) {
		AssertUtil.check(bean != null && bean.getId() != null, "请选择工单！");
		UpWorkSheet sheet = this.dao.load(UpWorkSheet.class, bean.getId());
		AssertUtil.check(sheet.getUser().getId().equals(userId), "无法操作他人工单！");
		if (bean.getWorkSheetStatus() != null && !bean.getWorkSheetStatus().equals(sheet.getWorkSheetStatus())) {
			sheet.setWorkSheetStatus(bean.getWorkSheetStatus());
			this.dao.update(sheet, "workSheetStatus");
		}
		return bean;
	}

	@Override
	public UpWorkSheetResultBean query(UpWorkSheetSearchBean searchBean, Integer userId) {
		UpWorkSheetBean bean = searchBean.getBean();
		if (bean == null) {
			bean = new UpWorkSheetBean();
		}
		bean.setUserId(ThreadCache.getUserId());
		List<UpWorkSheet> entitys = this.dao.query(searchBean, BeanCopyUtil.copy(bean, UpWorkSheet.class));

		UpWorkSheetResultBean result = new UpWorkSheetResultBean();
		result.setDataList(BeanCopyUtil.copy2BeanList(entitys, UpWorkSheetBean.class));
		result.setPageCount(searchBean.getPageCount());
		result.setPageNum(searchBean.getPageNum());
		result.setPageSize(searchBean.getPageSize());
		result.setRecordCount(searchBean.getRecordCount());

		return result;
	}

	private String cleanXSS(String value) {
		value = value.replaceAll("<", "& lt;").replaceAll(">", "& gt;");
		value = value.replaceAll("\\(", "& #40;").replaceAll("\\)", "& #41;");
		value = value.replaceAll("'", "& #39;");
		value = value.replaceAll("eval\\((.*)\\)", "");
		value = value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
		value = value.replaceAll("script", "");
		return value;
	}

}
