package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "虚机")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVmBean.class})
public class SpVmBean extends SpRecordBean {

	@ApiModelProperty(value = "业务租户ID", position = 40)
    private SpDeployStatus deployStatus;
	
	@ApiModelProperty(value = "进度")
    private Integer progres;
	
    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String groupName;

//    @ApiModelProperty(value = "应用系统ID")
//    private Integer appSystemId;
//
//    @ApiModelProperty(value = "应用系统名称")
//    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
//    private String appSystemName;
//
//    @ApiModelProperty(value = "应用系统显示名称")
//    private String appSystemDisplayName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "根部署ID")
    private Integer rootDeploymentId;

    @ApiModelProperty(value = "根部署名称")
    private String rootDeploymentName;

    @ApiModelProperty(value = "根部署显示名称")
    private String rootDeploymentDisplayName;

    @ApiModelProperty(value = "根蓝图ID")
    private Integer rootBlueprintId;

    @ApiModelProperty(value = "根蓝图名称")
    private String rootBlueprintName;

    @ApiModelProperty(value = "根蓝图显示名称")
    private String rootBlueprintDisplayName;

    @ApiModelProperty(value = "申请部署ID")
    private Integer reqDeploymentId;

    @ApiModelProperty(value = "申请部署名称")
    private String reqDeploymentName;

    @ApiModelProperty(value = "资源部署ID")
    private Integer spDeploymentId;

    @ApiModelProperty(value = "资源部署名称")
    private String spDeploymentName;

    @ApiModelProperty(value = "部署机器关系ID")
    private Integer blueprintMachineRelationId;

    @ApiModelProperty(value = "部署机器关系名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String blueprintMachineRelationName;

    @ApiModelProperty(value = "部署机器关系显示名称")
    private String blueprintMachineRelationDisplayName;

    @ApiModelProperty(value = "部署机器ID")
    private Integer blueprintMachineId;

    @ApiModelProperty(value = "部署机器名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String blueprintMachineName;

    @ApiModelProperty(value = "部署机器显示名称")
    private String blueprintMachineDisplayName;

    @ApiModelProperty(value = "预留ID")
    private Integer reservationId;

    @ApiModelProperty(value = "预留名称")
    private String reservationName;

    @ApiModelProperty(value = "预留显示名称")
    private String reservationDisplayName;

    @ApiModelProperty(value = "虚机说明")
    private String vmComment;

    @ApiModelProperty(value = "CPU(核)")
    private Integer cpuNum;

    @ApiModelProperty(value = "内存(GB)")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘（GB）")
    private Integer diskGB;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "公网IP地址")
    private String[] publicIpAddress;
    
    @ApiModelProperty(value = "公网端口")
    private String[] publicPort;
    
    @ApiModelProperty(value = "云主机端口")
    private String[] port;
    
    @ApiModelProperty(value = "协议")
    private SpProtocolType[] protocol;
    
    @ApiModelProperty(value = "协议")
    private SpIpBindingType[] bindingType;
    
    @ApiModelProperty(value = "操作系统主机名")
    private String hostName;

    @ApiModelProperty(value = "操作系统用户名")
    private String osUser;

    @ApiModelProperty(value = "操作系统用户密码")
    private String osPassword;

    @ApiModelProperty(value = "VC目录")
    private String folderPath;

    @ApiModelProperty(value = "是否有快照")
    private Boolean hasSnapshot;

    @ApiModelProperty(value = "快照名称")
    private String snapshotName;

    @ApiModelProperty(value = "快照创建时间")
    private Date snapshotCreateTm;

    @ApiModelProperty(value = "创建时间")
    private Date createDt;

    @ApiModelProperty(value = "到期时间")
    private Date expireDt;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "电源状态")
    private SpVmPowerStatus powerStatus;

    @ApiModelProperty(value = "状态")
    private SpEcStatus ecStatus;

    @ApiModelProperty(value = "显示状态")
    private String ecStatusDisplay;

    @ApiModelProperty(value = "置备类型(THIN,THICK)")
    private SpVmProvisionType provisionType;

    @ApiModelProperty(value = "回收状态")
    private SpRecycleStatus recycleStatus;

    @ApiModelProperty(value = "回收状态显示")
    private String recycleStatusDisplay;

//    @ApiModelProperty(value = "分区")
//    private String region;
//
//    @ApiModelProperty(value = "域")
//    private String domain;

    @ApiModelProperty(value = "实例个数")
    private Integer instanceNum;

    @ApiModelProperty(value = "脚本ID")
    private Integer scriptId;

    @ApiModelProperty(value = "脚本名称")
    private String scriptName;

    @ApiModelProperty(value = "初始化参数JSON")
    private String jsonInitConfig;

    @ApiModelProperty(value = "应用安装参数JSON")
    private String jsonInstallConfig;

    @ApiModelProperty(value = "应用启动运行参数JSON")
    private String jsonAppConfig;

    @ApiModelProperty(value = "扩展1参数JSON")
    private String jsonEx1Config;

    @ApiModelProperty(value = "扩展2参数JSON")
    private String jsonEx2Config;

    @ApiModelProperty(value = "磁盘列表")
    private SpVmDiskBean[] diskList;

    @ApiModelProperty(value = "网络列表")
    private SpVmNetworkBean[] networkList;

    @ApiModelProperty(value = "自定义属性列表")
    private ReqPropertyItemBean[] customPropertyList;

    private SpVmOwnerBindingBean[] vmOwnerBindings;

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;

    @ApiModelProperty(value = "虚机创建时间", position = 170)
    private Date createTm;
    
    @ApiModelProperty(value = "UUID")
    private String spUuid;
    
    @ApiModelProperty(value = "备份策略名")
    private String backupStrategyName;
    
    @ApiModelProperty(value = "备份策略Id")
    private Integer backupStrategyId;

    @ApiModelProperty(value = "备份序号")
    private Integer backupSequence;

    @ApiModelProperty(value = "备份名")
    private String backupName;
    
    @ApiModelProperty(value = "备份云主机UUID")
    private String backupVmUuid;

    @ApiModelProperty(value = "备份云主机名")
    private String backupVmName;

    private String securityGroupName;

    private Integer securityGroupId;
    
    @ApiModelProperty(value = "变更配额")
    private UpOrderQuotaDetailBean updateOrderQuotaDetail;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

//    public Integer getAppSystemId() {
//        return appSystemId;
//    }
//
//    public void setAppSystemId(Integer appSystemId) {
//        this.appSystemId = appSystemId;
//    }
//
//    public String getAppSystemName() {
//        return appSystemName;
//    }
//
//    public void setAppSystemName(String appSystemName) {
//        this.appSystemName = appSystemName;
//    }
//
//    public String getAppSystemDisplayName() {
//        return appSystemDisplayName;
//    }
//
//    public void setAppSystemDisplayName(String appSystemDisplayName) {
//        this.appSystemDisplayName = appSystemDisplayName;
//    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getRootDeploymentId() {
        return rootDeploymentId;
    }

    public void setRootDeploymentId(Integer rootDeploymentId) {
        this.rootDeploymentId = rootDeploymentId;
    }

    public String getRootDeploymentName() {
        return rootDeploymentName;
    }

    public void setRootDeploymentName(String rootDeploymentName) {
        this.rootDeploymentName = rootDeploymentName;
    }

    public String getRootDeploymentDisplayName() {
        return rootDeploymentDisplayName;
    }

    public void setRootDeploymentDisplayName(String rootDeploymentDisplayName) {
        this.rootDeploymentDisplayName = rootDeploymentDisplayName;
    }

    public Integer getRootBlueprintId() {
        return rootBlueprintId;
    }

    public void setRootBlueprintId(Integer rootBlueprintId) {
        this.rootBlueprintId = rootBlueprintId;
    }

    public String getRootBlueprintName() {
        return rootBlueprintName;
    }

    public void setRootBlueprintName(String rootBlueprintName) {
        this.rootBlueprintName = rootBlueprintName;
    }

    public String getRootBlueprintDisplayName() {
        return rootBlueprintDisplayName;
    }

    public void setRootBlueprintDisplayName(String rootBlueprintDisplayName) {
        this.rootBlueprintDisplayName = rootBlueprintDisplayName;
    }

    public Integer getReqDeploymentId() {
        return reqDeploymentId;
    }

    public void setReqDeploymentId(Integer reqDeploymentId) {
        this.reqDeploymentId = reqDeploymentId;
    }

    public String getReqDeploymentName() {
        return reqDeploymentName;
    }

    public void setReqDeploymentName(String reqDeploymentName) {
        this.reqDeploymentName = reqDeploymentName;
    }

    public Integer getSpDeploymentId() {
        return spDeploymentId;
    }

    public void setSpDeploymentId(Integer spDeploymentId) {
        this.spDeploymentId = spDeploymentId;
    }

    public String getSpDeploymentName() {
        return spDeploymentName;
    }

    public void setSpDeploymentName(String spDeploymentName) {
        this.spDeploymentName = spDeploymentName;
    }

    public Integer getBlueprintMachineRelationId() {
        return blueprintMachineRelationId;
    }

    public void setBlueprintMachineRelationId(Integer blueprintMachineRelationId) {
        this.blueprintMachineRelationId = blueprintMachineRelationId;
    }

    public String getBlueprintMachineRelationName() {
        return blueprintMachineRelationName;
    }

    public void setBlueprintMachineRelationName(String blueprintMachineRelationName) {
        this.blueprintMachineRelationName = blueprintMachineRelationName;
    }

    public String getBlueprintMachineRelationDisplayName() {
        return blueprintMachineRelationDisplayName;
    }

    public void setBlueprintMachineRelationDisplayName(String blueprintMachineRelationDisplayName) {
        this.blueprintMachineRelationDisplayName = blueprintMachineRelationDisplayName;
    }

    public Integer getBlueprintMachineId() {
        return blueprintMachineId;
    }

    public void setBlueprintMachineId(Integer blueprintMachineId) {
        this.blueprintMachineId = blueprintMachineId;
    }

    public String getBlueprintMachineName() {
        return blueprintMachineName;
    }

    public void setBlueprintMachineName(String blueprintMachineName) {
        this.blueprintMachineName = blueprintMachineName;
    }

    public String getBlueprintMachineDisplayName() {
        return blueprintMachineDisplayName;
    }

    public void setBlueprintMachineDisplayName(String blueprintMachineDisplayName) {
        this.blueprintMachineDisplayName = blueprintMachineDisplayName;
    }

    public Integer getReservationId() {
        return reservationId;
    }

    public void setReservationId(Integer reservationId) {
        this.reservationId = reservationId;
    }

    public String getReservationName() {
        return reservationName;
    }

    public void setReservationName(String reservationName) {
        this.reservationName = reservationName;
    }

    public String getReservationDisplayName() {
        return reservationDisplayName;
    }

    public void setReservationDisplayName(String reservationDisplayName) {
        this.reservationDisplayName = reservationDisplayName;
    }

    public String getVmComment() {
        return vmComment;
    }

    public void setVmComment(String vmComment) {
        this.vmComment = vmComment;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getOsUser() {
        return osUser;
    }

    public void setOsUser(String osUser) {
        this.osUser = osUser;
    }

    public String getOsPassword() {
        return osPassword;
    }

    public void setOsPassword(String osPassword) {
        this.osPassword = osPassword;
    }

    public String getFolderPath() {
        return folderPath;
    }

    public void setFolderPath(String folderPath) {
        this.folderPath = folderPath;
    }

    public Boolean getHasSnapshot() {
        return hasSnapshot;
    }

    public void setHasSnapshot(Boolean hasSnapshot) {
        this.hasSnapshot = hasSnapshot;
    }

    public String getSnapshotName() {
        return snapshotName;
    }

    public void setSnapshotName(String snapshotName) {
        this.snapshotName = snapshotName;
    }

    public Date getSnapshotCreateTm() {
        return snapshotCreateTm;
    }

    public void setSnapshotCreateTm(Date snapshotCreateTm) {
        this.snapshotCreateTm = snapshotCreateTm;
    }

    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    public Date getExpireDt() {
        return expireDt;
    }

    public void setExpireDt(Date expireDt) {
        this.expireDt = expireDt;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public SpVmPowerStatus getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(SpVmPowerStatus powerStatus) {
        this.powerStatus = powerStatus;
    }

    public SpVmProvisionType getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(SpVmProvisionType provisionType) {
        this.provisionType = provisionType;
    }

//    public String getRegion() {
//        return region;
//    }
//
//    public void setRegion(String region) {
//        this.region = region;
//    }
//
//    public String getDomain() {
//        return domain;
//    }
//
//    public void setDomain(String domain) {
//        this.domain = domain;
//    }

    public Integer getInstanceNum() {
        return instanceNum;
    }

    public void setInstanceNum(Integer instanceNum) {
        this.instanceNum = instanceNum;
    }

    public Integer getScriptId() {
        return scriptId;
    }

    public void setScriptId(Integer scriptId) {
        this.scriptId = scriptId;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getJsonInitConfig() {
        return jsonInitConfig;
    }

    public void setJsonInitConfig(String jsonInitConfig) {
        this.jsonInitConfig = jsonInitConfig;
    }

    public String getJsonInstallConfig() {
        return jsonInstallConfig;
    }

    public void setJsonInstallConfig(String jsonInstallConfig) {
        this.jsonInstallConfig = jsonInstallConfig;
    }

    public String getJsonAppConfig() {
        return jsonAppConfig;
    }

    public void setJsonAppConfig(String jsonAppConfig) {
        this.jsonAppConfig = jsonAppConfig;
    }

    public String getJsonEx1Config() {
        return jsonEx1Config;
    }

    public void setJsonEx1Config(String jsonEx1Config) {
        this.jsonEx1Config = jsonEx1Config;
    }

    public String getJsonEx2Config() {
        return jsonEx2Config;
    }

    public void setJsonEx2Config(String jsonEx2Config) {
        this.jsonEx2Config = jsonEx2Config;
    }

    public SpVmDiskBean[] getDiskList() {
        return diskList;
    }

    public void setDiskList(SpVmDiskBean[] diskList) {
        this.diskList = diskList;
    }

    public SpVmNetworkBean[] getNetworkList() {
        return networkList;
    }

    public void setNetworkList(SpVmNetworkBean[] networkList) {
        this.networkList = networkList;
    }

    public ReqPropertyItemBean[] getCustomPropertyList() {
        return customPropertyList;
    }

    public void setCustomPropertyList(ReqPropertyItemBean[] customPropertyList) {
        this.customPropertyList = customPropertyList;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

    public String getSpUuid() {
        return spUuid;
    }

    public void setSpUuid(String spUuid) {
        this.spUuid = spUuid;
    }

    public String[] getPublicIpAddress() {
        return publicIpAddress;
    }

    public void setPublicIpAddress(String[] publicIpAddress) {
        this.publicIpAddress = publicIpAddress;
    }

	public SpDeployStatus getDeployStatus() {
		return deployStatus;
	}

	public void setDeployStatus(SpDeployStatus deployStatus) {
		this.deployStatus = deployStatus;
	}

	public Integer getProgres() {
		return progres;
	}

	public void setProgres(Integer progres) {
		this.progres = progres;
	}

    public String getBackupStrategyName() {
        return backupStrategyName;
    }

    public void setBackupStrategyName(String backupStrategyName) {
        this.backupStrategyName = backupStrategyName;
    }

    public Integer getBackupStrategyId() {
        return backupStrategyId;
    }

    public void setBackupStrategyId(Integer backupStrategyId) {
        this.backupStrategyId = backupStrategyId;
    }

    public Integer getBackupSequence() {
        return backupSequence;
    }

    public void setBackupSequence(Integer backupSequence) {
        this.backupSequence = backupSequence;
    }

    public String getBackupVmUuid() {
        return backupVmUuid;
    }

    public void setBackupVmUuid(String backupVmUuid) {
        this.backupVmUuid = backupVmUuid;
    }

    public String getBackupVmName() {
        return backupVmName;
    }

    public void setBackupVmName(String backupVmName) {
        this.backupVmName = backupVmName;
    }

    public String getBackupName() {
        return backupName;
    }

    public void setBackupName(String backupName) {
        this.backupName = backupName;
    }

	public String[] getPublicPort() {
		return publicPort;
	}

	public void setPublicPort(String[] publicPort) {
		this.publicPort = publicPort;
	}

	public String[] getPort() {
		return port;
	}

	public void setPort(String[] port) {
		this.port = port;
	}

	public SpProtocolType[] getProtocol() {
		return protocol;
	}

	public void setProtocol(SpProtocolType[] protocol) {
		this.protocol = protocol;
	}

	public UpOrderQuotaDetailBean getUpdateOrderQuotaDetail() {
		return updateOrderQuotaDetail;
	}

	public void setUpdateOrderQuotaDetail(UpOrderQuotaDetailBean updateOrderQuotaDetail) {
		this.updateOrderQuotaDetail = updateOrderQuotaDetail;
	}

	public SpIpBindingType[] getBindingType() {
		return bindingType;
	}

	public void setBindingType(SpIpBindingType[] bindingType) {
		this.bindingType = bindingType;
	}


    public SpEcStatus getEcStatus() {
        return ecStatus;
    }

    public void setEcStatus(SpEcStatus ecStatus) {
        this.ecStatus = ecStatus;
    }

    public String getEcStatusDisplay() {
        if (ecStatus != null) {
            this.ecStatusDisplay = ecStatus.getTitle();
        }
        return ecStatusDisplay;
    }

    public void setEcStatusDisplay(String ecStatusDisplay) {
        this.ecStatusDisplay = ecStatusDisplay;
    }

    public SpRecycleStatus getRecycleStatus() {
        return recycleStatus;
    }

    public void setRecycleStatus(SpRecycleStatus recycleStatus) {
        this.recycleStatus = recycleStatus;
    }

    public String getRecycleStatusDisplay() {
        if (recycleStatus != null) {
            return recycleStatus.getTitle();
        }
        return recycleStatusDisplay;
    }

    public void setRecycleStatusDisplay(String recycleStatusDisplay) {
        this.recycleStatusDisplay = recycleStatusDisplay;
    }

    public SpVmOwnerBindingBean[] getVmOwnerBindings() {
        return vmOwnerBindings;
    }

    public void setVmOwnerBindings(SpVmOwnerBindingBean[] vmOwnerBindings) {
        this.vmOwnerBindings = vmOwnerBindings;
    }

    public String getSecurityGroupName() {
        return securityGroupName;
    }

    public void setSecurityGroupName(String securityGroupName) {
        this.securityGroupName = securityGroupName;
    }

    public Integer getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(Integer securityGroupId) {
        this.securityGroupId = securityGroupId;
    }
}
