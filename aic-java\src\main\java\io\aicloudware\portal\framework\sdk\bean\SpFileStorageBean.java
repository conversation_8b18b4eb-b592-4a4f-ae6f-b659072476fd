package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "文件存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpFileStorageBean.class})
public class SpFileStorageBean extends SpRecordBean {
	
    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;
    
    @ApiModelProperty(value = "租户ID")
    private Integer spOrgId;
    
    @ApiModelProperty(value = "租户名称")
    private String spOrgName;

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;

    @ApiModelProperty(value = "UUID")
    private String spUuid;
    
	@ApiModelProperty(value = "存储大小（G）")
    private Long sizeG;
	
	@ApiModelProperty(value = "已用容量")
	private String usageSize;
    
    @ApiModelProperty(value = "共享路径")
    private String sharePath;
    
	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public String getOwnerName() {
		return ownerName;
	}

	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}

	public String getOwnerDisplayName() {
		return ownerDisplayName;
	}

	public void setOwnerDisplayName(String ownerDisplayName) {
		this.ownerDisplayName = ownerDisplayName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public RecordStatus getStatus() {
		return status;
	}

	public void setStatus(RecordStatus status) {
		this.status = status;
	}

	public String getSpUuid() {
		return spUuid;
	}

	public void setSpUuid(String spUuid) {
		this.spUuid = spUuid;
	}

	public Long getSizeG() {
		return sizeG;
	}

	public void setSizeG(Long sizeG) {
		this.sizeG = sizeG;
	}

	public String getSharePath() {
		return sharePath;
	}

	public void setSharePath(String sharePath) {
		this.sharePath = sharePath;
	}

	public String getUsageSize() {
		return usageSize;
	}

	public void setUsageSize(String usageSize) {
		this.usageSize = usageSize;
	}
    
}
