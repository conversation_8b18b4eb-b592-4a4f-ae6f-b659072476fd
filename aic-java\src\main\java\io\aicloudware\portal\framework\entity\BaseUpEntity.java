package io.aicloudware.portal.framework.entity;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

import javax.persistence.*;

@MappedSuperclass
public abstract class BaseUpEntity<B extends RecordBean> extends BaseEntity<B> implements IUpEntity<B> {

	//    @EntityProperty(isNullCopy = false)
//    @Column(name = "region")
//    @Enumerated(EnumType.STRING)
//    private SpRegion region;

	@Column(name = "region")
	private String oldRegion;

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "region_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpRegionEntity region;

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "app_system_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpAppSystem appSystem;

    protected BaseUpEntity() {
    }

    protected BaseUpEntity(Integer id) {
        super(id);
    }

	public String getOldRegion() {
		return oldRegion;
	}

	public void setOldRegion(String oldRegion) {
		this.oldRegion = oldRegion;
	}

	public SpRegionEntity getRegion() {
		return region;
	}

	public void setRegion(SpRegionEntity region) {
		this.region = region;
	}

	public UpAppSystem getAppSystem() {
		return appSystem;
	}

	public void setAppSystem(UpAppSystem appSystem) {
		this.appSystem = appSystem;
	}
}
