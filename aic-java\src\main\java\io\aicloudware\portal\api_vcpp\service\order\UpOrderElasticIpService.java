package io.aicloudware.portal.api_vcpp.service.order;

import java.util.List;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_up.service.IUpQuotaService;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderElasticIp;
import io.aicloudware.portal.api_vcpp.service.finance.IUpFinanceRechargeService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpIpBindingSearchBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ResourceType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;

@Service
@Transactional
public class UpOrderElasticIpService extends BaseService implements IUpOrderElasticIpService {

	@Autowired
	private IUpFinanceRechargeService financeRechargeService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;

	@Autowired
	private IUpQuotaService upQuotaService;

	@Autowired
	private ISpRegionService spRegionService;
	
	@Override
	public Integer save(UpOrderElasticIpBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getBandwidth(), "请选择带宽峰值！");
//		UpOrderQuotaDetail quotaDetail = null;
//		if(bean.getQuotaDetailId()!=null){
//			quotaDetail = dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());
//		}
//		if(quotaDetail == null || quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
//			AssertUtil.check(bean.getBandwidthConfigId(), "缺少带宽产品配置！");
//		}
//		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
//		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_elasticIp, user.getId()) == 0, "您有未完成的弹性公网IP申请！");
		if (bean.getIsResource() != null && bean.getIsResource()) {
			AssertUtil.check(bean.getResourceType(), "请选择资源类型！");
			SpIpBinding params = new SpIpBinding();
			// 绑云服务器
			if (bean.getResourceType().equals(ResourceType.cloudServer)) {
				AssertUtil.check(bean.getCloudServerId(), "请选择云服务器！");
				SpVm vm = this.dao.load(SpVm.class,bean.getCloudServerId());
				AssertUtil.check(vm, "云服务器信息异常！");
				AssertUtil.check(vm.getSpOrg().getId().equals(user.getOrg().getId()),"云服务器所属用户组异常！");
				params.setVm(new SpVm(bean.getCloudServerId()));
				List<SpIpBinding> list = this.queryDao.query(new SpIpBindingSearchBean(), params);
				AssertUtil.check(list != null && list.size() == 0, "所选云服务器已绑定弹性公网IP！");
				bean.setLoadBalanceId(null);
			}

			// 绑负载均衡
			if (bean.getResourceType().equals(ResourceType.loadBalance)) {
				AssertUtil.check(bean.getLoadBalanceId(), "请选择负载均衡！");
//				UpOrderLoadBalance loadBalance = this.dao.load(UpOrderLoadBalance.class, bean.getLoadBalanceId());
				
				// 校验云服务器所属用户是同一人
//				AssertUtil.check(loadBalance, "负载均衡信息异常！");
//				AssertUtil.check(loadBalance.getOwner().getId().equals(user.getId()), "负载均衡所属人异常！");
				
//				params.setLoadBalanceId(bean.getLoadBalanceId());
//				params.setBindStatus(BindStatus.assigned);
//				List<UpOrderElasticIp> list = this.dao.query(new UpOrderElasticIpSearchBean(), params);
//				AssertUtil.check(list != null && list.size() == 0, "所选负载均衡已绑定弹性公网IP！");
				bean.setCloudServerId(null);
			}
		}else {
			bean.setLoadBalanceId(null);
			bean.setCloudServerId(null);
			bean.setResourceType(null);
		}
		
		String name = "ELASTICIP-"+System.currentTimeMillis() + String.format("%04d",(int)(Math.random()*1000));

		List<SpElasticIp> spElasticIpList = dao.list(SpElasticIp.class, MapUtil.of("ipStatus", SpIpStatus.assigned, "spOrg", user.getOrg(), "status", RecordStatus.active, "owner.id", ThreadCache.getUserId()));
		Integer eipCount = 0;
		if (spElasticIpList != null) {
			eipCount = spElasticIpList.size();
		}
		upQuotaService.checkQuota(SpService.vpc, SpResourceType.publicIp, bean.getAppSystemId(), user.getOrg().getId(), 1, eipCount);

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_elasticIp);
		order.setName("[" + OrderType.new_elasticIp + "]" + name);
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setAppSystem(new UpAppSystem(bean.getAppSystemId()));
		order.setApplyUser(applyUser);
		order.setPaymentType(bean.getPaymentType());
		order.setBandwidthNum(bean.getBandwidth());
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		this.dao.insert(order);
		
		UpOrderElasticIp entity = BeanCopyUtil.copy(bean, UpOrderElasticIp.class);
		entity.setOrder(order);
		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setName(name);
		entity.setAppSystem(new UpAppSystem(bean.getAppSystemId()));
		entity.setElasticIP(bean.getElasticIP());
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        task.setRegion(order.getRegion());
        dao.insert(task);
       // quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}

	@Override
	public List<String> availableIps(SpOrg entity) {
		// TODO GY3获取GY的公网IP
		SpRegionEntity region = ThreadCache.getRegion();
		List<String> ipList =queryDao.querySql("select ip_address from sp_elastic_ip where status='active' and ip_status='unassigned' and region_id = " + (region.equals(spRegionService.CIDCRP35()) ? spRegionService.CIDCRP35() : region).getId() + " order by ip_address", null);
		return ipList;
	}
	
}
