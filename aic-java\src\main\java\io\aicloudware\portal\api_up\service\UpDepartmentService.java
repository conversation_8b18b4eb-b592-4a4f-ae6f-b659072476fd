package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpDepartment;
import io.aicloudware.portal.api_up.entity.UpDepartmentUserRelation;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentBean;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentUserRelationBean;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class UpDepartmentService extends BaseService implements IUpDepartmentService {

    @Override
    public void add(UpDepartmentBean bean) {
        AssertUtil.check(bean, "部门信息不能为空");
        AssertUtil.check(StringUtils.isNotEmpty(bean.getDisplayName()), "部门名称不能为空");

        // 检查部门编码唯一性
        if (StringUtils.isNotEmpty(bean.getDepartmentCode())) {
            List<UpDepartment> existingDepts = dao.list(UpDepartment.class, "departmentCode", bean.getDepartmentCode());
            AssertUtil.check(existingDepts.isEmpty(), "部门编码已存在");
        }

        // 检查父部门是否存在
        UpDepartment parentDepartment = null;
        if (bean.getParentDepartmentId() != null) {
            parentDepartment = dao.load(UpDepartment.class, bean.getParentDepartmentId());
            AssertUtil.check(parentDepartment != null, "父部门不存在");
        }

        UpDepartment department = new UpDepartment();
        BeanCopyUtil.copy(bean, department);
        department.setParentDepartment(parentDepartment);

        // 设置默认值
        if (department.getIsActive() == null) {
            department.setIsActive(true);
        }
        if (department.getSortOrder() == null) {
            department.setSortOrder(0);
        }

        dao.insert(department);
    }

    @Override
    public void update(UpDepartmentBean bean) {
        AssertUtil.check(bean, "部门信息不能为空");
        AssertUtil.check(bean.getId() != null, "部门ID不能为空");
        AssertUtil.check(StringUtils.isNotEmpty(bean.getDisplayName()), "部门名称不能为空");

        UpDepartment department = dao.load(UpDepartment.class, bean.getId());
        AssertUtil.check(department != null, "部门不存在");

        // 检查部门编码唯一性（排除自己）
        if (StringUtils.isNotEmpty(bean.getDepartmentCode())) {
            List<UpDepartment> existingDepts = dao.list(UpDepartment.class, "departmentCode", bean.getDepartmentCode());
            for (UpDepartment existingDept : existingDepts) {
                AssertUtil.check(existingDept.getId().equals(bean.getId()), "部门编码已存在");
            }
        }

        // 检查父部门是否存在，并防止循环引用
        UpDepartment parentDepartment = null;
        if (bean.getParentDepartmentId() != null) {
            AssertUtil.check(!bean.getParentDepartmentId().equals(bean.getId()), "不能将自己设置为父部门");
            parentDepartment = dao.load(UpDepartment.class, bean.getParentDepartmentId());
            AssertUtil.check(parentDepartment != null, "父部门不存在");

            // 检查是否会形成循环引用
            checkCircularReference(bean.getId(), bean.getParentDepartmentId());
        }

        BeanCopyUtil.copy(bean, department);
        department.setParentDepartment(parentDepartment);

        dao.update(department);
    }

    @Override
    public void delete(Integer id) {
        AssertUtil.check(id != null, "部门ID不能为空");

        UpDepartment department = dao.load(UpDepartment.class, id);
        AssertUtil.check(department != null, "部门不存在");

        // 检查是否有子部门
        List<UpDepartment> childDepartments = queryDao.queryHql("FROM UpDepartment d WHERE d.parentDepartment.id = ?", MapUtil.of("0", id));
        AssertUtil.check(childDepartments.isEmpty(), "存在子部门，无法删除");

        // 检查是否有用户关联
        List<UpDepartmentUserRelation> userRelations = queryDao.queryHql("FROM UpDepartmentUserRelation r WHERE r.department.id = ?", MapUtil.of("0", id));
        AssertUtil.check(userRelations.isEmpty(), "部门下存在用户，无法删除");

        dao.delete(UpDepartment.class, id);
    }

    @Override
    public UpDepartmentBean[] list(UpDepartmentBean params) {
        List<UpDepartment> departments;

        if (params != null) {
            // 根据参数查询
            StringBuilder hql = new StringBuilder("FROM UpDepartment d WHERE 1=1");
            Map<String, Object> paramMap = new HashMap<>();
            int paramIndex = 0;

            if (StringUtils.isNotEmpty(params.getDisplayName())) {
                hql.append(" AND d.displayName LIKE :param").append(paramIndex);
                paramMap.put("param" + paramIndex, "%" + params.getDisplayName() + "%");
                paramIndex++;
            }

            if (StringUtils.isNotEmpty(params.getDepartmentCode())) {
                hql.append(" AND d.departmentCode LIKE :param").append(paramIndex);
                paramMap.put("param" + paramIndex, "%" + params.getDepartmentCode() + "%");
                paramIndex++;
            }

            if (params.getParentDepartmentId() != null) {
                hql.append(" AND d.parentDepartment.id = :param").append(paramIndex);
                paramMap.put("param" + paramIndex, params.getParentDepartmentId());
                paramIndex++;
            }

            if (params.getIsActive() != null) {
                hql.append(" AND d.isActive = :param").append(paramIndex);
                paramMap.put("param" + paramIndex, params.getIsActive());
                paramIndex++;
            }

            hql.append(" ORDER BY d.sortOrder, d.id");

            departments = queryDao.queryHql(hql.toString(), paramMap);
        } else {
            departments = queryDao.queryHql("FROM UpDepartment d ORDER BY d.sortOrder, d.id", null);
        }

        return convertToBeansWithCounts(departments);
    }

    @Override
    public UpDepartmentBean[] getDepartmentTree(Integer parentId) {
        List<UpDepartment> departments;

        if (parentId != null) {
            departments = queryDao.queryHql(
                "FROM UpDepartment d WHERE d.parentDepartment.id = :parentId ORDER BY d.sortOrder, d.id",
                MapUtil.of("parentId", parentId)
            );
        } else {
            departments = queryDao.queryHql(
                "FROM UpDepartment d WHERE d.parentDepartment IS NULL ORDER BY d.sortOrder, d.id",
                null
            );
        }

        List<UpDepartmentBean> result = new ArrayList<>();
        for (UpDepartment dept : departments) {
            UpDepartmentBean bean = BeanCopyUtil.copy2Bean(dept, UpDepartmentBean.class);

            // 设置父部门信息
            if (dept.getParentDepartment() != null) {
                bean.setParentDepartmentId(dept.getParentDepartment().getId());
                bean.setParentDepartmentName(dept.getParentDepartment().getName());
                bean.setParentDepartmentDisplayName(dept.getParentDepartment().getDisplayName());
            }

            // 递归获取子部门
            UpDepartmentBean[] childDepartments = getDepartmentTree(dept.getId());
            bean.setChildDepartments(childDepartments);
            bean.setChildDepartmentCount(childDepartments.length);

            // 获取用户数量
            List<UpDepartmentUserRelation> userRelations = queryDao.queryHql(
                "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId",
                MapUtil.of("departmentId", dept.getId())
            );
            bean.setUserCount(userRelations.size());

            result.add(bean);
        }

        return result.toArray(new UpDepartmentBean[0]);
    }

    @Override
    public void addUsersToDepart(Integer departmentId, List<Integer> userIds) {
        AssertUtil.check(departmentId != null, "部门ID不能为空");
        AssertUtil.check(userIds != null && !userIds.isEmpty(), "用户ID列表不能为空");

        UpDepartment department = dao.load(UpDepartment.class, departmentId);
        AssertUtil.check(department != null, "部门不存在");

        // 验证关系类型
//        UpDepartmentUserRelationType relationTypeEnum;
//        try {
//            relationTypeEnum = UpDepartmentUserRelationType.valueOf(relationType);
//        } catch (IllegalArgumentException e) {
//            throw new RuntimeException("无效的关系类型: " + relationType);
//        }

        for (Integer userId : userIds) {
            UpUser user = dao.load(UpUser.class, userId);
            AssertUtil.check(user != null, "用户不存在: " + userId);

            // 检查是否已存在关系
            List<UpDepartmentUserRelation> existingRelations = queryDao.queryHql(
                "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId AND r.user.id = :userId",
                MapUtil.of("departmentId", departmentId, "userId", userId)
            );
            UpDepartmentUserRelation existingRelation = existingRelations.isEmpty() ? null : existingRelations.get(0);

            if (existingRelation == null) {
                // 创建新关系
                UpDepartmentUserRelation relation = new UpDepartmentUserRelation();
                relation.setDepartment(department);
                relation.setUser(user);
//                relation.setRelationType(relationTypeEnum);
//                relation.setIsManager(UpDepartmentUserRelationType.MANAGER.equals(relationTypeEnum) ||
//                                     UpDepartmentUserRelationType.DEPUTY_MANAGER.equals(relationTypeEnum));
                relation.setSortOrder(0);

                dao.insert(relation);
            } else {
                // 更新现有关系
//                existingRelation.setRelationType(relationTypeEnum);
//                existingRelation.setIsManager(UpDepartmentUserRelationType.MANAGER.equals(relationTypeEnum) ||
//                                             UpDepartmentUserRelationType.DEPUTY_MANAGER.equals(relationTypeEnum));
                dao.update(existingRelation);
            }
        }
    }

    @Override
    public void removeUsersFromDepartment(Integer departmentId, List<Integer> userIds) {
        AssertUtil.check(departmentId != null, "部门ID不能为空");
        AssertUtil.check(userIds != null && !userIds.isEmpty(), "用户ID列表不能为空");

        UpDepartment department = dao.load(UpDepartment.class, departmentId);
        AssertUtil.check(department != null, "部门不存在");

        for (Integer userId : userIds) {
            List<UpDepartmentUserRelation> relations = queryDao.queryHql(
                "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId AND r.user.id = :userId",
                MapUtil.of("departmentId", departmentId, "userId", userId)
            );
            UpDepartmentUserRelation relation = relations.isEmpty() ? null : relations.get(0);

            if (relation != null) {
                dao.delete(UpDepartmentUserRelation.class, relation.getId());
            }
        }
    }

    @Override
    public UpDepartmentUserRelationBean[] getDepartmentUsers(Integer departmentId) {
        AssertUtil.check(departmentId != null, "部门ID不能为空");

        UpDepartment department = dao.load(UpDepartment.class, departmentId);
        AssertUtil.check(department != null, "部门不存在");

        List<UpDepartmentUserRelation> relations = queryDao.queryHql(
            "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId ORDER BY r.isManager DESC, r.sortOrder, r.id",
            MapUtil.of("departmentId", departmentId)
        );

        List<UpDepartmentUserRelationBean> result = new ArrayList<>();
        for (UpDepartmentUserRelation relation : relations) {
            UpDepartmentUserRelationBean bean = BeanCopyUtil.copy2Bean(relation, UpDepartmentUserRelationBean.class);

            // 设置部门信息
            bean.setDepartmentId(relation.getDepartment().getId());
            bean.setDepartmentName(relation.getDepartment().getName());
            bean.setDepartmentDisplayName(relation.getDepartment().getDisplayName());

            // 设置用户信息
            bean.setUserId(relation.getUser().getId());
            bean.setUserName(relation.getUser().getName());
            bean.setUserDisplayName(relation.getUser().getDisplayName());

            // 设置关系类型文本
//            bean.setRelationTypeText(relation.getRelationType().getTitle());

            result.add(bean);
        }

        return result.toArray(new UpDepartmentUserRelationBean[0]);
    }

    @Override
    public UpDepartmentBean[] getUserDepartments(Integer userId) {
        AssertUtil.check(userId != null, "用户ID不能为空");

        UpUser user = dao.load(UpUser.class, userId);
        AssertUtil.check(user != null, "用户不存在");

        List<UpDepartmentUserRelation> relations = queryDao.queryHql(
            "FROM UpDepartmentUserRelation r WHERE r.user.id = :userId ORDER BY r.isManager DESC, r.sortOrder, r.id",
            MapUtil.of("userId", userId)
        );

        List<UpDepartmentBean> result = new ArrayList<>();
        for (UpDepartmentUserRelation relation : relations) {
            UpDepartmentBean bean = BeanCopyUtil.copy2Bean(relation.getDepartment(), UpDepartmentBean.class);

            // 设置父部门信息
            if (relation.getDepartment().getParentDepartment() != null) {
                bean.setParentDepartmentId(relation.getDepartment().getParentDepartment().getId());
                bean.setParentDepartmentName(relation.getDepartment().getParentDepartment().getName());
                bean.setParentDepartmentDisplayName(relation.getDepartment().getParentDepartment().getDisplayName());
            }

            result.add(bean);
        }

        return result.toArray(new UpDepartmentBean[0]);
    }

    @Override
    public void setDepartmentManager(Integer departmentId, Integer userId, Boolean isManager) {
        AssertUtil.check(departmentId != null, "部门ID不能为空");
        AssertUtil.check(userId != null, "用户ID不能为空");
        AssertUtil.check(isManager != null, "管理员标识不能为空");

        UpDepartment department = dao.load(UpDepartment.class, departmentId);
        AssertUtil.check(department != null, "部门不存在");

        UpUser user = dao.load(UpUser.class, userId);
        AssertUtil.check(user != null, "用户不存在");

        List<UpDepartmentUserRelation> relations = queryDao.queryHql(
            "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId AND r.user.id = :userId",
            MapUtil.of("departmentId", departmentId, "userId", userId)
        );

        AssertUtil.check(!relations.isEmpty(), "用户不在该部门中");

        UpDepartmentUserRelation relation = relations.get(0);
        relation.setIsManager(isManager);

        // 根据管理员状态设置关系类型
//        if (Boolean.TRUE.equals(isManager)) {
//            relation.setRelationType(UpDepartmentUserRelationType.MANAGER);
//        } else {
//            relation.setRelationType(UpDepartmentUserRelationType.MEMBER);
//        }

        dao.update(relation);
    }

    /**
     * 检查循环引用
     */
    private void checkCircularReference(Integer departmentId, Integer parentDepartmentId) {
        if (parentDepartmentId == null) {
            return;
        }

        UpDepartment parent = dao.load(UpDepartment.class, parentDepartmentId);
        while (parent != null) {
            AssertUtil.check(!parent.getId().equals(departmentId), "不能形成循环引用");
            parent = parent.getParentDepartment();
        }
    }

    /**
     * 转换为Bean并计算统计信息
     */
    private UpDepartmentBean[] convertToBeansWithCounts(List<UpDepartment> departments) {
        List<UpDepartmentBean> result = new ArrayList<>();

        for (UpDepartment dept : departments) {
            UpDepartmentBean bean = BeanCopyUtil.copy2Bean(dept, UpDepartmentBean.class);

            // 设置父部门信息
            if (dept.getParentDepartment() != null) {
                bean.setParentDepartmentId(dept.getParentDepartment().getId());
                bean.setParentDepartmentName(dept.getParentDepartment().getName());
                bean.setParentDepartmentDisplayName(dept.getParentDepartment().getDisplayName());
            }

            // 计算子部门数量
            List<UpDepartment> childDepartments = queryDao.queryHql(
                "FROM UpDepartment d WHERE d.parentDepartment.id = :parentId",
                MapUtil.of("parentId", dept.getId())
            );
            bean.setChildDepartmentCount(childDepartments.size());

            // 计算用户数量
            List<UpDepartmentUserRelation> userRelations = queryDao.queryHql(
                "FROM UpDepartmentUserRelation r WHERE r.department.id = :departmentId",
                MapUtil.of("departmentId", dept.getId())
            );
            bean.setUserCount(userRelations.size());

            result.add(bean);
        }

        return result.toArray(new UpDepartmentBean[0]);
    }
}