package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "监控指标")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpMetricsBean extends SpRecordBean {

    @ApiModelProperty(value = "指标名")
    private String metricName;

    @ApiModelProperty(value = "数据集")
    private SpMetricsDataBean[] data;
    
    @ApiModelProperty(value = "对象ID")
    private Integer objectId;

	public String getMetricName() {
		return metricName;
	}

	public void setMetricName(String metricName) {
		this.metricName = metricName;
	}

	public SpMetricsDataBean[] getData() {
		return data;
	}

	public void setData(SpMetricsDataBean[] data) {
		this.data = data;
	}

	public Integer getObjectId() {
		return objectId;
	}

	public void setObjectId(Integer objectId) {
		this.objectId = objectId;
	}
    
    
}
