package io.aicloudware.portal.framework.sdk.bean;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpVmRecordType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机记录")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpVmRecordBean extends RecordBean {

    @ApiModelProperty(value = "记录日期")
    private Integer recordDt;

    @ApiModelProperty(value = "记录类型")
    private UpVmRecordType recordType;

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "CPU")
    private Integer cpu;

    @ApiModelProperty(value = "内存GB")
    private Integer memoryGB;

    @ApiModelProperty(value = "磁盘GB")
    private Integer diskGB;

    @ApiModelProperty(value = "网络个数")
    private Integer network;

    public Integer getRecordDt() {
        return recordDt;
    }

    public void setRecordDt(Integer recordDt) {
        this.recordDt = recordDt;
    }

    public UpVmRecordType getRecordType() {
        return recordType;
    }

    public void setRecordType(UpVmRecordType recordType) {
        this.recordType = recordType;
    }

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public Integer getNetwork() {
        return network;
    }

    public void setNetwork(Integer network) {
        this.network = network;
    }
}
