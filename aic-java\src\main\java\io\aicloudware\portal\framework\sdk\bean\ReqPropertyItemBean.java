package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "部署参数（蓝图机器自定义属性项）")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, ReqPropertyItemBean.class})
public class ReqPropertyItemBean extends RecordBean {

    @ApiModelProperty(value = "请求属性组ID")
    private Integer reqPropertyGroupId;

    @ApiModelProperty(value = "请求属性组名称")
    private String reqPropertyGroupName;

    @ApiModelProperty(value = "属性项ID")
    private Integer spPropertyItemId;

    @ApiModelProperty(value = "属性项名称")
    private String spPropertyItemName;

    @ApiModelProperty(value = "属性项显示名称")
    private String spPropertyItemDisplayName;

    @ApiModelProperty(value = "属性值")
    private String value;

    public Integer getReqPropertyGroupId() {
        return reqPropertyGroupId;
    }

    public void setReqPropertyGroupId(Integer reqPropertyGroupId) {
        this.reqPropertyGroupId = reqPropertyGroupId;
    }

    public String getReqPropertyGroupName() {
        return reqPropertyGroupName;
    }

    public void setReqPropertyGroupName(String reqPropertyGroupName) {
        this.reqPropertyGroupName = reqPropertyGroupName;
    }

    public Integer getSpPropertyItemId() {
        return spPropertyItemId;
    }

    public void setSpPropertyItemId(Integer spPropertyItemId) {
        this.spPropertyItemId = spPropertyItemId;
    }

    public String getSpPropertyItemName() {
        return spPropertyItemName;
    }

    public void setSpPropertyItemName(String spPropertyItemName) {
        this.spPropertyItemName = spPropertyItemName;
    }

    public String getSpPropertyItemDisplayName() {
        return spPropertyItemDisplayName;
    }

    public void setSpPropertyItemDisplayName(String spPropertyItemDisplayName) {
        this.spPropertyItemDisplayName = spPropertyItemDisplayName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
