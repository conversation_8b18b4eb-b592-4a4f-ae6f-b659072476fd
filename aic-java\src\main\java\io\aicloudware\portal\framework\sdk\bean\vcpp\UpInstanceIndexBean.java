package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.SpServiceCatalog;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "实例索引")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpInstanceIndexBean.class})
public class UpInstanceIndexBean extends SpRecordBean {

	private Date instanceCreateTm;

	private Date instanceUpdateTm;

	private SpService serviceType;

	private String serviceTypeText;

	private SpServiceCatalog serviceCatalog;

	private String serviceCatalogText;

	public SpService getServiceType() {
		return serviceType;
	}

	public void setServiceType(SpService serviceType) {
		this.serviceType = serviceType;
	}

	public SpServiceCatalog getServiceCatalog() {
		return serviceCatalog;
	}

	public void setServiceCatalog(SpServiceCatalog serviceCatalog) {
		this.serviceCatalog = serviceCatalog;
	}

	public Date getInstanceCreateTm() {
		return instanceCreateTm;
	}

	public void setInstanceCreateTm(Date instanceCreateTm) {
		this.instanceCreateTm = instanceCreateTm;
	}

	public Date getInstanceUpdateTm() {
		return instanceUpdateTm;
	}

	public void setInstanceUpdateTm(Date instanceUpdateTm) {
		this.instanceUpdateTm = instanceUpdateTm;
	}

	public String getServiceTypeText() {
		return serviceTypeText;
	}

	public void setServiceTypeText(String serviceTypeText) {
		this.serviceTypeText = serviceTypeText;
	}

	public String getServiceCatalogText() {
		return serviceCatalogText;
	}

	public void setServiceCatalogText(String serviceCatalogText) {
		this.serviceCatalogText = serviceCatalogText;
	}
}
