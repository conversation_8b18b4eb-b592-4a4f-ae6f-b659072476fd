package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderSecurityBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.platform_vcd.entity.SpIpBinding;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpSecurity;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Entity
@Table(name = "up_order_security")
@Access(AccessType.FIELD)
public class UpOrderSecurity extends UpOrderProduct<UpOrderSecurityBean> implements IOrderEntity {

    @Column(name = "security_type")
    @Enumerated(EnumType.STRING)
    private ProductVmSetType securityType;
    
    @Column(name = "code")
    private String productCode;

    @JoinColumn(name = "server_config_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductVmSet config;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
    
	@JoinColumn(name = "security_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpSecurity security;
	
    @Column(name = "image_id")
    private Integer imageId;
    
    @JoinColumn(name = "vm_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;
    
    @JoinColumn(name = "ip_binding_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpIpBinding ipBinding;

	public ProductVmSetType getSecurityType() {
		return securityType;
	}

	public void setSecurityType(ProductVmSetType securityType) {
		this.securityType = securityType;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public UpProductVmSet getConfig() {
		return config;
	}

	public void setConfig(UpProductVmSet config) {
		this.config = config;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public SpSecurity getSecurity() {
		return security;
	}

	public void setSecurity(SpSecurity security) {
		this.security = security;
	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}

	public SpVm getVm() {
		return vm;
	}

	public void setVm(SpVm vm) {
		this.vm = vm;
	}

	public SpIpBinding getIpBinding() {
		return ipBinding;
	}

	public void setIpBinding(SpIpBinding ipBinding) {
		this.ipBinding = ipBinding;
	}
}
