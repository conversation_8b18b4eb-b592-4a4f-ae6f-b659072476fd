package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import javax.validation.GroupSequence;

@ApiModel(value = "快照")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, SpSnapshotBean.class })
public class SpSnapshotBean extends SpRecordBean {

    @ApiModelProperty(value = "虚机ID")
    private String vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "快照ID")
    private String spUuid;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTm;
    
    public String getVmId() {
        return vmId;
    }

    public void setVmId(String vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getSpUuid() {
        return spUuid;
    }

    public void setSpUuid(String spUuid) {
        this.spUuid = spUuid;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

}
