package io.aicloudware.portal.api_rest.service;

import io.aicloudware.portal.api_rest.framework.bean.RestResponseBean;
import io.aicloudware.portal.api_rest.framework.bean.RestResultDataBean;
import io.aicloudware.portal.api_rest.framework.entity.RestCustom;
import io.aicloudware.portal.api_rest.framework.enums.RestCustomQuotaProductCode;
import io.aicloudware.portal.api_rest.framework.exception.RestValidateException;
import io.aicloudware.portal.api_rest.framework.util.RestAssertUtil;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.SpMetricsBean;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBandwidthSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductDiskSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpOrgRuntimeStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.service.ICloudService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpElasticIpService;
import io.aicloudware.portal.platform_vcd.service.ISpFileStorageService;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import io.aicloudware.portal.platform_vcd.service.ISpVappService;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class RestApiService extends BaseService implements IRestApiService {

    @Value("${gy_to_gy3}")
    private String transformGY_GY3;

    @Value("${rest_channel}")
    private String channel;

    private final static Logger logger = Logger.getLogger(RestApiService.class);

    @Autowired
    private ISpVappService spVappService;

    @Autowired
    private IUpOrderQuotaService quotaService;

    @Autowired
    private ISpFileStorageService spFileStorageService;

    @Autowired
    private IUpProductService productService;

    @Autowired
    private IRestMessageService restMessageService;

    @Autowired
    private ISpElasticIpService spElasticIpService;

    @Autowired
    private ISpRegionService spRegionService;

    @Override
    public RestResponseBean userAdd(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        String userName = getValiStringParams(params, "userName");
        String userId = getValiStringParams(params, "userId");

        List<UpUser> users = dao.list(UpUser.class, "username", userName);
        if(!"85153467562".equals(customNo)) {
            RestAssertUtil.checkRuntime(users.size() == 0, "userName:" + userName + "已存在");
            users = dao.list(UpUser.class, "ssoUserId", userId);
            RestAssertUtil.checkRuntime(users.size() == 0, "userId:" + userId + "已存在");

            SpOrg org = getSpOrgByCustomNoAllowNull(customNo);
            RestCustom custom = getRestCustomByCustomNo(customNo);
            UpUser user = new UpUser();
            user.setSsoUserId(userId);
            user.setName(userName);
            user.setUsername(userName);
            user.setPassword(EncryptUtil.encryptWithRSA("1qaz@WSX"));
            user.setOrg(org);
            user.setIsArrearage(false);
            user.setSystemAdmin(false);
            user.setIsDelete(false);
            user.setType("sso");
            user.setCustom(custom);
            this.dao.insert(user);

        } else {
            logger.info("85153467562 user add." );
            if(users == null || users.size()==0) {
                SpOrg org = getSpOrgByCustomNoAllowNull(customNo);
                RestCustom custom = getRestCustomByCustomNo(customNo);
                UpUser user = new UpUser();
                user.setSsoUserId(userId);
                user.setName(userName);
                user.setUsername(userName);
                user.setPassword(EncryptUtil.encryptWithRSA("1qaz@WSX"));
                user.setOrg(org);
                user.setIsArrearage(false);
                user.setSystemAdmin(false);
                user.setIsDelete(false);
                user.setType("sso");
                user.setCustom(custom);
                this.dao.insert(user);
            } else {
                logger.info("85153467562 user exist ignore." );
            }
        }


        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean userDelete(JSONObject params) {
        String userId = getValiStringParams(params, "userId");
        List<UpUser> users = dao.list(UpUser.class, "ssoUserId", userId);
        RestAssertUtil.checkRuntime(users.size() > 0, "未找到userId" + userId + "的用户信息");
        UpUser user = users.get(0);
        this.dao.delete(UpUser.class, user.getId());
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean quotaAdd(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        String contractName = getValiStringParams(params, "contractName");
        String mainAgreementId = getValiStringParams(params, "mainAgreementId");
        JSONArray cloudProudcts = getValiJSONArrayParams(params, "cloudProudcts");
        SpRegionEntity sourceRegion = getRegionByCode(getValiStringParams(params, "cityId"));
        Map<ProductType, List<UpOrderQuotaDetail>> map = new LinkedHashMap<>();
        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);

        SpRegionEntity region = transformGY_GY3(org,sourceRegion);

        UpUser user = null;
        RestCustom custom = null;
        if(org == null){
            custom = getRestCustomByCustomNo(customNo);
//            List<SpOrg> orgs = queryDao.queryHql("from SpOrg where status = 'active' and customNo is null and name not in ('System','Public') and (isDelete is null or isDelete = false) order by name", null);
//            RestAssertUtil.checkRuntime(orgs.size() > 0, "租户资源不足，请联系管理员");
//            org = orgs.get(0);
//            org.setCustomNo(customNo);
//            org.setIsDelete(false);
//            org.setRuntimeStatus(custom.getRuntimeStatus());
//            this.dao.update(org);
            List<UpUser> userList = custom.getUserList();
//            for(UpUser ctUser : userList){
//                ctUser.setOrg(org);
//            }
//            dao.update(userList, "org");
            user = userList.get(0);
        }else{
            user = getUpUserByOrg(org);
        }

        for (int i = 0; i < cloudProudcts.length(); i++) {
            JSONObject item;
            try {
                item = cloudProudcts.getJSONObject(i);
            } catch (JSONException e) {
                logger.error("[REST_GETPARAMS] error, Params" + params + " | " + e.getMessage());
                throw new RestValidateException(e.getMessage());
            }
            String doorOrderItemId = getValiStringParams(item, "doorOrderItemId");
            String prodName = getValiStringParams(item, "prodName");
            String prodCode = getValiStringParams(item, "prodCode");
            Integer standard = null;
            String prodType = getValiStringParams(item, "prodType");
            ProductType type = getQuotaType(prodType);

            if(type == ProductType.VPC){
                continue;
            }

            Integer cpu = null;
            Integer memoryG = null;
            Integer diskG = null;
            Integer diskType = null;
            Boolean isCustom = false;
            // 二编产品改造
            if(RestCustomQuotaProductCode.isMatcher(prodCode)){
                if(type == ProductType.SERVER){
                    cpu = checkCustomCpu(getValiIntegerParams(item, "cpu"));
                    memoryG = checkCustomMemory(getValiIntegerParams(item, "memoryG"));
                    diskG = getValiIntegerParams(item, "diskG");
                }
                if(type == ProductType.STORAGE){
                    diskG = getValiIntegerParams(item, "diskG");
                    diskType = getValiIntegerParams(item, "diskType");
                }
                if(type == ProductType.FILE_STORAGE || type == ProductType.OBJECT_STORAGE){
                    diskG = getValiIntegerParams(item, "diskG");
                }
                if (type == ProductType.ELASTIC_IP || type == ProductType.BACKUP) {
                    standard = getValiIntegerParams(item, "standard");
                }
                isCustom = true;
            }else{
                if (type == ProductType.BACKUP) {
                    standard = getValiIntegerParams(item, "standard");
                }
            }

            RestAssertUtil.checkRuntime(availProductType(type, region), type.getTitle() + "暂停下单");
            checkProductCode(prodCode, type, region, diskType);
            if(isCustom) {
                checkCustomStandardDiskData(type, standard, diskG);
            }

            UpOrderQuotaDetail detail = new UpOrderQuotaDetail(region, QuotaCatalog.NEW, prodName, prodCode, org, doorOrderItemId, type);
            detail.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
            detail.setSourceRegion(sourceRegion);
            detail.setCpu(cpu);
            detail.setMemoryG(memoryG);
            detail.setDiskG(diskG);
            detail.setDiskType(diskType);
            detail.setIsCustom(isCustom);
            if (standard != null) {
                detail.setValue(standard.toString());
            }
            if (map.containsKey(type)) {
                map.get(type).add(detail);
            } else {
                map.put(type, new ArrayList<>(Arrays.asList(detail)));
            }
        }
        if(map.size() == 0){
            return RestResponseBean.success();
        }

        List<UpOrderQuota> quotaList = new ArrayList<>();
        for (ProductType type : map.keySet()) {
            UpOrderQuota quota = new UpOrderQuota(region, QuotaCatalog.NEW, mainAgreementId, contractName, user, user.getOrg(), type, customNo);
            quota.setCatalog(QuotaCatalog.NEW);
            quota.setRestCustom(custom);
            quota.setQuotaDetailList(ListUtil.toList(map.get(type), (l, item) -> {
                checkDoorOrderItemIdUnique(item.getSubCode());
                item.setQuota(quota);
                l.add(item);
            }));
            quota.setSourceRegion(sourceRegion);
            quotaList.add(quota);
        }
        dao.insert(quotaList);
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean quotaChange(JSONObject params) {
        String childrenAgreementId = null;
        try {
            childrenAgreementId = params.getString("childrenAgreementId");
        } catch (JSONException e) {
        }
        String customNo = getValiStringParams(params, "customNo");
        String mainAgreementId = getValiStringParams(params, "mainAgreementId");
        JSONArray cloudProudcts = getValiJSONArrayParams(params, "cloudProudcts");

//        SpOrg org = getSpOrgByCustomNo(customNo);
//        UpUser user = getUpUserByOrg(org);

        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);
        UpUser user = null;
        RestCustom custom = null;
        if(org == null){
            custom = getRestCustomByCustomNo(customNo);
            List<UpUser> userList = custom.getUserList();
            user = userList.get(0);
        }else{
            user = getUpUserByOrg(org);
        }

        List<UpOrderQuota> dbQuotaList = dao.list(UpOrderQuota.class, "code", mainAgreementId);
        if(dbQuotaList.get(0).getSpOrg() == null){
            RestAssertUtil.checkRuntime(dbQuotaList.size() > 0 && dbQuotaList.get(0).getRestCustom().getCustomNo().equals(customNo), "customNo:" + customNo + "内未找到mainAgreementId:" + mainAgreementId + "的信息");
        }else{
            RestAssertUtil.checkRuntime(dbQuotaList.size() > 0 && dbQuotaList.get(0).getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "内未找到mainAgreementId:" + mainAgreementId + "的信息");
        }

        UpOrderQuota dbQuota = dbQuotaList.get(0);

        for (int i = 0; i < cloudProudcts.length(); i++) {
            JSONObject item;
            try {
                item = cloudProudcts.getJSONObject(i);
            } catch (JSONException e) {
                logger.error("[REST_GETPARAMS] error, Params" + params + " | " + e.getMessage());
                throw new RestValidateException(e.getMessage());
            }
            String doorOrderItemId = getValiStringParams(item, "doorOrderItemId");
            String prodName = getValiStringParams(item, "prodName");
            String prodCode = getValiStringParams(item, "prodCode");

            String operationType = getValiStringParams(item, "operationType");
            RestAssertUtil.checkValidate(operationType.equals("1") || operationType.equals("2") || operationType.equals("3"), "operationType:" + operationType + "非法参数");


            // TODO
            Integer standard = null;
            String prodType = getValiStringParams(item, "prodType");
            ProductType type = getQuotaType(prodType);


            // delete
            if (operationType.equals("1")) {
                UpOrderQuotaDetail detail = getQuotaDetailBySubcode(doorOrderItemId);

                RestAssertUtil.checkRuntime(detail, "doorOrderItemId" + doorOrderItemId + "不存在");
                if(detail.getSpOrg() != null){
                    RestAssertUtil.checkRuntime(detail.getSpOrg() != null && detail.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "内不存在doorOrderItemId:" + doorOrderItemId);
                }

                RestAssertUtil.checkRuntime(detail.getQuotaDetailStatus() == QuotaDetailStatus.start, "doorOrderItemId" + doorOrderItemId + "的状态为" + detail.getQuotaDetailStatus().getTitle() + ", 无法撤单");
                quotaService.deleteQuotaDetail(detail.getId());

            }


            // new
            if (operationType.equals("2")) {
                checkDoorOrderItemIdUnique(doorOrderItemId);


                UpOrderQuotaDetail detail = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.NEW, prodName, prodCode, org, doorOrderItemId, type);
                detail.setSourceRegion(dbQuota.getSourceRegion());
                detail.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));

                Integer cpu = null;
                Integer memoryG = null;
                Integer diskG = null;
                Integer diskType = null;
                Boolean isCustom = false;
                // 二编产品改造
                if(RestCustomQuotaProductCode.isMatcher(prodCode)){
                    if(type == ProductType.SERVER){
                        cpu = checkCustomCpu(getValiIntegerParams(item, "cpu"));
                        memoryG = checkCustomMemory(getValiIntegerParams(item, "memoryG"));
                        diskG = getValiIntegerParams(item, "diskG");
                    }
                    if(type == ProductType.STORAGE){
                        diskG = getValiIntegerParams(item, "diskG");
                        diskType = getValiIntegerParams(item, "diskType");
                    }
                    if(type == ProductType.FILE_STORAGE || type == ProductType.OBJECT_STORAGE){
                        diskG = getValiIntegerParams(item, "diskG");
                    }
                    if (type == ProductType.ELASTIC_IP || type == ProductType.BACKUP) {
                        standard = getValiIntegerParams(item, "standard");
                    }
                    isCustom = true;
                }else{
                    if (type == ProductType.BACKUP) {
                        standard = getValiIntegerParams(item, "standard");
                    }
                }

                if (standard != null) {
                    detail.setValue(standard.toString());
                }
                if(isCustom){
                    checkCustomStandardDiskData(type, standard, diskG);
                }
                checkProductCode(prodCode, type, dbQuota.getRegion(), diskType);
                RestAssertUtil.checkRuntime(availProductType(type, dbQuota.getRegion()), type.getTitle() + "暂停下单");

                detail.setCpu(cpu);
                detail.setMemoryG(memoryG);
                detail.setDiskG(diskG);
                detail.setDiskType(diskType);
                detail.setIsCustom(isCustom);

                UpOrderQuota quota = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.NEW, mainAgreementId, dbQuota.getName(), user, org, type, customNo);
                quota.setSourceRegion(dbQuota.getSourceRegion());
                quota.setQuotaDetailList(new ArrayList<>(Arrays.asList(detail)));
                quota.setRestCustom(custom);
                quota.setChildCode(childrenAgreementId);
                dao.insert(quota);
            }

            // update
            if (operationType.equals("3")) {
                UpOrderQuotaDetail detail = getQuotaDetailBySubcode(doorOrderItemId);
                RestAssertUtil.checkRuntime(detail != null, "doorOrderItemId:"+doorOrderItemId+"不存在");
                if(detail.getSpOrg() != null) {
                    RestAssertUtil.checkRuntime(detail != null && detail.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "内不存在doorOrderItemId:" + doorOrderItemId);
                }
                RestAssertUtil.checkRuntime(detail.getQuotaDetailStatus() != QuotaDetailStatus.deploying, "doorOrderItemId:" + doorOrderItemId + "正在部署，无法变更");
                Integer cpu = null;
                Integer memoryG = null;
                Integer diskG = null;
                Integer diskType = null;
                Boolean isCustom = false;
                if(RestCustomQuotaProductCode.isMatcher(prodCode)){
                    if(type == ProductType.SERVER){
                        cpu = checkCustomCpu(getValiIntegerParams(item, "cpu"));
                        memoryG = checkCustomMemory(getValiIntegerParams(item, "memoryG"));
                        diskG = getValiIntegerParams(item, "diskG");
                    }
                    if(type == ProductType.STORAGE){
                        diskG = getValiIntegerParams(item, "diskG");
                        diskType = getValiIntegerParams(item, "diskType");
                    }
                    if(type == ProductType.FILE_STORAGE || type == ProductType.OBJECT_STORAGE){
                        diskG = getValiIntegerParams(item, "diskG");
                    }
                    if (type == ProductType.ELASTIC_IP || type == ProductType.BACKUP) {
                        standard = getValiIntegerParams(item, "standard");
                    }
                    isCustom = true;
                    if(isCustom) {
                        checkCustomStandardDiskData(type, standard, diskG);
                    }
                }

                if (detail.getQuotaDetailStatus() == QuotaDetailStatus.start) {
                    // 未开始的配置，直接调整
                    detail.setProductCode(prodCode);
                    detail.setCpu(cpu);
                    detail.setMemoryG(memoryG);
                    detail.setDiskG(diskG);
                    detail.setDiskType(diskType);
                    if(standard != null){
                        detail.setValue(standard.toString());
                    }
                    detail.setIsCustom(isCustom);
                    this.dao.update(detail, "productCode");
                } else if (detail.getQuotaDetailStatus() == QuotaDetailStatus.finish) {
                    if (type == ProductType.SERVER) {
                        checkDoorOrderItemIdUnfinish(doorOrderItemId);
                        String instId = getValiStringParams(item, "instId");
                        SpVm vm = ICloudService.getEntityByUuid(SpVm.class, org, instId, false);
                        RestAssertUtil.checkRuntime(vm != null && vm.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "集团内未查找到instId:" + instId + "的云服务器");
                        user = vm.getOwner() != null ? vm.getOwner() : vm.getOrder() != null ? vm.getOrder().getOwner() : user;
                        RestAssertUtil.checkRuntime(user, "instId:" + instId + "所有者的数据异常，操作失败");
                        RestAssertUtil.checkRuntime(vm.getUpdateOrderQuotaDetail() == null || vm.getUpdateOrderQuotaDetail().getQuotaDetailStatus() == QuotaDetailStatus.finish || vm.getUpdateOrderQuotaDetail().getStatus() == RecordStatus.deleted, "instIdl:" + instId + "有未完成的变更单");
                        RestAssertUtil.checkRuntime(vm.getOrder() != null , "instId:" + instId + "状态异常，不允许变更");

                        if(!isCustom){
                            UpProductVmSetBean updateVmSetBean = productService.getVmSetByCode(prodCode);
                            RestAssertUtil.checkRuntime(updateVmSetBean.getServerType() != null && updateVmSetBean.getServerType() == vm.getOrder().getVmSet().getServerType(), "prodCode:" + prodCode + "的类型与instId:" + instId + "的云服务器类型不匹配");
                        }

                        UpOrderQuotaDetail detailParams = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.UPDATE, prodName, prodCode, org, doorOrderItemId, type);
                        detailParams.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                        detailParams.setSourceRegion(dbQuota.getSourceRegion());
                        detailParams.setCpu(cpu);
                        detailParams.setMemoryG(memoryG);
                        detailParams.setDiskG(diskG);
//                        detail.setDiskType(diskType);
                        detailParams.setIsCustom(isCustom);

                        UpOrderQuota quotaParmas = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.UPDATE, mainAgreementId, dbQuota.getName(), user, org, type, customNo);
                        quotaParmas.setSourceRegion(dbQuota.getSourceRegion());
                        quotaParmas.setChildCode(childrenAgreementId);
                        quotaParmas.setQuotaDetailList(new ArrayList<>(Arrays.asList(detailParams)));
                        dao.insert(quotaParmas);
                        vm.setUpdateOrderQuotaDetail(detailParams);
                        dao.update(vm, "updateOrderQuotaDetail");
                    } else if (type == ProductType.ELASTIC_IP) {
                        checkDoorOrderItemIdUnfinish(doorOrderItemId);
                        String instId = getValiStringParams(item, "instId");
                        SpElasticIp elasticIp = ICloudService.getEntityByUuid(SpElasticIp.class, org, instId, false);
                        RestAssertUtil.checkRuntime(elasticIp != null && elasticIp.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "集团内未查找到instId:" + instId + "的公网IP");
                        user = elasticIp.getOrder() == null ? null : elasticIp.getOrder().getOwner();
                        RestAssertUtil.checkRuntime(user, "instId:" + instId + "所有者的数据异常，操作失败");
                        Integer bandwidthUnit = null;
                        if(!isCustom){
                            UpProductBandwidthSetBean bandwidthSet = productService.getBandwidthSet(prodCode);
                            RestAssertUtil.checkRuntime(bandwidthSet, "prodCode:" + prodCode + "未找到配置");
                            bandwidthUnit = bandwidthSet.getUnit();
                        }else{
                            bandwidthUnit = standard;
                        }
                        Boolean isSuccess = true;
                        String failReason = null;
                        try {
                            spElasticIpService.changeBandwidth(org, elasticIp.getId(), bandwidthUnit);
                        } catch (Exception e) {
                            isSuccess = false;
                            failReason = e.getMessage();
                        }

                        UpOrderQuotaDetail detailParams = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.UPDATE, prodName, prodCode, org, doorOrderItemId, type);
                        detailParams.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                        detailParams.setSourceRegion(dbQuota.getSourceRegion());
                        detailParams.setQuotaDetailStatus(QuotaDetailStatus.finish);
                        detailParams.setTargetId(elasticIp.getId());
                        detailParams.setIsUpdateSuccess(true);
                        if(standard!=null){
                            detailParams.setValue(standard.toString());
                        }
                        detailParams.setIsCustom(isCustom);
                        UpOrderQuota quotaParmas = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.UPDATE, mainAgreementId, dbQuota.getName(), user, org, type, customNo);
                        quotaParmas.setSourceRegion(dbQuota.getSourceRegion());
                        quotaParmas.setChildCode(childrenAgreementId);
                        quotaParmas.setQuotaDetailList(new ArrayList<>(Arrays.asList(detailParams)));
                        dao.insert(quotaParmas);
                        restMessageService.updateInstanceMessage(dbQuota.getRegion(), elasticIp, isSuccess, failReason, elasticIp.getOrder().getOwner().getSsoUserId(), mainAgreementId, doorOrderItemId, channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));

                    } else if (type == ProductType.FILE_STORAGE) {
                        checkDoorOrderItemIdUnfinish(doorOrderItemId);
                        String instId = getValiStringParams(item, "instId");
                        SpFileStorage entity = ICloudService.getEntityByUuid(SpFileStorage.class, org, instId, false);
                        RestAssertUtil.checkRuntime(entity != null && entity.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "集团内未查找到instId:" + instId + "的文件存储");
                        user = entity.getOwner() != null ? entity.getOwner() : entity.getOrder() != null ? entity.getOrder().getOwner() : user;
                        RestAssertUtil.checkRuntime(user, "instId:" + instId + "所有者的数据异常，操作失败");
                        Long fileStorageUnit = null;
                        if(isCustom){
                            fileStorageUnit = diskG.longValue();
                        }else{
                            UpProductDiskSetBean diskSet = productService.getDiskSet(ProductDiskSetType.file_storage, prodCode);
                            RestAssertUtil.checkRuntime(diskSet, "prodCode:" + prodCode + "未找到配置");
                            fileStorageUnit = diskSet.getUnit().longValue();
                        }
                        try {
                            RestAssertUtil.checkRuntime(entity.getSizeG().compareTo(fileStorageUnit) < 0, "待变更文件存储大小必须大于现有配置");
                            entity.setSizeG(fileStorageUnit);
                        } catch (Exception e) {
                            RestAssertUtil.checkRuntime(false, e.getMessage());
                        }
                        Boolean isSuccess = true;
                        String failReason = null;
                        try {
                            isSuccess = spFileStorageService.updateFileStorage(entity);
                        } catch (Exception e) {
                            failReason = e.getMessage();
                        }
                        UpOrderQuotaDetail detailParams = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.UPDATE, prodName, prodCode, org, doorOrderItemId, type);
                        detailParams.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                        detailParams.setSourceRegion(dbQuota.getSourceRegion());
                        detailParams.setQuotaDetailStatus(QuotaDetailStatus.finish);
                        detailParams.setTargetId(entity.getId());
                        detailParams.setIsUpdateSuccess(isSuccess);
                        detailParams.setDiskG(diskG);
//                        if(standard!=null){
//                            detailParams.setValue(standard.toString());
//                        }
                        detailParams.setIsCustom(isCustom);
                        UpOrderQuota quotaParmas = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.UPDATE, mainAgreementId, dbQuota.getName(), entity.getOrder().getOwner(), org, type, customNo);
                        quotaParmas.setSourceRegion(dbQuota.getSourceRegion());
                        quotaParmas.setChildCode(childrenAgreementId);
                        quotaParmas.setQuotaDetailList(new ArrayList<>(Arrays.asList(detailParams)));
                        dao.insert(quotaParmas);
                        restMessageService.updateInstanceMessage(dbQuota.getSourceRegion(), entity, isSuccess, failReason, entity.getOrder().getOwner().getSsoUserId(), mainAgreementId, doorOrderItemId, channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                    } else if (type == ProductType.BACKUP) {
                        checkDoorOrderItemIdUnfinish(doorOrderItemId);
                        List<UpOrderQuotaDetail> quotaDetails = this.dao.list(UpOrderQuotaDetail.class, MapUtil.of("spOrg", org, "type", ProductType.BACKUP, "catalog", QuotaCatalog.NEW, "quotaDetailStatus", QuotaDetailStatus.start));
                        RestAssertUtil.checkRuntime(quotaDetails != null && quotaDetails.size() > 0, "customNo:" + customNo + "集团内未设置备份额度");
                        UpOrderQuotaDetail entity = quotaDetails.get(0);
                        standard = getValiIntegerParams(item, "standard");
                        RestAssertUtil.checkRuntime(Integer.valueOf(entity.getValue()) < standard, "standard:" + standard + "必须大于" + entity.getValue());
                        entity.setValue(standard.toString());
                        dao.update(entity);
                        UpOrderQuotaDetail detailParams = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.UPDATE, prodName, prodCode, org, doorOrderItemId, type);
                        detailParams.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                        detailParams.setSourceRegion(dbQuota.getSourceRegion());
                        detailParams.setQuotaDetailStatus(QuotaDetailStatus.finish);
                        detailParams.setTargetId(entity.getId());
                        detailParams.setIsUpdateSuccess(true);
                        if(standard!=null){
                            detailParams.setValue(standard.toString());
                        }
                        detailParams.setIsCustom(isCustom);
                        UpOrderQuota quotaParmas = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.UPDATE, mainAgreementId, dbQuota.getName(), user, org, type, customNo);
                        quotaParmas.setSourceRegion(dbQuota.getSourceRegion());
                        quotaParmas.setChildCode(childrenAgreementId);
                        quotaParmas.setQuotaDetailList(new ArrayList<>(Arrays.asList(detailParams)));
                        dao.insert(quotaParmas);
                    } else if (type == ProductType.CLOUD_REDIS || type == ProductType.CLOUD_MYSQL) {
                        checkDoorOrderItemIdUnfinish(doorOrderItemId);
                        String instId = getValiStringParams(item, "instId");
                        SpVapp entity = ICloudService.getEntityByUuid(SpVapp.class, org, instId, false);
                        RestAssertUtil.checkRuntime(entity != null && entity.getSpOrg().getId().equals(org.getId()), "customNo:" + customNo + "集团内未查找到instId:" + instId + "的实例");
                        user = entity.getOwner() != null ? entity.getOwner() : entity.getOrder() != null ? entity.getOrder().getOwner() : user;
                        RestAssertUtil.checkRuntime(user, "instId:" + instId + "所有者的数据异常，操作失败");
                        RestAssertUtil.checkRuntime(entity.getUpdateOrderQuotaDetail() == null || entity.getUpdateOrderQuotaDetail().getQuotaDetailStatus() == QuotaDetailStatus.finish || entity.getUpdateOrderQuotaDetail().getStatus() == RecordStatus.deleted, "instIdl:" + instId + "有未完成的变更单");
                        RestAssertUtil.checkRuntime(entity.getOrder() != null, "instId:" + instId + "状态异常，不允许变更");
                        UpProductVmSetBean updateVmSetBean = productService.getVmSetByCode(prodCode);
//						RestAssertUtil.checkRuntime(updateVmSetBean.getServerType() != null && updateVmSetBean.getServerType() == entity.getOrder().getVmSet().getServerType(),"prodCode:"+prodCode+"的类型与instId:"+instId+"的产品类型不匹配");
                        if (type == ProductType.CLOUD_MYSQL) {
                            RestAssertUtil.checkRuntime(entity.getOrder().getVmSet().getProductCode().indexOf("single") == prodCode.indexOf("single"), "prodCode:" + prodCode + "的类型与instId:" + instId + "的产品类型不匹配");
                            RestAssertUtil.checkRuntime(entity.getOrder().getVmSet().getDiskUnit() >= updateVmSetBean.getDiskUnit(), "prodCode:" + prodCode + "的数据盘小于与instId:" + instId + "的数据盘");
                        }
                        UpOrderQuotaDetail detailParams = new UpOrderQuotaDetail(dbQuota.getRegion(), QuotaCatalog.UPDATE, prodName, prodCode, org, doorOrderItemId, type);
                        detailParams.setChannel(channel == null ? UpProductSystemEnums.QuotaDetailChannel.cloud_net : UpProductSystemEnums.QuotaDetailChannel.valueOf(channel));
                        detailParams.setSourceRegion(dbQuota.getSourceRegion());
                        UpOrderQuota quotaParmas = new UpOrderQuota(dbQuota.getRegion(), QuotaCatalog.UPDATE, mainAgreementId, dbQuota.getName(), user, org, type, customNo);
                        quotaParmas.setSourceRegion(dbQuota.getSourceRegion());
                        quotaParmas.setChildCode(childrenAgreementId);
                        quotaParmas.setQuotaDetailList(new ArrayList<>(Arrays.asList(detailParams)));
                        dao.insert(quotaParmas);
                        entity.setUpdateOrderQuotaDetail(detailParams);
                        dao.update(entity, "updateOrderQuotaDetail");
                    } else {
                        RestAssertUtil.checkRuntime(false, "prodType:" + type + "不支持变更");
                    }
                }
            }
        }
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean orgAdd(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        List<SpOrg> orgs = dao.list(SpOrg.class, MapUtil.of("customNo", customNo, "isDelete", false));
        List<RestCustom> customs = dao.list(RestCustom.class, "customNo", customNo);

        RestAssertUtil.checkRuntime(orgs.size() == 0 && customs.size() == 0, "已存在customNo:" + customNo + "的集团信息");
        orgs = queryDao.queryHql("from SpOrg where status = 'active' and customNo is null and name not in ('System','Public') and (isDelete is null or isDelete = false) order by id", null);
        RestAssertUtil.checkRuntime(orgs.size() > 0, "租户资源不足，无法绑定集团编码");

//        SpOrg org = orgs.get(0);
//        org.setCustomNo(customNo);
//        org.setIsDelete(false);
//        this.dao.update(org);

        RestCustom custom = new RestCustom(customNo);
        custom.setName(customNo);
        this.dao.insert(custom);

        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean orgStart(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);
        if(org!=null) {
            if (org.getRuntimeStatus() == null) {
                org.setRuntimeStatus(SpOrgRuntimeStatus.active);
            }
//            RestAssertUtil.checkRuntime(org.getRuntimeStatus() == SpOrgRuntimeStatus.suspend, "无法启动customNo:" + customNo + "，当前的状态为" + org.getRuntimeStatus().getTitle());
            org.setRuntimeStatus(SpOrgRuntimeStatus.pending_start);
            this.dao.update(org, "runtimeStatus");
        }else{
            RestCustom custom = getRestCustomByCustomNo(customNo);
            if (custom.getRuntimeStatus() == null) {
                custom.setRuntimeStatus(SpOrgRuntimeStatus.active);
            }
            custom.setRuntimeStatus(SpOrgRuntimeStatus.pending_start);
            this.dao.update(custom, "runtimeStatus");
        }
        restMessageService.createOrgMessage(customNo, "6", true, null);

        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean orgStop(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);
        if(org!=null){
//            RestAssertUtil.checkRuntime(org.getRuntimeStatus() == null || org.getRuntimeStatus() == SpOrgRuntimeStatus.active, "无法挂起customNo:" + customNo + "，当前的状态为" + org.getRuntimeStatus().getTitle());
            org.setRuntimeStatus(SpOrgRuntimeStatus.pending_suspend);
            this.dao.update(org, "runtimeStatus");
        }else{
            RestCustom custom = getRestCustomByCustomNo(customNo);
            custom.setRuntimeStatus(SpOrgRuntimeStatus.pending_suspend);
            this.dao.update(custom, "runtimeStatus");
        }
        restMessageService.createOrgMessage(customNo, "5", true, null);
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean orgDelete(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");

        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);

        Set<Integer> ids = new HashSet<>();
        if(org != null){
            RestAssertUtil.checkRuntime(org.getIsDelete() == null || !org.getIsDelete(), "customNo:" + customNo + "的集团信息正在删除");
            org.setIsDelete(true);
            this.dao.update(org);
            ids = new HashSet<>(ListUtil.toList(dao.list(UpUser.class, "org", org), (l, e) -> {
                l.add(e.getId());
            }));
        }else{
            RestCustom custom = getRestCustomByCustomNo(customNo);
            ids.addAll(ListUtil.toList(custom.getUserList(), (l, e) -> {
                l.add(e.getId());
            }));
            this.dao.delete(RestCustom.class, custom.getId());

        }

        if(ids.size() > 0){
            this.dao.delete(UpUser.class, ids);
        }
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean vappVmmetrics(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        String instId = getValiStringParams(params, "instId");
        String queryParam = getValiStringParams(params, "queryParam");
        SpOrg org = getSpOrgByCustomNo(customNo);
        SpVm vm = ICloudService.getEntityByUuid(SpVm.class, org, instId, false);
        RestAssertUtil.checkRuntime(vm, "customNo:" + customNo + "集团内未查找到instId:" + instId + "的云服务器");
        ThreadCache.setUserLogin(getUpUserByOrg(org).getId(), vm.getRegion());
        ArrayList<SpMetricsBean> beans = spVappService.queryVmHistoryMetrics(vm.getId(), Float.valueOf(queryParam));
//		ThreadCache.setSystemAdminLogin();
        return RestResponseBean.success(beans);
    }

    @Override
    public RestResponseBean vappNetmetrics(JSONObject params) {
        //TODO:YUNWANG
        String customNo = getValiStringParams(params, "customNo");
        SpOrg spOrg = getSpOrgByCustomNo(customNo);
        SpRegionEntity region = getRegionByCode(getValiStringParams(params, "cityId"));
        region = transformGY_GY3(spOrg, region);
        SpServerConnection connection = new SpServerConnection();

        SpOrg netParams = new SpOrg();
        netParams.setName(spOrg.getName());
        netParams.setServerConnection(connection);
        List<SpOVDC> ovdcList = dao.list(SpOVDC.class, MapUtil.of("status", RecordStatus.active, "region", region, "spOrg", spOrg));
        AssertUtil.check(ovdcList, "未找到OVDC，区域："+region+" 组织："+spOrg.getName());
        return RestResponseBean.error(500, "查询失败","");
    }

    @Override
    public RestResponseBean reservationInquire(JSONObject params) {
        String customNo = getValiStringParams(params, "customNo");
        SpRegionEntity region = getRegionByCode(getValiStringParams(params, "cityId"));
        JSONArray perambulationContents = getValiJSONArrayParams(params, "perambulationContents");

        boolean available = true;
        String desc = "";
        SpOrg org = getSpOrgByCustomNoAllowNull(customNo);

        //TODO
        region = transformGY_GY3(org, region);

        RestResultDataBean result = new RestResultDataBean();

        if(org == null){
            RestCustom custom = getRestCustomByCustomNo(customNo);
            RestAssertUtil.checkRuntime(custom.getUserList().size() > 0, "customNo" + customNo + "未绑定用户");
            List<SpOrg> orgs = queryDao.queryHql("from SpOrg where status = 'active' and customNo is null and name not in ('System','Public') and (isDelete is null or isDelete = false) order by name", null);
            if(orgs.size() == 0){
                available = false;
                desc = "租户资源不足;";
            }

            result.setResourceProfile(available ? 1 : -1);
            result.setDesc(desc);
            return RestResponseBean.success(result);
        }

        getUpUserByOrg(org);

        List<SpPVDC> pvdcList = dao.list(SpPVDC.class, "region", region);

        String regionMessage = "";
        Long total_cpu = 0l;
        Long total_memory = 0l;
        Long total_storage = 0l;
        Long fileStorage = 0l;
        Long objectStorage = 0l;
        Long veritasBackup = 0l;
        Integer eip = 0;
        for (int i = 0; i < perambulationContents.length(); i++) {
            JSONObject item;
            try {
                item = perambulationContents.getJSONObject(i);
            } catch (JSONException e) {
                logger.error("[REST_GETPARAMS] error, Params" + params + " | " + e.getMessage());
                throw new RestValidateException(e.getMessage());
            }
            String prodCode = getValiStringParams(item, "prodCode");
            Integer amount = getValiIntegerParams(item, "amount");

            if(RestCustomQuotaProductCode.isMatcher(prodCode)){
                ProductType productType = RestCustomQuotaProductCode.valueOf(prodCode).getProductType();
                Integer cpu = null;
                Integer memory = null;
                Integer disk = null;
                Integer standard = null;
                Integer diskType = null;
                if(productType == ProductType.SERVER){
                    cpu = checkCustomCpu(getValiIntegerParams(item, "cpu"));
                    memory = checkCustomMemory(getValiIntegerParams(item, "memoryG"));
                    disk = getValiIntegerParams(item, "diskG");
//                    storage += disk.longValue() * amount;
                    total_cpu += cpu.longValue() * amount;
                    total_memory += memory.longValue() * amount;
                    total_storage += disk.longValue() * amount;
                }
                if(productType == ProductType.BACKUP){
                    standard = getValiIntegerParams(item, "standard");
                    veritasBackup += standard.longValue() * amount;
                }
                if(productType == ProductType.ELASTIC_IP){
                    standard = getValiIntegerParams(item, "standard");
                }
                if(productType == ProductType.STORAGE){
                    disk = getValiIntegerParams(item, "diskG");
                    diskType = getValiIntegerParams(item, "diskType");
                    total_storage += disk.longValue() * amount;
                }
                if(productType == ProductType.FILE_STORAGE){
                    disk = getValiIntegerParams(item, "diskG");
                    fileStorage += disk.longValue() * amount;
                }
                if(productType == ProductType.OBJECT_STORAGE){
                    disk = getValiIntegerParams(item, "diskG");
                    objectStorage += disk.longValue() * amount;
                }

                checkCustomStandardDiskData(productType, standard, disk);
                checkProductCode(prodCode, productType, region, diskType);
            }else{
//			    Integer standard = null;
//              //TODO 暂时屏蔽
//              RestAssertUtil.checkRuntime(!prodCode.equals("vpc_load_balance"), "负载均衡暂停下单");

                List<UpProductVmSet> vmSets = this.dao.list(UpProductVmSet.class, MapUtil.of("productCode", prodCode, "enabled", true));
                if (!vmSets.isEmpty()) {
                    UpProductVmSet entity = vmSets.get(0);
                    if (!checkProductRegion(entity, region)) {
                        available = false;
                        regionMessage += "prodCode:" + prodCode + " 在 cityId:" + region.getCode() + " 中不可用;";
                        continue;
                    }
                    total_cpu += Long.valueOf(entity.getCpuUnit() * amount);
                    total_memory += Long.valueOf(entity.getMemoryUnit() * amount);
                    total_storage += Long.valueOf((entity.getDiskUnit() == null ? 0 : entity.getDiskUnit()) * amount);

                    //TODO 暂时屏蔽
//                if( entity.getType() == UpOrderSystemEnums.ProductVmSetType.rds){
//                    RestAssertUtil.checkRuntime(false, entity.getType().getTitle() + "暂停下单");
//                }

                    continue;
                }

                List<UpProductBackupSet> backupSets = this.dao.list(UpProductBackupSet.class, MapUtil.of("productCode", prodCode, "enabled", true));
                if (!backupSets.isEmpty()) {
                    if (!checkProductRegion(backupSets.get(0), region)) {
                        available = false;
                        regionMessage += "prodCode:" + prodCode + " 在 cityId:" + region.getCode() + " 中不可用;";
                        continue;
                    }
                    veritasBackup += getValiIntegerParams(item, prodCode);
                    continue;
                }

                List<UpProductDiskSet> diskSets = this.dao.list(UpProductDiskSet.class, MapUtil.of("productCode", prodCode, "enabled", true));
                if (!diskSets.isEmpty()) {
                    UpProductDiskSet entity = diskSets.get(0);
                    if (!checkProductRegion(entity, region)) {
                        available = false;
                        regionMessage += "prodCode:" + prodCode + " 在 cityId:" + region.getCode() + " 中不可用;";
                        continue;
                    }
                    if (entity.getType() == ProductDiskSetType.file_storage) {
                        fileStorage += entity.getUnit();
                    } else if (entity.getType() == ProductDiskSetType.vm_disk) {

                        total_storage += Long.valueOf(entity.getUnit() * amount);
                    } else if (entity.getType() == ProductDiskSetType.object_storage_bucket) {
                        //TODO 暂时屏蔽
//                    RestAssertUtil.checkRuntime(false, "对象存储暂停下单");
                        objectStorage += Long.valueOf(entity.getUnit() * amount);
                    }
                    continue;
                }

                List<UpProductBandwidthSet> bandwidthSets = this.dao.list(UpProductBandwidthSet.class, MapUtil.of("productCode", prodCode, "enabled", true));
                if (!bandwidthSets.isEmpty()) {
                    eip++;
                    continue;
                }
            }
        }



        if (!pvdcList.isEmpty()) {
            if (regionMessage.length() != 0) {
                desc += regionMessage;
            }


            Long finalTotal_cpu = total_cpu;
            Long finalTotal_memory = total_memory;
            Long finalTotal_storage = total_storage;
            available = pvdcList.stream().filter(pvdc ->
                    (pvdc.getCpuTotal() - pvdc.getCpuUsed()) >= finalTotal_cpu
                    && (pvdc.getMemoryTotalMb() - pvdc.getMemoryUsedMb()) >= (finalTotal_memory * 1024)
                    && (pvdc.getStorageTotalGb() - pvdc.getStorageUsedGb()) >= finalTotal_storage * 0.75
            ).collect(Collectors.toList()).size() > 0;

            if(!available){
                desc += "CPU，内存，存储资源不足! ";
            }
        }

        //TODO veritas filestorage s3
        result.setResourceProfile(available ? 1 : -1);
        result.setDesc(desc);
        return RestResponseBean.success(result);
    }


    @Override
    public RestResponseBean createVPCMessage(JSONObject params) {
        SpOrg org = dao.load(SpOrg.class, getValiIntegerParams(params, "orgId"));
        UpUser user = getUpUserByOrg(org);

        List<SpVPCRelation> relationList = dao.list(SpVPCRelation.class, "spOrg", org);
        for (SpVPCRelation relation : relationList) {
            SpVPC vpc = relation.getVpc();
            ThreadCache.setUserLogin(user.getId(), vpc.getRegion());
            SpOVDCNetwork ovdcNetwork = relation.getOvdcNetwork();
            restMessageService.createVPCMessage(vpc, new ArrayList<>(Arrays.asList(ovdcNetwork)));
        }
        return RestResponseBean.success();
    }

    @Override
    public RestResponseBean createOrderMessage(JSONObject params) {
        Integer orderId = getValiIntegerParams(params, "orderId");
        UpOrder order = new UpOrder();
        order.setId(orderId);
        restMessageService.createOrderMessage(order);
        return null;
    }

    @Override
    public String ping(Integer ovdcId, String ip, String type) {

        return "";
    }

    private SpOrg getSpOrgByCustomNoAllowNull(String customNo) {
        List<SpOrg> orgs = dao.list(SpOrg.class, MapUtil.of("customNo", customNo, "isDelete", false));
        if(orgs.size() ==0){
            return null;
        }
        return orgs.get(0);
    }

    private RestCustom getRestCustomByCustomNo(String customNo) {
        List<RestCustom> customs = dao.list(RestCustom.class, MapUtil.of("customNo", customNo));
        RestAssertUtil.checkRuntime(customs.size() > 0, "未找到customNo:" + customNo + "的集团信息");
        return customs.get(0);
    }

    private SpOrg getSpOrgByCustomNo(String customNo) {
        List<SpOrg> orgs = dao.list(SpOrg.class, MapUtil.of("customNo", customNo, "isDelete", false));
        RestAssertUtil.checkRuntime(orgs.size() > 0, "未找到customNo:" + customNo + "的集团信息");
        return orgs.get(0);
    }

    private UpUser getUpUserByOrg(SpOrg org) {
        List<UpUser> users = dao.list(UpUser.class, MapUtil.of("org", org, "type", "sso"));
        RestAssertUtil.checkRuntime(users.size() > 0, "customNo" + org.getCustomNo() + "未绑定用户");
        UpUser user = null;
        for (UpUser ssoUser : users) {
            if (user == null || user.getCreateTm().getTime() > ssoUser.getCreateTm().getTime()) {
                user = ssoUser;
            }
        }
        return user;
    }

    private String getValiStringParams(JSONObject params, String key) {
        try {
            return (String) RestAssertUtil.checkValidate(params.getString(key), key + "不能为空");
        } catch (JSONException e) {
            logger.error("[REST_GETPARAMS] error, key:" + key + ", Params" + params + " | " + e.getMessage());
            throw new RestValidateException(e.getMessage());
        }
    }

    private Integer getValiIntegerParams(JSONObject params, String key) {
        try {
            return (Integer) RestAssertUtil.checkValidate(params.getInt(key), key + "不能为空");
        } catch (JSONException e) {
            logger.error("[REST_GETPARAMS] error, key:" + key + ", Params" + params + " | " + e.getMessage());
            throw new RestValidateException(e.getMessage());
        }
    }

    private JSONArray getValiJSONArrayParams(JSONObject params, String key) {
        try {
            JSONArray array = params.getJSONArray(key);
            RestAssertUtil.checkValidate(array != null && array.length() > 0, key + "不能为空");
            return array;
        } catch (JSONException e) {
            logger.error("[REST_GETPARAMS] error, key:" + key + ", Params" + params + " | " + e.getMessage());
            throw new RestValidateException(e.getMessage());
        }
    }

    private void checkDoorOrderItemIdUnique(String doorOrderItemId) {
        List<BigInteger> counts = queryDao.querySql(
                "select count(1) from up_order_quota_detail where status = 'active' and sub_code = '" + doorOrderItemId + "'", null);
        RestAssertUtil.checkRuntime(counts == null || counts.get(0).intValue() == 0, "doorOrderItemId:" + doorOrderItemId + " 已存在");
    }

    private void checkDoorOrderItemIdUnfinish(String doorOrderItemId) {
        List<BigInteger> counts = queryDao.querySql(
                "select count(1) from up_order_quota_detail where status = 'active' and sub_code = '" + doorOrderItemId
                        + "' and quota_detail_status != '" + QuotaDetailStatus.finish + "'", null);
        RestAssertUtil.checkRuntime(counts == null || counts.get(0).intValue() == 0, "doorOrderItemId:" + doorOrderItemId + " 有未完成的订单项");
    }

    private UpOrderQuotaDetail getQuotaDetailBySubcode(String subCode) {
        List<UpOrderQuotaDetail> details = dao.list(UpOrderQuotaDetail.class, "subCode", subCode);
        return details.size() == 0 ? null : details.get(0);
    }

    private SpRegionEntity getRegionByCode(String code) {
//        for (SpRegion region : SpRegion.values()) {
//            if (region.getCode().equals(code)) {
//                return region;
//            }
//        }
        SpRegionBean bean = Arrays.stream(spRegionService.list()).filter(b -> b.getCode().equals(code)).findFirst().get();
        RestAssertUtil.checkRuntime(bean, "cityId:" + code + "不支持");
        return BeanCopyUtil.copy(bean, SpRegionEntity.class);
    }

    // TODO 地市屏蔽SSD
    private void checkSSDByRegion(String prodCode, ProductType type, SpRegionEntity region, Integer diskType){
        if(type == ProductType.STORAGE){
            if(RestCustomQuotaProductCode.isMatcher(prodCode) && Integer.valueOf(1).equals(diskType)){
                RestAssertUtil.checkValidate(false, "地市暂不支持高速存储的产品配置");
            }

            if(!RestCustomQuotaProductCode.isMatcher(prodCode)){
                UpProductDiskSetBean diskSet = productService.getDiskSet(prodCode);
                RestAssertUtil.checkValidate(diskSet.getDiskType() == UpOrderSystemEnums.DiskType.hdd, "未找到prodCode:" + prodCode + "的产品配置");
            }
        }
    }

    private void checkProductCode(String prodCode, ProductType type, SpRegionEntity region) {
        this.checkProductCode(prodCode, type, region, null);
    }

    private void checkProductCode(String prodCode, ProductType type, SpRegionEntity region, Integer diskType) {
        checkSSDByRegion(prodCode, type, region, diskType);
        try {
            if(RestCustomQuotaProductCode.isMatcher(prodCode)){
                return;
            }
            if (type == ProductType.SERVER || type == ProductType.CLOUD_MYSQL || type == ProductType.CLOUD_ORACLE || type == ProductType.CLOUD_REDIS
            ) {
                UpProductVmSetBean bean = productService.getVmSetByCode(prodCode);
                if(bean.getSecurityVersion() != null && bean.getSecurityVersion() != region.getVersion()){
                    throw new RuntimeException();
                }
            } else if (type == ProductType.BACKUP) {
                productService.getBackupSet(prodCode);
            } else if (type == ProductType.FILE_STORAGE || type == ProductType.STORAGE) {
                productService.getDiskSet(prodCode);
            } else if (type == ProductType.ELASTIC_IP) {
                productService.getBandwidthSet(prodCode);
            }
        } catch (Exception e) {
            RestAssertUtil.checkValidate(false, "未找到prodCode:" + prodCode + "的产品配置");
        }

    }

    private void checkCustomStandardDiskData(ProductType type, Integer standard, Integer diskG){
        if(type == ProductType.SERVER){
            RestAssertUtil.checkValidate(diskG.compareTo(Integer.valueOf(50)) >= 0 && diskG.compareTo(Integer.valueOf(1024)) <= 0, "云主机系统盘大小限制50~1024G，订单中云主机系统盘为" + diskG + "G，请调整后重试" );
        }
        if(type == ProductType.STORAGE){
            RestAssertUtil.checkValidate(diskG.compareTo(Integer.valueOf(1)) >= 0 && diskG.compareTo(Integer.valueOf(2048)) <= 0, "云盘大小限制1~2048G，订单中云盘为" + diskG + "G，请调整后重试" );
        }
        if(type == ProductType.FILE_STORAGE){
            RestAssertUtil.checkValidate(diskG.compareTo(Integer.valueOf(1)) >= 0 && diskG.compareTo(Integer.valueOf(20480)) <= 0, "文件存储大小限制1~20480G，订单中文件存储为" + diskG + "G，请调整后重试" );
        }
        if(type == ProductType.OBJECT_STORAGE){
            RestAssertUtil.checkValidate(diskG.compareTo(Integer.valueOf(1)) >= 0 && diskG.compareTo(Integer.valueOf(1024000)) <= 0, "对象存储大小限制1~1024000G，订单中对象存储为" + diskG + "G，请调整后重试" );
        }
        if(type == ProductType.ELASTIC_IP){
            RestAssertUtil.checkValidate(standard.compareTo(Integer.valueOf(1)) >= 0 && standard.compareTo(Integer.valueOf(1024)) <= 0, "公网带宽大小限制1~1024MB，订单中公网带宽为" + standard + "MB，请调整后重试" );
        }
        if(type == ProductType.BACKUP){
            RestAssertUtil.checkValidate(standard.compareTo(Integer.valueOf(1)) >= 0 && standard.compareTo(Integer.valueOf(20480)) <= 0, "公网带宽大小限制1~20480G，订单中公网带宽为" + standard + "G，请调整后重试" );
        }
    }

    private Integer checkCustomCpu(Integer cpu){
        if(cpu != null) {
            RestAssertUtil.checkValidate(cpu.compareTo(Integer.valueOf(1)) >= 0 && cpu.compareTo(Integer.valueOf(64)) <= 0, "CPU大小限制1~64G，订单中CPU为" + cpu + "，请调整后重试");
        }
        return cpu;
    }

    private Integer checkCustomMemory(Integer memoryG){
        if(memoryG != null) {
            RestAssertUtil.checkValidate(memoryG.compareTo(Integer.valueOf(1)) >= 0 && memoryG.compareTo(Integer.valueOf(256)) <= 0, "内存大小限制1~256G，订单中内存为" + memoryG + "G，请调整后重试");
        }
        return memoryG;
    }

    private ProductType getQuotaType(String prodType) {
        try {
            return ProductType.valueOf(prodType);
        } catch (Exception e) {
            RestAssertUtil.checkValidate(false, "不支持prodType:" + prodType + "的产品类型");
            return null;
        }
    }

    // TODO 负载均衡，mysql，暂停下单；非贵阳节点的备份暂停下单
    private Boolean availProductType(ProductType type, SpRegionEntity region){
//        if( type == ProductType.LOAD_BALANCER){
//            return false;
//        }
//        if((type == ProductType.BACKUP) && region != SpRegion.GY){
//            return false;
//        }

//        // GY暂停 SERVER STORAGE
//        if((type == ProductType.SERVER || type == ProductType.STORAGE) && region == SpRegion.GY){
//            return false;
//        }
        return true;
    }

    // TODO 资源勘测屏蔽产品
    private static boolean checkProductRegion(BaseUpEntity entity, SpRegionEntity region) {
        if (entity instanceof UpProductDiskSet) {
            UpProductDiskSet diskSet = (UpProductDiskSet) entity;
            if (diskSet.getType() == ProductDiskSetType.file_storage) {

            }

            if (diskSet.getType() == ProductDiskSetType.object_storage_bucket) {

            }

//            //TODO GY 存储暂时屏蔽
//            if( diskSet.getType() == ProductDiskSetType.vm_disk && region == SpRegion.GY){
//                return false;
//            }
        }

        if (entity instanceof UpProductVmSet) {
            UpProductVmSet vmSet = (UpProductVmSet) entity;
            UpOrderSystemEnums.ProductVmSetType type = vmSet.getType();
            if (type == UpOrderSystemEnums.ProductVmSetType.CLOUD_CSSP
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_ANTITAMPERING
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_WEB_FIREWALL
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_FIREWALL
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_SERVER_SECURITY
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_SSLVPN
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_LOG_AUDIT
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_VULNERABILITY_SCAN
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_BASTION_HOST
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_DATABASE_AUDIT
                    || type == UpOrderSystemEnums.ProductVmSetType.CLOUD_INTRUSION_PREVENTION
                    || type == UpOrderSystemEnums.ProductVmSetType.SITUATION_AWARENESS) {
                if(vmSet.getSecurityVersion() != region.getVersion()){
                    return false;
                }
            }

//            //TODO GY虚机暂时屏蔽
//            if( type == UpOrderSystemEnums.ProductVmSetType.vm && region == SpRegion.GY){
//                return false;
//            }

            if (type == UpOrderSystemEnums.ProductVmSetType.redis) {

            }

            if (type == UpOrderSystemEnums.ProductVmSetType.rds) {

            }
        }

        if (entity instanceof UpProductBackupSet) {

        }
        return true;
    }

    // GY节点资源耗尽，暂时将GY的新租户申请移至GY3
    @Override
    public SpRegionEntity transformGY_GY3(SpOrg spOrg, SpRegionEntity region){

        return region;

    }
}
