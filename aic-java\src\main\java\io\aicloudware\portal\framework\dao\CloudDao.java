package io.aicloudware.portal.framework.dao;

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.orm.hibernate5.HibernateCallback;
import org.springframework.stereotype.Repository;

import io.aicloudware.portal.framework.entity.IResourceEntity;
import io.aicloudware.portal.framework.hibernate.SqlHelper;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.platform_vcd.entity.SpIpScope;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

@Repository
public final class CloudDao extends BaseDao implements ICloudDao {

    @Override
    public <T extends IResourceEntity> T loadBySpUuid(Class<T> clazz, SpOrg spOrg, String spUuid) {
        List<T> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<T>>() {
            @Override
            public List<T> doInHibernate(Session session) throws HibernateException {
                return (List<T>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.eq("spOrg.id", spOrg.getId()))
                        .add(Restrictions.eq("spUuid", spUuid))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.unique(dataList, spUuid);
    }

    @Override
    public <T extends IResourceEntity> T loadBySpUuid(Class<T> clazz, String spUuid) {
        List<T> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<T>>() {
            @Override
            public List<T> doInHibernate(Session session) throws HibernateException {
                return (List<T>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.eq("spUuid", spUuid))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.unique(dataList, spUuid);
    }
    
    @Override
    public <T extends IResourceEntity> T loadBySpUuidFullStatus(Class<T> clazz, String spUuid) {
        List<T> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<T>>() {
            @Override
            public List<T> doInHibernate(Session session) throws HibernateException {
                return (List<T>) DetachedCriteria.forClass(clazz)
                        .add(Restrictions.eq("spUuid", spUuid))
                        .addOrder(Order.desc("id"))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.first(dataList);
    }

    @Override
    public SpVappTemplate loadBlueprintByCatalogItemId(SpOrg spTenant, String catalogItemId) {
        List<SpVappTemplate> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<SpVappTemplate>>() {
            @Override
            public List<SpVappTemplate> doInHibernate(Session session) throws HibernateException {
                return (List<SpVappTemplate>) DetachedCriteria.forClass(SpVappTemplate.class)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.eq("spTenant.id", spTenant.getId()))
                        .add(Restrictions.eq("catalogItemId", catalogItemId))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.unique(dataList, catalogItemId);
    }

    @Override
    public SpIpScope getNetworkProfileByName(SpOrg spTenant, String name) {
        List<SpIpScope> dataList = getHibernateTemplate().executeWithNativeSession(new HibernateCallback<List<SpIpScope>>() {
            @Override
            public List<SpIpScope> doInHibernate(Session session) throws HibernateException {
                return (List<SpIpScope>) DetachedCriteria.forClass(SpIpScope.class)
                        .add(Restrictions.ne("status", RecordStatus.deleted))
                        .add(Restrictions.eq("spTenant.id", spTenant.getId()))
                        .add(Restrictions.eq("name", name))
                        .getExecutableCriteria(session)
                        .list();
            }
        });
        SqlHelper.outputResult(dataList);
        return ListUtil.unique(dataList, name);
    }
}
