package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentUserRelationBean;

import javax.persistence.*;

@Entity
@Table(name = "up_department_user_relation")
@Access(AccessType.FIELD)
public class UpDepartmentUserRelation extends BaseUpEntity<UpDepartmentUserRelationBean> {

    public UpDepartmentUserRelation() {
    }

    public UpDepartmentUserRelation(Integer id) {
        super(id);
    }

    @EntityProperty(isCopyOnUpdate = false)
    @JoinColumn(name = "department_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpDepartment department;

    @EntityProperty(isCopyOnUpdate = false)
    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser user;
//
//    @Column(name = "relation_type")
//    @Enumerated(EnumType.STRING)
//    private UpDepartmentUserRelationType relationType;

    @Column(name = "is_manager")
    private Boolean isManager;

    @Column(name = "sort_order")
    private Integer sortOrder;

    public UpDepartment getDepartment() {
        return department;
    }

    public void setDepartment(UpDepartment department) {
        this.department = department;
    }

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }

//    public UpDepartmentUserRelationType getRelationType() {
//        return relationType;
//    }
//
//    public void setRelationType(UpDepartmentUserRelationType relationType) {
//        this.relationType = relationType;
//    }

    public Boolean getIsManager() {
        return isManager;
    }

    public void setIsManager(Boolean isManager) {
        this.isManager = isManager;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}
