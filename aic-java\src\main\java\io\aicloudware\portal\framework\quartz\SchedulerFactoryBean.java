package io.aicloudware.portal.framework.quartz;

import io.aicloudware.portal.framework.utility.EncryptUtil;
import io.aicloudware.portal.framework.common.SystemConfigUtil;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Properties;

@Component
public class SchedulerFactoryBean extends org.springframework.scheduling.quartz.SchedulerFactoryBean {

    @Override
    public void setConfigLocation(Resource configLocation) {
        try {
            Properties properties = new Properties();
            PropertiesLoaderUtils.fillProperties(properties, configLocation);
            final File file = new File(SystemConfigUtil.getConfigPath() + "up_quartz.properties");
            if (file.exists()) {
                PropertiesLoaderUtils.fillProperties(properties, new FileSystemResource(file));
            }
            String password = properties.getProperty("org.quartz.dataSource.upQuartzDS.password");
            password = EncryptUtil.decryptWithRSA(password);
            properties.setProperty("org.quartz.dataSource.upQuartzDS.password", password);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            properties.store(os, "up_quartz.properties");
            configLocation = new ByteArrayResource(os.toByteArray()) {
                @Override
                public String getFilename() {
                    return file.getAbsolutePath();
                }
            };
        } catch (IOException e) {
            // ignore
        }
        super.setConfigLocation(configLocation);
    }
}
