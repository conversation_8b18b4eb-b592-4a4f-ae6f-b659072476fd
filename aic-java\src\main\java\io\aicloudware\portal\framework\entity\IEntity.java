package io.aicloudware.portal.framework.entity;

import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.bean.IDProvider;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import org.hibernate.criterion.DetachedCriteria;

import java.util.Date;
import java.util.Set;

public interface IEntity<B extends RecordBean> extends IDProvider {

    public void setId(Integer id);

    public String getName();

    public void setName(String name);

    public Integer getCreateBy();

    public void setCreateBy(Integer createBy);

    public Integer getUpdateBy();

    public void setUpdateBy(Integer updateBy);

    public Date getCreateTm();

    public void setCreateTm(Date createTm);

    public Date getUpdateTm();

    public void setUpdateTm(Date updateTm);

    public RecordStatus getStatus();

    public void setStatus(RecordStatus status);

    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<B> searchBean, Set<String> aliasSet);

}
