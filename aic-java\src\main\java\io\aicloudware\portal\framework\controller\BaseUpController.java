package io.aicloudware.portal.framework.controller;


import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.UpDataListBean;
import io.aicloudware.portal.framework.bean.UpSimpleOperateBean;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Controller
public abstract class BaseUpController<E extends IEntity<B>, B extends RecordBean, RL extends ResultListBean<B>>
        extends BaseEntityController<E, B, RL> {

    protected B[] doInsert(List<E> entityList) {
        return commonService.insert(getBeanType(), entityList);
    }

    protected B[] doUpdate(List<B> beanList) {
        return commonService.update(getEntityType(), getBeanType(), beanList);
    }

    protected Boolean doDelete(List<Integer> idList) {
        return commonService.delete(getEntityType(), idList);
    }

    private B[] addEntity(List<B> beanList) {
        List<E> entityList = new ArrayList<>(beanList.size());
        for (B b : beanList) {
            entityList.add(BeanCopyUtil.copy(b, getEntityType()));
        }
        return doInsert(entityList);
    }

    protected ResponseBean addEntity(B bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        B[] beanList = addEntity(Arrays.asList(bean));
        return ResponseBean.success(beanList[0]);
    }

    protected ResponseBean addEntity(UpDataListBean<B> bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        B[] beanList = addEntity(Arrays.asList(bean.getDataList()));
        bean.setDataList(beanList);
        return ResponseBean.success(bean);
    }

    private B[] updateEntity(List<B> beanList) {
        return doUpdate(beanList);
    }

    protected ResponseBean updateEntity(Integer id, B bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        bean.setId(id);
        B[] beanList = updateEntity(Arrays.asList(bean));
        return ResponseBean.success(beanList[0]);
    }

    protected ResponseBean updateEntity(UpDataListBean<B> bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        B[] beanList = updateEntity(Arrays.asList(bean.getDataList()));
        bean.setDataList(beanList);
        return ResponseBean.success(bean);
    }

    protected ResponseBean deleteEntity(UpSimpleOperateBean bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        List<Integer> idList = Arrays.asList(bean.getIdList());
        Boolean result = Boolean.FALSE;
        if (Utility.isNotEmpty(idList)) {
            result = doDelete(idList);
        }
        return ResponseBean.success(result);
    }

    protected ResponseBean deleteEntity(Integer id) {
        UpSimpleOperateBean bean = new UpSimpleOperateBean();
        bean.setIdList(new Integer[]{id});
        return deleteEntity(bean, null);
    }
}
