package io.aicloudware.portal.api_vcpp.service.profile;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_vcpp.entity.UpMessage;
import io.aicloudware.portal.framework.sdk.bean.profile.UpMessageBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpMessageStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;

@Service
@Transactional
public class UpMessageService extends BaseService implements IUpMessageService {

	@Override
	public UpMessageBean detail(Integer id, Integer userId) {
		AssertUtil.check(id, "请选择消息！");
		UpMessage entity = this.dao.load(UpMessage.class, id);
		AssertUtil.check(entity != null && entity.getUser().getId().equals(userId), "消息数据异常！");
		return BeanCopyUtil.copy(entity, UpMessageBean.class);
	}

	@Override
	public Integer countNewMsg(Integer userId) {
		List<Integer> count = this.queryDao.querySql("select count(1) from up_message where user_id = " + userId + " and message_status = '" + UpMessageStatus.unread + "' and status <> '" + RecordStatus.deleted + "'",
				null);
		if(count==null || count.size()==0) {
			return 0;
		}
		return Integer.parseInt(count.get(0) + "");
	}

	@Override
	public UpMessageBean updateToRead(Integer id, Integer userId) {
		AssertUtil.check(id, "请选择消息！");
		UpMessage entity = this.dao.load(UpMessage.class, id);
		AssertUtil.check(entity != null && entity.getUser().getId().equals(userId), "消息数据异常！");
		if (entity.getMessageStatus() == null || entity.getMessageStatus().equals(UpMessageStatus.unread)) {
			entity.setMessageStatus(UpMessageStatus.read);
			this.dao.update(entity, "messageStatus");
		}
		return BeanCopyUtil.copy(entity, UpMessageBean.class);
	}

}
