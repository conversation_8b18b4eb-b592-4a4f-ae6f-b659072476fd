package io.aicloudware.portal.api_vcpp.service.order;

import java.math.BigDecimal;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudDisk;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.entity.UpProductDiskSet;
import io.aicloudware.portal.api_vcpp.service.finance.IUpFinanceRechargeService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.entity.SpVmDisk;

@Service
@Transactional
public class UpOrderCloudDiskService extends BaseService implements IUpOrderCloudDiskService {

	@Autowired
	private IUpFinanceRechargeService financeRechargeService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Override
	public Integer save(UpOrderCloudDiskBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean.getDiskGB(), "请输入容量！");
		AssertUtil.check(bean.getName(), "请输入云盘名称！");
		AssertUtil.check(bean.getDiskType(), "请选择云盘类型！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getName()), "云盘名称只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getName()), "云盘名称必须包含字母！");
		AssertUtil.check(bean.getCloudServerId(), "请选择云服务器！");

		UpOrderQuotaDetail quotaDetail = null;
		if(bean.getQuotaDetailId() != null){
			quotaDetail = dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());
		}
		if(quotaDetail == null || quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
			AssertUtil.check(bean.getDiskConfigId(), "缺少云盘产品配置！");
		}
//		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		bean.setOwnerId(user.getId());
		UpOrderCloudDisk entity = BeanCopyUtil.copy(bean, UpOrderCloudDisk.class);
		if (entity.getType() == null) {
			entity.setType(SpVmDiskType.mount);
		}

		SpVm vm = this.dao.load(SpVm.class, bean.getCloudServerId());
		AssertUtil.check(vm, "未找到相关云服务器信息！");
		AssertUtil.check(vm.getSpOrg().getId().equals(user.getOrg().getId()), "云服务器所属用户组异常！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_cloud_disk, user.getId()) == 0, "您有未完成的云盘申请！");
		
		entity.setCloudServerId(bean.getCloudServerId());

		int count = 0;
		if (vm.getDiskList() != null && vm.getDiskList().size() > 0) {
	        for (SpVmDisk disk : vm.getDiskList()) {
	            if (disk.getDiskNumber() != null && disk.getDiskNumber() > count) {
	                count = disk.getDiskNumber();
	            }
	        }
            count++;
		}
		entity.setDiskNumber(count);
		entity.setName(entity.getName() + "-" + System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000)) + "-" + count);

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_cloud_disk);
		order.setName("[" + OrderType.new_cloud_disk + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setPaymentType(bean.getPaymentType());
		order.setSystemDiskNum(0);
		order.setDiskNum(entity.getDiskGB());

		if(quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
			UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
			AssertUtil.check(diskSet != null && diskSet.getEnabled() && diskSet.getDiskType().equals(bean.getDiskType()), "云盘配置信息异常！");
			AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= bean.getDiskGB(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
			AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= bean.getDiskGB(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
			order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(entity.getDiskGB())));
			order.setDiskSet(diskSet);
		}

		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
//		orderMQService.createOrderMQ(order, Lists.newArrayList(entity));
		
		quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}

}
