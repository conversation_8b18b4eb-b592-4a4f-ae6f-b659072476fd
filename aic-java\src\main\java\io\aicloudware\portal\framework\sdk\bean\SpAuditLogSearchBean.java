package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "审计日志查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpAuditLogSearchBean extends SearchBean<SpAuditLogBean> {
	//开始时间
	private String startTm;
	private String endTm;
	
	public String getStartTm() {
		return startTm;
	}
	public void setStartTm(String startTm) {
		this.startTm = startTm;
	}
	public String getEndTm() {
		return endTm;
	}
	public void setEndTm(String endTm) {
		this.endTm = endTm;
	}
	
	
}
