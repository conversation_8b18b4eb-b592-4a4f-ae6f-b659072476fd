package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpApplicationChangeLog;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationChangeLogBean;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationChangeLogResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/application_change_log")
@Api(value = "/application_change_log", description = "申请单变更日志", position = 613)
public class UpApplicationChangeLogController extends BaseUpController<UpApplicationChangeLog, UpApplicationChangeLogBean, UpApplicationChangeLogResultBean> {

//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApplicationChangeLogResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryApplicationChangeLog(@ApiParam(value = "查询条件") @RequestBody UpApplicationChangeLogSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApplicationChangeLogBean.class)})
//    @ResponseBody
//    public ResponseBean getOperationLog(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
}
