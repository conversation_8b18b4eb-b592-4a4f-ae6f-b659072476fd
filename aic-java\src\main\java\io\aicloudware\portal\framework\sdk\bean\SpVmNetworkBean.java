package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚拟机网络")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVmNetworkBean extends SpRecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "虚拟机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚拟机名称")
    private String vmName;

    @ApiModelProperty(value = "网络ID")
    private Integer networkId;

    @ApiModelProperty(value = "网络名称")
    private String networkName;

    @ApiModelProperty(value = "网络显示名称")
    private String networkDisplayName;

    @ApiModelProperty(value = "网络配置文件ID")
    private Integer networkProfileId;

    @ApiModelProperty(value = "网络配置文件名称")
    private String networkProfileName;

    @ApiModelProperty(value = "网络配置文件显示名称")
    private String networkProfileDisplayName;

    @ApiModelProperty(value = "网卡序号")
    private Integer nicNumber;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public Integer getNetworkId() {
        return networkId;
    }

    public void setNetworkId(Integer networkId) {
        this.networkId = networkId;
    }

    public String getNetworkName() {
        return networkName;
    }

    public void setNetworkName(String networkName) {
        this.networkName = networkName;
    }

    public Integer getNetworkProfileId() {
        return networkProfileId;
    }

    public void setNetworkProfileId(Integer networkProfileId) {
        this.networkProfileId = networkProfileId;
    }

    public String getNetworkProfileName() {
        return networkProfileName;
    }

    public void setNetworkProfileName(String networkProfileName) {
        this.networkProfileName = networkProfileName;
    }

    public Integer getNicNumber() {
        return nicNumber;
    }

    public void setNicNumber(Integer nicNumber) {
        this.nicNumber = nicNumber;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getNetworkDisplayName() {
        return networkDisplayName;
    }

    public void setNetworkDisplayName(String networkDisplayName) {
        this.networkDisplayName = networkDisplayName;
    }

    public String getNetworkProfileDisplayName() {
        return networkProfileDisplayName;
    }

    public void setNetworkProfileDisplayName(String networkProfileDisplayName) {
        this.networkProfileDisplayName = networkProfileDisplayName;
    }

}
