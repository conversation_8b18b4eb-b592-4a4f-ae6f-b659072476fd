package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order")
@Access(AccessType.FIELD)
public class UpOrder extends BaseUpEntity<UpOrderBean>{

	@Column(name = "payment_type")
	@Enumerated(EnumType.STRING)
	private PaymentType paymentType;

	@JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
	
	@JoinColumn(name = "sp_org_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
	
	@JoinColumn(name = "apply_user_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser applyUser;
	
	@Column(name = "number")
	private Integer number;
	
	@Column(name = "type", nullable = false)
	@Enumerated(EnumType.STRING)
	private OrderType type;
	
	@Column(name = "order_status", nullable = false)
	@Enumerated(EnumType.STRING)
	private OrderStatus orderStatus;
	
	@JoinColumn(name = "vm_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductVmSet vmSet;
	
	@JoinColumn(name = "redis_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductRedisSet redisSet;
	
	@Column(name = "cpu_num")
	private Integer cpuNum;
	
	@Column(name = "cpu_price")
	private BigDecimal cpuPrice;
	
	@Column(name = "memory_num")
	private Integer memoryNum;
	
	@Column(name = "memory_price")
	private BigDecimal memoryPrice;
	
	@JoinColumn(name = "disk_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductDiskSet diskSet;
	
	@Column(name = "disk_num")
	private Integer diskNum;
	
	@Column(name = "system_disk_num")
	private Integer systemDiskNum;
	
	@Column(name = "disk_price")
	private BigDecimal diskPrice;
	
	@JoinColumn(name = "bandwidth_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductBandwidthSet bandwidthSet;
	
	@Column(name = "bandwidth_num")
	private Integer bandwidthNum;
	
	@Column(name = "bandwidth_price")
	private BigDecimal bandwidthPrice;
	
	@JoinColumn(name = "load_balance_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductLoadBalanceSet loadBalanceSet;
	
	@Column(name = "load_balance_num")
	private Integer loadBalanceNum;
	
	@Column(name = "load_balance_price")
	private BigDecimal loadBalancePrice;

	@JoinColumn(name = "snapshot_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductSnapshotSet snapshotSet;
	
	@Column(name = "snapshot_num")
	private Integer snapshotNum;
	
	@Column(name = "snapshot_price")
	private BigDecimal snapshotPrice;
	
	@JoinColumn(name = "backup_set_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpProductBackupSet backupSet;
	
	@Column(name = "backup_num")
	private Integer backupNum;
	
	@Column(name = "backup_price")
	private BigDecimal backupPrice;
	
//	@JoinColumn(name = "org_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//	private SpOrg spOrg;
	
	@JoinColumn(name = "quota_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuota quota;
	
	@JoinColumn(name = "quota_detail_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuotaDetail quotaDetail;

	@JoinColumn(name = "service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan servicePlan;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public OrderType getType() {
		return type;
	}

	public void setType(OrderType type) {
		this.type = type;
	}

	public OrderStatus getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(OrderStatus orderStatus) {
		this.orderStatus = orderStatus;
	}

	public UpProductVmSet getVmSet() {
		return vmSet;
	}

	public void setVmSet(UpProductVmSet vmSet) {
		this.vmSet = vmSet;
	}

	public Integer getCpuNum() {
		return cpuNum;
	}

	public void setCpuNum(Integer cpuNum) {
		this.cpuNum = cpuNum;
	}

	public BigDecimal getCpuPrice() {
		return cpuPrice;
	}

	public void setCpuPrice(BigDecimal cpuPrice) {
		this.cpuPrice = cpuPrice;
	}

	public Integer getMemoryNum() {
		return memoryNum;
	}

	public void setMemoryNum(Integer memoryNum) {
		this.memoryNum = memoryNum;
	}

	public BigDecimal getMemoryPrice() {
		return memoryPrice;
	}

	public void setMemoryPrice(BigDecimal memoryPrice) {
		this.memoryPrice = memoryPrice;
	}

	public UpProductDiskSet getDiskSet() {
		return diskSet;
	}

	public void setDiskSet(UpProductDiskSet diskSet) {
		this.diskSet = diskSet;
	}

	public Integer getDiskNum() {
		return diskNum;
	}

	public void setDiskNum(Integer diskNum) {
		this.diskNum = diskNum;
	}

	public BigDecimal getDiskPrice() {
		return diskPrice;
	}

	public void setDiskPrice(BigDecimal diskPrice) {
		this.diskPrice = diskPrice;
	}

	public UpProductBandwidthSet getBandwidthSet() {
		return bandwidthSet;
	}

	public void setBandwidthSet(UpProductBandwidthSet bandwidthSet) {
		this.bandwidthSet = bandwidthSet;
	}

	public Integer getBandwidthNum() {
		return bandwidthNum;
	}

	public void setBandwidthNum(Integer bandwidthNum) {
		this.bandwidthNum = bandwidthNum;
	}

	public BigDecimal getBandwidthPrice() {
		return bandwidthPrice;
	}

	public void setBandwidthPrice(BigDecimal bandwidthPrice) {
		this.bandwidthPrice = bandwidthPrice;
	}

	public UpProductLoadBalanceSet getLoadBalanceSet() {
		return loadBalanceSet;
	}

	public void setLoadBalanceSet(UpProductLoadBalanceSet loadBalanceSet) {
		this.loadBalanceSet = loadBalanceSet;
	}

	public Integer getLoadBalanceNum() {
		return loadBalanceNum;
	}

	public void setLoadBalanceNum(Integer loadBalanceNum) {
		this.loadBalanceNum = loadBalanceNum;
	}

	public BigDecimal getLoadBalancePrice() {
		return loadBalancePrice;
	}

	public void setLoadBalancePrice(BigDecimal loadBalancePrice) {
		this.loadBalancePrice = loadBalancePrice;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public Integer getNumber() {
		return number;
	}

	public void setNumber(Integer number) {
		this.number = number;
	}

	public Integer getSystemDiskNum() {
		return systemDiskNum;
	}

	public void setSystemDiskNum(Integer systemDiskNum) {
		this.systemDiskNum = systemDiskNum;
	}

	public UpProductSnapshotSet getSnapshotSet() {
		return snapshotSet;
	}

	public void setSnapshotSet(UpProductSnapshotSet snapshotSet) {
		this.snapshotSet = snapshotSet;
	}

	public Integer getSnapshotNum() {
		return snapshotNum;
	}

	public void setSnapshotNum(Integer snapshotNum) {
		this.snapshotNum = snapshotNum;
	}

	public BigDecimal getSnapshotPrice() {
		return snapshotPrice;
	}

	public void setSnapshotPrice(BigDecimal snapshotPrice) {
		this.snapshotPrice = snapshotPrice;
	}

	public Integer getBackupNum() {
		return backupNum;
	}

	public void setBackupNum(Integer backupNum) {
		this.backupNum = backupNum;
	}

	public BigDecimal getBackupPrice() {
		return backupPrice;
	}

	public void setBackupPrice(BigDecimal backupPrice) {
		this.backupPrice = backupPrice;
	}

	public UpProductBackupSet getBackupSet() {
		return backupSet;
	}

	public void setBackupSet(UpProductBackupSet backupSet) {
		this.backupSet = backupSet;
	}

	public UpProductRedisSet getRedisSet() {
		return redisSet;
	}

	public void setRedisSet(UpProductRedisSet redisSet) {
		this.redisSet = redisSet;
	}

	public UpUser getApplyUser() {
		return applyUser;
	}

	public void setApplyUser(UpUser applyUser) {
		this.applyUser = applyUser;
	}

	public UpOrderQuota getQuota() {
		return quota;
	}

	public void setQuota(UpOrderQuota quota) {
		this.quota = quota;
	}

	public UpOrderQuotaDetail getQuotaDetail() {
		return quotaDetail;
	}

	public void setQuotaDetail(UpOrderQuotaDetail quotaDetail) {
		this.quotaDetail = quotaDetail;
	}

	public UpServicePlan getServicePlan() {
		return servicePlan;
	}

	public void setServicePlan(UpServicePlan servicePlan) {
		this.servicePlan = servicePlan;
	}
}
