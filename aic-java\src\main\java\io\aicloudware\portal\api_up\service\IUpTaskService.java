package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;

import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public interface IUpTaskService {

    public UpTaskBean redeploy(Integer taskId);

    public void runTask(String refreshJob);

    public UpTaskBean getRefreshTask(boolean isTenant);

	public String getStatus(Integer orgId, Integer orderId);

	public Map<Integer, String> getIncompletedTask(Integer orgId, OrderType orderType);

}
