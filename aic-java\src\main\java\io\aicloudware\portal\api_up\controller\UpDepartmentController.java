package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpDepartment;
import io.aicloudware.portal.api_up.service.IUpDepartmentService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentBean;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentUserRelationBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/department")
@Api(value = "/department", description = "部门管理", position = 520)
public class UpDepartmentController extends BaseUpController<UpDepartment, UpDepartmentBean, UpDepartmentResultBean> {

    @Autowired
    private IUpDepartmentService upDepartmentService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "查询部门列表", notes = "根据条件查询部门列表")
    public ResponseBean query(@ApiParam(value = "查询参数") @RequestBody UpDepartmentBean params) {
        UpDepartmentBean[] result = upDepartmentService.list(params);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取部门树", notes = "获取部门树形结构")
    public ResponseBean getDepartmentTree(@ApiParam(value = "父部门ID") @RequestParam(required = false) Integer parentId) {
        UpDepartmentBean[] result = upDepartmentService.getDepartmentTree(parentId);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "添加部门", notes = "添加新部门")
    public ResponseBean add(@ApiParam(value = "部门信息") @RequestBody UpDepartmentBean bean) {
        upDepartmentService.add(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "更新部门", notes = "更新部门信息")
    public ResponseBean update(@ApiParam(value = "部门信息") @RequestBody UpDepartmentBean bean) {
        upDepartmentService.update(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除部门", notes = "删除指定部门")
    public ResponseBean delete(@ApiParam(value = "部门ID") @PathVariable Integer id) {
        upDepartmentService.delete(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/addUsers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "添加用户到部门", notes = "将用户添加到指定部门")
    public ResponseBean addUsersToDepart(
            @ApiParam(value = "部门ID") @RequestParam Integer departmentId,
            @ApiParam(value = "用户ID列表") @RequestParam List<Integer> userIds
           ) {
        upDepartmentService.addUsersToDepart(departmentId, userIds);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/removeUsers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "从部门移除用户", notes = "从指定部门移除用户")
    public ResponseBean removeUsersFromDepartment(
            @ApiParam(value = "部门ID") @RequestParam Integer departmentId,
            @ApiParam(value = "用户ID列表") @RequestParam List<Integer> userIds) {
        upDepartmentService.removeUsersFromDepartment(departmentId, userIds);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/users/{departmentId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取部门用户", notes = "获取指定部门的用户列表")
    public ResponseBean getDepartmentUsers(@ApiParam(value = "部门ID") @PathVariable Integer departmentId) {
        UpDepartmentUserRelationBean[] result = upDepartmentService.getDepartmentUsers(departmentId);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/userDepartments/{userId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取用户部门", notes = "获取指定用户所属的部门列表")
    public ResponseBean getUserDepartments(@ApiParam(value = "用户ID") @PathVariable Integer userId) {
        UpDepartmentBean[] result = upDepartmentService.getUserDepartments(userId);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/setManager", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "设置部门管理员", notes = "设置或取消用户的部门管理员身份")
    public ResponseBean setDepartmentManager(
            @ApiParam(value = "部门ID") @RequestParam Integer departmentId,
            @ApiParam(value = "用户ID") @RequestParam Integer userId,
            @ApiParam(value = "是否为管理员") @RequestParam Boolean isManager) {
        upDepartmentService.setDepartmentManager(departmentId, userId, isManager);
        return ResponseBean.success(true);
    }
}