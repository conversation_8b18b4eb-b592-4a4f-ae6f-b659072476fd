package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpDepartmentBean;
import io.aicloudware.portal.framework.sdk.bean.UpDepartmentUserRelationBean;

import java.util.List;

public interface IUpDepartmentService {

    /**
     * 添加部门
     * @param bean 部门信息
     */
    void add(UpDepartmentBean bean);

    /**
     * 更新部门
     * @param bean 部门信息
     */
    void update(UpDepartmentBean bean);

    /**
     * 删除部门
     * @param id 部门ID
     */
    void delete(Integer id);

    /**
     * 获取部门列表
     * @param params 查询参数
     * @return 部门列表
     */
    UpDepartmentBean[] list(UpDepartmentBean params);

    /**
     * 获取部门树结构
     * @param parentId 父部门ID，null表示获取根部门
     * @return 部门树
     */
    UpDepartmentBean[] getDepartmentTree(Integer parentId);

    /**
     * 添加用户到部门
     * @param departmentId 部门ID
     * @param userIds 用户ID列表
     */
    void addUsersToDepart(Integer departmentId, List<Integer> userIds);

    /**
     * 从部门移除用户
     * @param departmentId 部门ID
     * @param userIds 用户ID列表
     */
    void removeUsersFromDepartment(Integer departmentId, List<Integer> userIds);

    /**
     * 获取部门用户关系列表
     * @param departmentId 部门ID
     * @return 用户关系列表
     */
    UpDepartmentUserRelationBean[] getDepartmentUsers(Integer departmentId);

    /**
     * 获取用户所属部门列表
     * @param userId 用户ID
     * @return 部门列表
     */
    UpDepartmentBean[] getUserDepartments(Integer userId);

    /**
     * 设置部门管理员
     * @param departmentId 部门ID
     * @param userId 用户ID
     * @param isManager 是否为管理员
     */
    void setDepartmentManager(Integer departmentId, Integer userId, Boolean isManager);
}
