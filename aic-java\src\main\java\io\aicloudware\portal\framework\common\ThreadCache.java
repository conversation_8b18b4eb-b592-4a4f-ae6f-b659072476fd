package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.api_up.entity.UpOperationLog;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpUserService;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class ThreadCache {
    private static final ThreadLocal<UpUser> userLocal = new ThreadLocal<>();
    public static final ThreadLocal<UpOperationLog> operationLogLocal = new ThreadLocal<>();
    public static final ThreadLocal<Integer[]> deployingThresholdCountLocal = new ThreadLocal<>();
    public static final Map<String, Integer> orgIdMap = new HashMap<>();
    public static final Set<String> ipInProcess = new HashSet<>();

    @Autowired
	private IUpUserService upUserService;

    @Autowired
    private ISpRegionService spRegionService;

	private static ThreadCache threadCache;

	@PostConstruct
	public void init() {
		threadCache = this;
		threadCache.upUserService = this.upUserService;
        threadCache.spRegionService = this.spRegionService;
	}
	
	public static boolean checkAddIPInProcess(String ip) {
		synchronized(ipInProcess) {
			if (ipInProcess.contains(ip)) {
				return false;
			}
			ipInProcess.add(ip);
			return true;
		}
	}
	
	public static boolean releaseIPInProcess(String ip) {
		synchronized(ipInProcess) {
			ipInProcess.remove(ip);
			return true;
		}
	}
	
    public static void setLocalUser(String userToken) {
        if (Utility.isNotEmpty(userToken)) {
            String[] values = userToken.split(":");
        	final long expireTime = Long.parseLong(values[5]) + 24 * 60 * 60 * 1000;
            AssertUtil.check(expireTime >= System.currentTimeMillis(), "找不到客户端认证信息(Header: Authorization)");
            UpUser user = new UpUser(Integer.valueOf(values[0]));
            user.setSystemAdmin(Boolean.valueOf(values[1]));
            user.setTenantAdmin(Boolean.valueOf(values[2]));
        	if(StringUtils.isNotEmpty(values[3])) {
				user.setOrg(new SpOrg(Integer.valueOf(values[3])));
			}else {
				Integer orgId = threadCache.upUserService.getUserOrg(user.getId());
				AssertUtil.check(orgId, "您的云服务环境正在初始化，这个过程需要2~3分钟，请稍后再试");
				user.setOrg(new SpOrg(orgId));
			}
        	if(StringUtils.isNotEmpty(values[4])) {
        		user.setRegion(threadCache.spRegionService.getRegionByName(values[4]));
        	}
        	user.setTimeMillis(values[5]);
            userLocal.set(user);
        }
    }
    
    public static Integer getOrgId() {
    	Integer orgId = null;
		if (getUser() != null && getUser().getOrg() != null) {
			orgId = getUser().getOrg().getId();
		}
		if (orgId == null) {
            return 1;
        }
		return orgId;
    }
    
    public static Integer getSysOrgId() {
    	if (orgIdMap.containsKey("system")) {
    		return orgIdMap.get("system");
    	}
    	throw new RuntimeException("您的云服务环境正在初始化，这个过程需要2~3分钟，请稍后再试");
    }

    public static void clear() {
        userLocal.remove();
        operationLogLocal.remove();
    }

    public static boolean isSystemAdminLogin() {
        return Utility.isNotZero(getUserId()) && getUserId() == 1;
    }

    // TODO 缺少reqgion
    public static void setSystemAdminLogin() {
        setLocalUser("1:true:true:1::" + System.currentTimeMillis());
    }
    
    public static void setUserLogin(Integer userId, SpRegionEntity region) {
        setLocalUser(userId + ":false:false::"+region+":" + System.currentTimeMillis());
    }

    public static Integer getUserId() {
        UpUser user = getUser();
        return user == null ? Utility.ZERO : user.getId();
    }

    public static UpUser getUser() {
        return userLocal.get();
    }
    
    public static SpRegionEntity getRegion() {
    	UpUser user = getUser();
    	return user == null ? null : user.getRegion();
    }

    public static <E extends IEntity> void initOperationLogLocal(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                                                 Integer targetId, String targetName) {
        if (null != ThreadCache.operationLogLocal.get()) {
            ThreadCache.operationLogLocal.get().setOperationType(operationType);
            ThreadCache.operationLogLocal.get().setName(tableName);
            ThreadCache.operationLogLocal.get().setOwner(null == user ? ThreadCache.getUser() : user);
            ThreadCache.operationLogLocal.get().setTargetTable(null == targetTable ? null : targetTable.getSimpleName());
            ThreadCache.operationLogLocal.get().setTargetId(targetId);
            ThreadCache.operationLogLocal.get().setTargetName(targetName);
        }
    }

	public synchronized static void setOrgList(Map<String, Integer> map) {
		orgIdMap.clear();
		orgIdMap.putAll(map);
	}
}
