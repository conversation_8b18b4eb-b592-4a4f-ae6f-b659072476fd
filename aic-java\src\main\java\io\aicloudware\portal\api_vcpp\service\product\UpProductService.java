package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.product.*;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.CloudType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.*;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.plateform_ecloud.operation.EcloudEbsOperation;
import io.aicloudware.portal.plateform_ecloud.operation.EcloudEcsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEcsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEvsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudIamOperation;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpProject;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional

public class UpProductService extends BaseService implements IUpProductService {

	@Autowired
	private ISpRegionService spRegionService;

	@Override
	public void sync(Integer orgId, SpRegionEntity region) {
		syncVmSet(orgId, region);
		syncCceSet(orgId, region);
		syncDiskSet(orgId, region);
		syncBandwidthSet(orgId, region);
	}

	private void syncVmSet(Integer orgId, SpRegionEntity region) {
		SpOrg org = dao.load(SpOrg.class, orgId);
		List<UpProductVmSet> serverDataList = null;
		if (CloudType.ecloud.equals(region.getType())) {
			serverDataList = EcloudEcsOperation.getProductVmSetList(EcloudEcsOperation.createClient(org.getUsername(), org.getPassword(), region.getCode()),
					region);
		} else {
			List<SpProject> projectList = JointEcloudIamOperation.getInstance(org.getUsername(), org.getPassword(), region).listProject(new ArrayList<>(spRegionService.getRegionMap().values()));

			final List<UpProductVmSet> specs = new ArrayList<>();;
			projectList.forEach( project -> {
				if (region.equals(project.getRegion())) {
					specs.addAll(JointEcloudEcsOperation.getInstance(org.getUsername(), org.getPassword(), project.getRegion()).ListSpec(project));
				}
			});
			serverDataList = specs;
		}
		List<UpProductVmSet> tableDataList = queryDao.list(UpProductVmSet.class, MapUtil.of("status", RecordStatus.active, "type", ProductVmSetType.vm, "region", region));
		List<UpProductVmSet> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1);
		List<UpProductVmSet> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1);
		List<UpProductVmSet> dataToUpdate = ListUtil.getIntersection(tableDataList, serverDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1,
				(o1, o2) -> {
									o1.setType(o2.getType());
									o1.setEnabled(o2.getEnabled());
									o1.setServerType(o2.getServerType());
									});
		dataToAdd.forEach( o -> {
			dao.insert(o);
		} );
		dataToDel.forEach( o -> {
			dao.delete(UpProductVmSet.class, o.getId());
		} );
		dataToUpdate.forEach( o -> {
			dao.update(o);
		} );
	}

	private void syncCceSet(Integer orgId, SpRegionEntity region) {
		SpOrg org = dao.load(SpOrg.class, orgId);
		List<UpProductCceSet> serverDataList = null;
		if (CloudType.ecloud.equals(region.getType())) {
			throw new RuntimeException("暂不支持");
		} else {
			List<SpProject> projectList = JointEcloudIamOperation.getInstance(org.getUsername(), org.getPassword(), region).listProject(new ArrayList<>(spRegionService.getRegionMap().values()));

			final List<UpProductCceSet> specs = new ArrayList<>();;
			projectList.forEach( project -> {
				if (region.equals(project.getRegion())) {
					specs.addAll(JointEcloudEcsOperation.getInstance(org.getUsername(), org.getPassword(), project.getRegion()).ListSpecForK8s(project));
				}
			});
			serverDataList = specs;
		}
		List<UpProductCceSet> tableDataList = queryDao.list(UpProductCceSet.class, MapUtil.of("status", RecordStatus.active, "region", region));
		List<UpProductCceSet> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1);
		List<UpProductCceSet> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1);
		List<UpProductCceSet> dataToUpdate = ListUtil.getIntersection(tableDataList, serverDataList, (o1, o2) -> o1.getInfo().equals(o2.getInfo()) ? 0 : 1,
				(o1, o2) -> {
					o1.setServerType(o2.getServerType());
					o1.setCpuUnit(o2.getCpuUnit());
					o1.setMemoryUnit(o2.getMemoryUnit());
				});
		dataToAdd.forEach( o -> {
			dao.insert(o);
		} );
		dataToDel.forEach( o -> {
			dao.delete(UpProductVmSet.class, o.getId());
		} );
		dataToUpdate.forEach( o -> {
			dao.update(o);
		} );
	}

	private void syncDiskSet(Integer orgId, SpRegionEntity region) {
		SpOrg org = dao.load(SpOrg.class, orgId);
		List<UpProductDiskSet> serverDataList = null;
		if (CloudType.ecloud.equals(region.getType())) {
			serverDataList = EcloudEbsOperation.getProductDiskSetList(EcloudEbsOperation.createClient(org.getUsername(), org.getPassword(), region.getCode()), region);
		} else {
			List<SpProject> projectList = JointEcloudIamOperation.getInstance(org.getUsername(), org.getPassword(), region).listProject(new ArrayList<>(spRegionService.getRegionMap().values()));
			final List<UpProductDiskSet> specs = new ArrayList<>();;
			projectList.forEach( project -> {
				if (region.equals(project.getRegion())) {
					specs.addAll(JointEcloudEvsOperation.getInstance(org.getUsername(), org.getPassword(), project.getRegion()).ListSpec(project));
				}
				});
			serverDataList = specs;
		}
		List<UpProductDiskSet> tableDataList = queryDao.list(UpProductDiskSet.class, MapUtil.of("status", RecordStatus.active, "type", ProductDiskSetType.vm_disk));
		List<UpProductDiskSet> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, (o1, o2) -> o1.getProductCode().equals(o2.getProductCode()) ? 0 : 1);
		List<UpProductDiskSet> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, (o1, o2) -> o1.getProductCode().equals(o2.getProductCode()) ? 0 : 1);
		dataToAdd.forEach( o -> {
			dao.insert(o);
		} );
		dataToDel.forEach( o -> {
			dao.delete(UpProductDiskSet.class, o.getId());
		} );
	}

	private void syncBandwidthSet(Integer orgId, SpRegionEntity region) {
		SpOrg org = dao.load(SpOrg.class, orgId);
		List<UpProductBandwidthSet>	serverDataList = new ArrayList<>();
		UpProductBandwidthSet bandwidthSet = new UpProductBandwidthSet();
		bandwidthSet.setEnabled(true);
		bandwidthSet.setType(ProductBandwidthSetType.bandwidth);
		bandwidthSet.setProductCode("bandwidth_1M");
		bandwidthSet.setUnit(1);
		bandwidthSet.setPrice(BigDecimal.valueOf(1));
		bandwidthSet.setName("bandwidth_1M");
		serverDataList.add(bandwidthSet);
		List<UpProductBandwidthSet> tableDataList = queryDao.list(UpProductBandwidthSet.class, MapUtil.of("status", RecordStatus.active, "type", ProductBandwidthSetType.bandwidth));
		List<UpProductBandwidthSet> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, (o1, o2) -> o1.getProductCode().equals(o2.getProductCode()) ? 0 : 1);
		List<UpProductBandwidthSet> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, (o1, o2) -> o1.getProductCode().equals(o2.getProductCode()) ? 0 : 1);
		dataToAdd.forEach( o -> {
			dao.insert(o);
		} );
		dataToDel.forEach( o -> {
			dao.delete(UpProductBandwidthSet.class, o.getId());
		} );
	}

	@Override
	public UpProductVmSet getVmSetByCode(ProductVmSetType type, String productCode) {
		List<UpProductVmSet> productVmSetList = dao.list(UpProductVmSet.class, MapUtil.of("enabled", true, "productCode", productCode, "type", type));
		AssertUtil.check(!productVmSetList.isEmpty(), "未找到虚拟机配置，请联系管理员");
		return productVmSetList.get(0);
	}

	@Override
	public List<UpProductVmSetBean> queryVmSetList(ProductVmSetType type) {
		UpProductVmSet entity = new UpProductVmSet();
		entity.setEnabled(true);
		entity.setType(type);
		UpProductVmSetSearchBean bean = new UpProductVmSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		bean.setOrderBy1(true);
		bean.setOrderName1("id");
		List<UpProductVmSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到虚拟机配置，请联系管理员");
		return Arrays.asList(BeanCopyUtil.copy2BeanList(entitys, UpProductVmSetBean.class));
	}
	
	@Override
	public Map<ServerType,List<UpProductVmSetBean>> queryVmSet(ProductVmSetType type, SpRegionEntity region) {
		UpProductVmSet entity = new UpProductVmSet();
		entity.setEnabled(true);
		entity.setType(type);
		entity.setRegion(region);
		UpProductVmSetSearchBean bean = new UpProductVmSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		bean.setOrderBy1(true);
		bean.setOrderName1("id");
		List<UpProductVmSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到虚拟机配置，请联系管理员");
		return ListUtil.map(entitys, (vmSetMap, vmSet) -> {
			if(vmSetMap.containsKey(vmSet.getServerType())){
				((List<UpProductVmSetBean>)vmSetMap.get(vmSet.getServerType())).add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
			}else {
				List<UpProductVmSetBean> list = new ArrayList<>();
				list.add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
				vmSetMap.put(vmSet.getServerType(), list);
			}
		});
	}
	
	@Override
	public Map<ServerType,List<UpProductVmSetBean>> queryVmSet(ProductVmSetType type, String productCode) {
		UpOrderQuotaDetail detail = dao.load(UpOrderQuotaDetail.class, Integer.valueOf(productCode));
		if(detail.getIsCustom() != null && detail.getIsCustom()){
			Map<ServerType,List<UpProductVmSetBean>> map = new HashMap<>();
			UpProductVmSetBean bean = new UpProductVmSetBean();
			bean.setCpuUnit(detail.getCpu());
			bean.setMemoryUnit(detail.getMemoryG());
			bean.setDiskUnit(detail.getDiskG());
			map.put(ServerType.high_performance, Collections.singletonList(bean));
			return map;
		}else{
			UpProductVmSet entity = new UpProductVmSet();
			entity.setEnabled(true);
			if(type != null) {
				entity.setType(type);
			}
			entity.setProductCode(detail.getProductCode());
			UpProductVmSetSearchBean bean = new UpProductVmSetSearchBean();
			bean.setPageSize(Integer.MAX_VALUE);
			bean.setOrderBy1(true);
			bean.setOrderName1("id");
			List<UpProductVmSet> entitys = this.dao.query(bean, entity);
			AssertUtil.check(entitys != null && entitys.size() > 0, "未找到虚拟机配置，请联系管理员");
			return ListUtil.map(entitys, (vmSetMap, vmSet) -> {
				if(vmSetMap.containsKey(vmSet.getServerType())){
					((List<UpProductVmSetBean>)vmSetMap.get(vmSet.getServerType())).add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
				}else {
					List<UpProductVmSetBean> list = new ArrayList<>();
					list.add(BeanCopyUtil.copy(vmSet, UpProductVmSetBean.class));
					vmSetMap.put(vmSet.getServerType(), list);
				}
			});
		}
	}

	@Override
	public UpProductVmSetBean getVmSetByCode(String productCode) {
		List<UpProductVmSet> entitys = this.dao.list(UpProductVmSet.class, MapUtil.of("productCode", productCode, "enabled", true));
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到虚拟机配置，请联系管理员");
		return BeanCopyUtil.copy(entitys.get(0),UpProductVmSetBean.class);
	}
	
	@Override
	public Map<DiskType, UpProductDiskSetBean> mapDiskSet(ProductDiskSetType type) {
		UpProductDiskSet params = new UpProductDiskSet();
		params.setEnabled(true);
		params.setType(type);
		UpProductDiskSetSearchBean bean = new UpProductDiskSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductDiskSet> entitys = this.dao.query(bean, params);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到云盘配置，请联系管理员");
		return ListUtil.map(entitys, (diskSetMap, diskSet) -> {
			if(diskSetMap.containsKey(diskSet.getDiskType())){
				UpProductDiskSetBean diskSetBean = (UpProductDiskSetBean) diskSetMap.get(diskSet.getDiskType());
				if (diskSetBean.getPrice().divide(BigDecimal.valueOf(diskSetBean.getUnit())).compareTo(diskSet.getPrice().divide(BigDecimal.valueOf(diskSet.getUnit()))) < 0) {
					diskSetMap.put(diskSet.getDiskType(), BeanCopyUtil.copy(diskSet, UpProductDiskSetBean.class));
				}
			}else {
				diskSetMap.put(diskSet.getDiskType(), BeanCopyUtil.copy(diskSet, UpProductDiskSetBean.class));
			}
		});
	}
	
	@Override
	public List<UpProductDiskSetBean> listDiskSet(ProductDiskSetType type) {
		UpProductDiskSet params = new UpProductDiskSet();
		params.setEnabled(true);
		params.setType(type);
		UpProductDiskSetSearchBean bean = new UpProductDiskSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductDiskSet> entitys = this.dao.query(bean, params);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到云盘配置，请联系管理员");
		return Arrays.asList(BeanCopyUtil.copy2BeanList(entitys, UpProductDiskSetBean.class));
	}
	
	@Override
	public Map<DiskType, UpProductDiskSetBean> mapDiskSet(ProductDiskSetType type,String productCode) {
		UpOrderQuotaDetail detail = dao.load(UpOrderQuotaDetail.class, Integer.valueOf(productCode));
		if(detail.getIsCustom() != null && detail.getIsCustom()){
			Map<DiskType, UpProductDiskSetBean> map = new HashMap<>();
			UpProductDiskSetBean bean = new UpProductDiskSetBean();
			bean.setUnit(detail.getDiskG());
			bean.setDiskType(detail.getDiskType() == null ? null : Integer.valueOf(0).equals(detail.getDiskType()) ? DiskType.hdd : DiskType.ssd);
			map.put(bean.getDiskType(), bean);
			return map;
		}else{
			UpProductDiskSet params = new UpProductDiskSet();
			params.setEnabled(true);
			params.setType(type);
			params.setProductCode(detail.getProductCode());
			UpProductDiskSetSearchBean bean = new UpProductDiskSetSearchBean();
			bean.setPageSize(Integer.MAX_VALUE);
			List<UpProductDiskSet> entitys = this.dao.query(bean, params);
			AssertUtil.check(entitys != null && entitys.size() > 0, "未找到云盘配置，请联系管理员");
			return ListUtil.map(entitys, (diskSetMap, diskSet) -> {
				if(diskSetMap.containsKey(diskSet.getDiskType())){
					UpProductDiskSetBean diskSetBean = (UpProductDiskSetBean) diskSetMap.get(diskSet.getDiskType());
					if (diskSetBean.getPrice().divide(BigDecimal.valueOf(diskSetBean.getUnit())).compareTo(diskSet.getPrice().divide(BigDecimal.valueOf(diskSet.getUnit()))) < 0) {
						diskSetMap.put(diskSet.getDiskType(), BeanCopyUtil.copy(diskSet, UpProductDiskSetBean.class));
					}
				}else {
					diskSetMap.put(diskSet.getDiskType(), BeanCopyUtil.copy(diskSet, UpProductDiskSetBean.class));
				}
			});
		}
	}
	
	@Override
	public UpProductDiskSetBean getDiskSet(ProductDiskSetType type,String productCode) {
		List<UpProductDiskSet> entitys = this.dao.list(UpProductDiskSet.class,MapUtil.of("type", type, "productCode", productCode));
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到云盘配置，请联系管理员");
		return BeanCopyUtil.copy(entitys.get(0), UpProductDiskSetBean.class);
	}
	
	@Override
	public UpProductDiskSetBean getDiskSet(String productCode) {
		List<UpProductDiskSet> entitys = this.dao.list(UpProductDiskSet.class, "productCode", productCode);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到云盘配置，请联系管理员");
		return BeanCopyUtil.copy(entitys.get(0), UpProductDiskSetBean.class);
	}

	@Override
	public UpProductLoadBalanceSetBean getLoadBalanceSetByOrg(Integer orgId) {
		UpProductLoadBalanceSet entity = new UpProductLoadBalanceSet();
		entity.setEnabled(true);
		UpProductLoadBalanceSetSearchBean bean = new UpProductLoadBalanceSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductLoadBalanceSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到负载均衡配置，请联系管理员");
		entity = null;
		for (UpProductLoadBalanceSet item : entitys) {
			if (entity == null) {
				entity = item;
				continue;
			}
			if (entity.getPrice().divide(BigDecimal.valueOf(entity.getUnit())).compareTo(item.getPrice().divide(BigDecimal.valueOf(item.getUnit()))) < 0) {
				entity = item;
			}
		}
		return BeanCopyUtil.copy(entity, UpProductLoadBalanceSetBean.class);
	}

	@Override
	public UpProductBandwidthSetBean getBandwidthSetByOrg(Integer orgId) {
		UpProductBandwidthSet entity = new UpProductBandwidthSet();
		entity.setEnabled(true);
		entity.setType(ProductBandwidthSetType.bandwidth);
		UpProductBandwidthSetSearchBean bean = new UpProductBandwidthSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductBandwidthSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到带宽配置，请联系管理员");
		entity = null;
//		for (UpProductBandwidthSet item : entitys) {
//			if (entity == null) {
//				entity = item;
//				continue;
//			}
//			if (entity.getPrice().divide(BigDecimal.valueOf(entity.getUnit())).compareTo(item.getPrice().divide(BigDecimal.valueOf(item.getUnit()))) < 0) {
//				entity = item;
//			}
//		}
		entity = entitys.get(0);
		return BeanCopyUtil.copy(entity, UpProductBandwidthSetBean.class);
	}
	
	@Override
	public UpProductBandwidthSetBean getBandwidthSet(String code) {
		UpProductBandwidthSet entity = new UpProductBandwidthSet();
		entity.setEnabled(true);
		entity.setProductCode(code);
		entity.setType(ProductBandwidthSetType.bandwidth);
		UpProductBandwidthSetSearchBean bean = new UpProductBandwidthSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductBandwidthSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到带宽配置，请联系管理员");
		UpProductBandwidthSetBean set = BeanCopyUtil.copy(entitys.get(0), UpProductBandwidthSetBean.class);
		
		Integer[] number = new Integer[] {1,2,3,4,5,7,8,9,10,15,20,25,30,40,50,60,70,80,90,100,150,200,300,400,500,600,700,800,900,1000};
		set.setNumber(number);
		
		return set;
	}

	@Override
	public UpProductSnapshotSetBean getSnapshotByOrg(Integer orgId) {
		UpProductSnapshotSet entity = new UpProductSnapshotSet();
		entity.setEnabled(true);
		UpProductSnapshotSetSearchBean bean = new UpProductSnapshotSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductSnapshotSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到快照配置，请联系管理员");
		entity = null;
		for (UpProductSnapshotSet item : entitys) {
			if (entity == null) {
				entity = item;
				continue;
			}
			if (entity.getPrice().divide(BigDecimal.valueOf(entity.getUnit())).compareTo(item.getPrice().divide(BigDecimal.valueOf(item.getUnit()))) < 0) {
				entity = item;
			}
		}
		return BeanCopyUtil.copy(entity, UpProductSnapshotSetBean.class);
	}

	@Override
	public UpProductBackupSetBean getBackupByOrg(Integer orgId) {
		UpProductBackupSet entity = new UpProductBackupSet();
		entity.setEnabled(true);
		UpProductBackupSetSearchBean bean = new UpProductBackupSetSearchBean();
		bean.setPageSize(Integer.MAX_VALUE);
		List<UpProductBackupSet> entitys = this.dao.query(bean, entity);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到备份配置，请联系管理员");
		entity = null;
		for (UpProductBackupSet item : entitys) {
			if (entity == null) {
				entity = item;
				continue;
			}
			if (entity.getPrice().divide(BigDecimal.valueOf(entity.getUnit())).compareTo(item.getPrice().divide(BigDecimal.valueOf(item.getUnit()))) < 0) {
				entity = item;
			}
		}
		return BeanCopyUtil.copy(entity, UpProductBackupSetBean.class);
	}

	@Override
	public UpProductBackupSetBean[] getBackupSet(String productCode) {
		Map<String,Object> map = MapUtil.of("enabled",true);
		if(productCode != null) {
			map.put("productCode", productCode);
		}
		List<UpProductBackupSet> entitys = this.dao.list(UpProductBackupSet.class, map);
		AssertUtil.check(entitys != null && entitys.size() > 0, "未找到备份配置，请联系管理员");
		return BeanCopyUtil.copy2BeanList(entitys, UpProductBackupSetBean.class);
	}

	@Override
	public List<UpProductCceSet> queryCceSetByOrg(Integer orgId) {
		List<UpProductCceSet> list = dao.list(UpProductCceSet.class, MapUtil.of("region", ThreadCache.getRegion(), "status", RecordStatus.active));
		return list.stream().filter(x -> x.getServerType() != null).collect(Collectors.toList());
	}

}
