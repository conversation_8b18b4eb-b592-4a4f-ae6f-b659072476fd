package io.aicloudware.portal.api_up.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import io.aicloudware.portal.api_up.entity.UpSystemConfig;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.executor.IExecutorAA;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ImageUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;

@Service
@Transactional
public class UpSystemConfigService extends BaseService implements IUpSystemConfigService {

    private static final Map<Integer, UpSystemConfigBean> SystemConfigCache = new ConcurrentHashMap<>();

//    @Value("#{'${logo_list}'.split(',')}")
//    private List<String> logoList;

    @Value("${logo_path:/opt/hcm/logo/}")
    private String logoPath;

    private void initCache() {
        if (SystemConfigCache.isEmpty()) {
            synchronized (getClass()) {
                if (SystemConfigCache.isEmpty()) {
                    for (UpSystemConfig systemConfig : dao.list(UpSystemConfig.class)) {
                        UpSystemConfigBean bean = BeanCopyUtil.copy2Bean(systemConfig, UpSystemConfigBean.class);
                        SystemConfigCache.put(systemConfig.getId(), bean);
                    }
                }
            }
        }
    }

    private void handle(UpSystemConfigListBean bean, IExecutorAA<UpSystemConfigBean, UpSystemConfigBean> executor) {
        initCache();
        for (UpSystemConfigBean systemConfigBean : bean.getDataList()) {
            UpSystemConfigBean dbBean = null;
            if (Utility.isNotZero(systemConfigBean.getId())) {
                UpSystemConfigBean cache = SystemConfigCache.get(systemConfigBean.getId());
                if (Utility.equals(systemConfigBean.getKey(), cache.getKey())
                        && cache.getKey().isSystemOrTenantLevel()
                        && Utility.equals(systemConfigBean.getName(), cache.getName())) {
                    dbBean = cache;
                }
            }
            if (dbBean == null) {
                for (UpSystemConfigBean cache : SystemConfigCache.values()) {
                    if (Utility.equals(systemConfigBean.getKey(), cache.getKey())
                            && (cache.getKey().isSystemOrTenantLevel())
                            && Utility.equals(systemConfigBean.getName(), cache.getName())) {
                        dbBean = cache;
                        break;
                    }
                }
            }
            executor.doExecute(dbBean, systemConfigBean);
        }
    }

    @Override
    public UpSystemConfigBean get(UpSystemConfigKey key) {
        UpSystemConfigBean bean = new UpSystemConfigBean();
        bean.setKey(key);
        bean.setName(key.name());
        UpSystemConfigListBean listBean = new UpSystemConfigListBean();
        listBean.setDataList(new UpSystemConfigBean[]{bean});
        query(listBean);
        if (Utility.isEmpty(bean.getValue())) {
            bean.setValue(key.getDefaultValue());
        }
        return bean;
    }

    @Override
    public UpSystemConfigListBean query(UpSystemConfigListBean bean) {
        handle(bean, (dbBean, reqBean) -> {
            if (dbBean != null) {
                reqBean.setId(dbBean.getId());
                reqBean.setValue(dbBean.getValue());
            }
            if (Utility.isEmpty(reqBean.getValue())) {
                reqBean.setValue(reqBean.getKey().getDefaultValue());
            }
        });
        return bean;
    }

    @Override
    public UpSystemConfigListBean save(UpSystemConfigListBean bean) {
        handle(bean, (dbBean, reqBean) -> {
            AssertUtil.check(!reqBean.getKey().isSystemOrTenantLevel() || ThreadCache.getUser() == null || ThreadCache.getUser().getSystemAdmin(), "全局参数必须由系统管理员设置");
            if (dbBean != null) {
                UpSystemConfig systemConfig = dao.load(UpSystemConfig.class, dbBean.getId());
                systemConfig.setValue(reqBean.getValue());
                if (UpSystemConfigKey.log_level.toString().equals(systemConfig.getKey().toString())) {
                    Logger.setLevel(Level.valueOf(systemConfig.getValue()));
                }
                if (Utility.isNotEmpty(reqBean.getValue()) && Utility.notEquals(reqBean.getValue(), reqBean.getKey().getDefaultValue())) {
                    dao.update(systemConfig, "value");
                } else {
                    dao.delete(UpSystemConfig.class, systemConfig.getId());
                }
                reqBean.setId(systemConfig.getId());
                dbBean.setValue(systemConfig.getValue());
            } else if (Utility.isNotEmpty(reqBean.getValue()) && Utility.notEquals(reqBean.getValue(), reqBean.getKey().getDefaultValue())) {
                UpSystemConfig systemConfig = dao.insert(BeanCopyUtil.copy(reqBean, UpSystemConfig.class));
                if (UpSystemConfigKey.log_level.toString().equals(systemConfig.getKey().toString())) {
                    Logger.setLevel(Level.valueOf(systemConfig.getValue()));
                }
                reqBean.setId(systemConfig.getId());
                reqBean = BeanCopyUtil.copy2Bean(systemConfig, UpSystemConfigBean.class);
                SystemConfigCache.put(systemConfig.getId(), reqBean);
            }
        });
        return bean;
    }

    @Override
    public void clearCache() {
        SystemConfigCache.clear();
    }
    
    @Override
    public void refreshConfig(UpSystemConfigKey key) {
    	List<UpSystemConfig> list = this.dao.list(UpSystemConfig.class,"key",key);
    	UpSystemConfig entity = null;
    	if(list!=null && !list.isEmpty()) {
    		entity = list.get(0);
    	}else {
    		entity = new UpSystemConfig();
    		entity.setKey(key);
    		entity.setName(key.name());
    		entity.setValue("");
    		this.dao.insert(entity);
    	}
    	if(SystemConfigCache.containsKey(entity.getId())) {
    		SystemConfigCache.get(entity.getId()).setValue(entity.getValue());
		}else {
			SystemConfigCache.put(entity.getId(), BeanCopyUtil.copy2Bean(entity, UpSystemConfigBean.class));
		}
    }

    @Override
    public List<String> logoList() {
//        if(logoList.isEmpty()){
//            return List.of("default");
//        }
//        return logoList;
        return null;
    }

    @Override
    public String uploadFullLogo(MultipartFile file) throws Exception {
        return uploadLogo(file, "full-logo.png");
    }

    @Override
    public String uploadMiniLogo(MultipartFile file) throws Exception {
        return uploadLogo(file, "mini-logo.png");
    }

    /**
     * 通用的LOGO上传处理方法
     *
     * @param file 上传的文件
     * @param fileName 保存的文件名
     * @return 上传结果消息
     * @throws Exception 上传过程中的异常
     */
    private String uploadLogo(MultipartFile file, String fileName) throws Exception {
        // 验证文件是否为空
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 验证文件大小（限制为10MB）
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (!ImageUtil.isValidFileSize(file, maxSize)) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }

        // 验证文件是否为支持的图片格式
        if (!ImageUtil.isValidImageFile(file)) {
            throw new IllegalArgumentException("不支持的图片格式，请上传JPG、PNG、GIF、BMP或WebP格式的图片");
        }

        // 确保目录存在
        File logoDir = new File(logoPath);
        if (!logoDir.exists()) {
            if (!logoDir.mkdirs()) {
                throw new IOException("无法创建LOGO存储目录: " + logoPath);
            }
        }

        // 将图片转换为PNG格式
        byte[] pngBytes = ImageUtil.convertToPng(file);

        // 保存文件
        File targetFile = new File(logoDir, fileName);
        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
            fos.write(pngBytes);
            fos.flush();
        }

        return "LOGO上传成功，文件保存为: " + fileName;
    }
}
