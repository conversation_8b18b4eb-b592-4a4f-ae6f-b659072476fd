package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.IPassword;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.CloudType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "组织")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOrgBean.class})
public class SpOrgBean extends RecordBean implements IPassword, IDisplayName {

    @ApiModelProperty(value = "服务器连接ID")
    private Integer serverConnectionId;

    @ApiModelProperty(value = "服务器连接名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String serverConnectionName;

    @ApiModelProperty(value = "显示名称", position = 50)
    private String displayName;
    
    @ApiModelProperty(value = "全名", position = 50)
    private String fullName;

	@ApiModelProperty(value = "用户名")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String username;

    @ApiModelProperty(value = "密码")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String password;

    @ApiModelProperty(value = "状态", position = 170)
    private RecordStatus status;

    public CloudType getType() {
        return type;
    }

    public void setType(CloudType type) {
        this.type = type;
    }

    @ApiModelProperty(value = "类型", position = 171)
    private CloudType type;
    
    public Integer getServerConnectionId() {
        return serverConnectionId;
    }

    public void setServerConnectionId(Integer serverConnectionId) {
        this.serverConnectionId = serverConnectionId;
    }

    public String getServerConnectionName() {
        return serverConnectionName;
    }

    public void setServerConnectionName(String serverConnectionName) {
        this.serverConnectionName = serverConnectionName;
    }
    
    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }
}
