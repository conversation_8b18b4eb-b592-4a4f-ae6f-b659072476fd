package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderEskService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CatalogType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 云日志服务器申请
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/order/esk")
public class UpOrderElasticSearchController extends BaseController {

	@Autowired
	private IUpOrderEskService eskService;
	
	@Autowired
	private IUpProductService productService;

	@Autowired
	private IUpOrderService orderService;
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudServerChargeType type : CloudServerChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
 		Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        // 配置
        datas.put("serverConfigs", productService.queryVmSet(ProductVmSetType.esk, ThreadCache.getRegion()));
        datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.esk_disk));
        
        // 镜像
        datas.put("images", orderService.getTemplatesByCatalog(CatalogType.esk));
        
        // 网络
        datas.put("vpc", orderService.getVPC(id));
        
		return ResponseBean.success(datas);
	}
	
	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "产品code") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudServerChargeType type : CloudServerChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
 		Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        // 配置
        datas.put("serverConfigs", productService.queryVmSet(ProductVmSetType.esk, code));
        
        // 镜像
        //TODO  镜像确定方案未定
        datas.put("images", orderService.getTemplatesByCatalog(CatalogType.esk));
        
        // 网络
        datas.put("vpc", orderService.getVPC(ThreadCache.getUserId()));
        
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean save(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(eskService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
	public ResponseBean quotaSave(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(eskService.save(bean, ThreadCache.getUser()));
	}
	
}