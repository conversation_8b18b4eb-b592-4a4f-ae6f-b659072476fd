package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpCouponItemBean;

import javax.persistence.*;

@Entity
@Table(name = "up_coupon_item")
@Access(AccessType.FIELD)
public final class UpCouponItem extends BaseEntity<UpCouponItemBean> {

	@Column(name = "month")
	private Integer month;

	@Column(name = "discount")
	private Integer discount;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "coupon_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpCoupon coupon;

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Integer getDiscount() {
		return discount;
	}

	public void setDiscount(Integer discount) {
		this.discount = discount;
	}

	public UpCoupon getCoupon() {
		return coupon;
	}

	public void setCoupon(UpCoupon coupon) {
		this.coupon = coupon;
	}
}
