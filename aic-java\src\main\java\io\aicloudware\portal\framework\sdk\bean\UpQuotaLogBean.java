package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.QuotaLogStatus;
import io.aicloudware.portal.framework.sdk.contants.SpResourceType;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "资源配额申请记录")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpQuotaLogBean.class})
public class UpQuotaLogBean extends RecordBean {

    @ApiModelProperty(value = "所有者用户ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者用户名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "审批人ID")
    private Integer approverId;

    @ApiModelProperty(value = "审批人名称")
    private String approverName;

    @ApiModelProperty(value = "审批人名称")
    private String approverDisplayName;

    @ApiModelProperty(value = "申请原因")
    private String reason;

    @ApiModelProperty(value = "是否审批通过")
    private QuotaLogStatus quotaLogStatus;

    @ApiModelProperty(value = "审批意见")
    private String approveComment;

    @ApiModelProperty(value = "虚机可用数量")
    private SpService service;

    private String serviceText;

    @ApiModelProperty(value = "vm_used_num")
    private SpResourceType resourceType;

    private String resourceTypeText;

    private Integer applyQuota;

    @ApiModelProperty(value = "vm_max_num")
    private Integer quota;

    @ApiModelProperty(value = "vm_used_num")
    private Integer usedQuota;

    @ApiModelProperty(value = "sp_org_id")
    private Integer orgId;

    @ApiModelProperty(value = "sp_org_name")
    private String orgName;

    private SpRegionBean region;

    private Date recordTm;

    private UpQuotaLogBean[] children;

    private Integer parentId;

    private String parentName;

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getUsedQuota() {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota) {
        this.usedQuota = usedQuota;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public Integer getApproverId() {
        return approverId;
    }

    public void setApproverId(Integer approverId) {
        this.approverId = approverId;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApproverDisplayName() {
        return approverDisplayName;
    }

    public void setApproverDisplayName(String approverDisplayName) {
        this.approverDisplayName = approverDisplayName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getApproveComment() {
        return approveComment;
    }

    public void setApproveComment(String approveComment) {
        this.approveComment = approveComment;
    }

    public Integer getApplyQuota() {
        return applyQuota;
    }

    public void setApplyQuota(Integer applyQuota) {
        this.applyQuota = applyQuota;
    }

    public SpRegionBean getRegion() {
        return region;
    }

    public void setRegion(SpRegionBean region) {
        this.region = region;
    }

    public Date getRecordTm() {
        return recordTm;
    }

    public void setRecordTm(Date recordTm) {
        this.recordTm = recordTm;
    }

    public UpQuotaLogBean[] getChildren() {
        return children;
    }

    public void setChildren(UpQuotaLogBean[] children) {
        this.children = children;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public SpResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(SpResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public String getServiceText() {
        return serviceText;
    }

    public void setServiceText(String serviceText) {
        this.serviceText = serviceText;
    }

    public String getResourceTypeText() {
        return resourceTypeText;
    }

    public void setResourceTypeText(String resourceTypeText) {
        this.resourceTypeText = resourceTypeText;
    }

    public QuotaLogStatus getQuotaLogStatus() {
        return quotaLogStatus;
    }

    public void setQuotaLogStatus(QuotaLogStatus quotaLogStatus) {
        this.quotaLogStatus = quotaLogStatus;
    }
}
