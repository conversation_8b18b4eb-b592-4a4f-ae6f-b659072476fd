package io.aicloudware.portal.framework.hibernate;

import io.aicloudware.portal.framework.executor.IExecutorAR;

import java.lang.reflect.Field;

public final class PropertyFilter implements IExecutorAR<Field, Boolean> {

    public static final PropertyFilter isParentEntity = new PropertyFilter(EntityProperty::isParentEntity);

    public static final PropertyFilter isCopyOnUpdate = new PropertyFilter(EntityProperty::isCopyOnUpdate);

    public static final PropertyFilter isNullCopy = new PropertyFilter(EntityProperty::isNullCopy);

    private final IExecutorAR<EntityProperty, Boolean> executor;

    protected PropertyFilter(IExecutorAR<EntityProperty, Boolean> executor) {
        this.executor = executor;
    }

    @Override
    public Boolean doExecute(Field field) {
        EntityProperty property = field.getAnnotation(EntityProperty.class);
        return property == null || executor.doExecute(property);
    }
}
