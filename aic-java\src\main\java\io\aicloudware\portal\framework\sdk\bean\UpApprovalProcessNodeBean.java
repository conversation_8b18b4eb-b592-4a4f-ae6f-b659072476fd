package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "审批流程节点设定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpApprovalProcessNodeBean extends RecordBean {

    @ApiModelProperty(value = "审批流程ID", position = 60)
    private Integer approvalProcessId;

    @ApiModelProperty(value = "审批流程名称", position = 70)
    private String approvalProcessName;

    @ApiModelProperty(value = "节点序号", position = 80)
    private Integer seq;

    @ApiModelProperty(value = "用户ID", position = 90)
    private Integer userId;

    @ApiModelProperty(value = "用户名称", position = 100)
    private String userName;

    @ApiModelProperty(value = "用户显示名称")
    private String userDisplayName;

    @ApiModelProperty(value = "部门ID", position = 110)
    private Integer groupId;

    @ApiModelProperty(value = "部门名称", position = 120)
    private String groupName;

    @ApiModelProperty(value = "角色ID", position = 130)
    private Integer roleId;

    @ApiModelProperty(value = "角色名称", position = 140)
    private String roleName;

    @ApiModelProperty(value = "是否项目所属部门标志", position = 150)
    private Boolean isAppSystemGroup;

    @ApiModelProperty(value = "最大节点序号", position = 160)
    private Integer maxSeq;

    public Integer getApprovalProcessId() {
        return approvalProcessId;
    }

    public void setApprovalProcessId(Integer approvalProcessId) {
        this.approvalProcessId = approvalProcessId;
    }

    public String getApprovalProcessName() {
        return approvalProcessName;
    }

    public void setApprovalProcessName(String approvalProcessName) {
        this.approvalProcessName = approvalProcessName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Boolean getIsAppSystemGroup() {
        return isAppSystemGroup;
    }

    public void setIsAppSystemGroup(Boolean isAppSystemGroup) {
        this.isAppSystemGroup = isAppSystemGroup;
    }

    public Integer getMaxSeq() {
        return maxSeq;
    }

    public void setMaxSeq(Integer maxSeq) {
        this.maxSeq = maxSeq;
    }

    public String getUserDisplayName() {
        return userDisplayName;
    }

    public void setUserDisplayName(String userDisplayName) {
        this.userDisplayName = userDisplayName;
    }

}
