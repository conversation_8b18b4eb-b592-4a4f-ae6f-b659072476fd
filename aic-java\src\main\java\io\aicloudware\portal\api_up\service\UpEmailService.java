package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.CmVm;
import io.aicloudware.portal.api_up.entity.CmVmNetwork;
import io.aicloudware.portal.api_up.entity.ReqVm;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.api_up.entity.UpApplication;
import io.aicloudware.portal.api_up.entity.UpRole;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.EncryptUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.remote.RemoteUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class UpEmailService extends BaseTaskService implements IUpEmailService {

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @Autowired
    private IUpRightService upRightService;

    @Override
    protected void doTaskStart(UpTask task) {
        sendMail(task.getId());
    }

    @Override
    protected void doTaskRunning(UpTask task) {
        task.setTaskStatus(UpTaskStatus.finish);
    }

    @Override
    public void sendMail(Integer taskId) {
        ThreadCache.setSystemAdminLogin();

        String toAddress = null;
        String ccers = "";
        String[] subjectContent = new String[2];
        UpTask task = dao.load(UpTask.class, taskId);
        if (UpTaskType.send_mail_auto_archive.equals(task.getType())) {
            
        } else if (UpTaskType.send_mail_pending_approve.equals(task.getType())) {
        } else if (UpTaskType.send_mail_deploy_success.equals(task.getType())) {
            UpApplication application = dao.load(UpApplication.class, task.getTargetId());
            toAddress = application.getOwner().getName();
            subjectContent[0] = "部署成功：" + application.getName();
            subjectContent[1] = subjectContent[0] + "\r\n\r\n虚机：";

            List<ReqVm> reqVmList = dao.list(ReqVm.class, "application", application);
            for (ReqVm reqVm : reqVmList) {
                SpVm spVm = ListUtil.first(dao.list(SpVm.class, "reqVm", reqVm));
                if (spVm != null) {
                    subjectContent[1] += "\r\n" + getVmInfo(spVm, true);
                }
            }
        }

        if (Utility.isNotEmpty(toAddress) && Utility.isNotEmpty(subjectContent[0]) && Utility.isNotEmpty(subjectContent[1])) {
            UpSystemConfigBean systemConfigBean = upSystemConfigService.get(UpSystemConfigKey.system_title);
            if (Utility.isEmpty(systemConfigBean.getValue())) {
                systemConfigBean.setValue("UnifiedPortal云平台管理系统");
            }
            subjectContent[1] += "\r\n\r\n" + systemConfigBean.getValue();
            String response = "";
            String result = "";
            String msg = "";
            if (!Utility.isEmpty(response)) {
                Map responseData = RemoteUtil.gson.fromJson(response, Map.class);
                result = (String) responseData.get("result");
                msg = (String) responseData.get("msg");
            }
            AssertUtil.check("success".equalsIgnoreCase(result), "邮件发送失败(taskid=" + taskId + "): " + msg);
        }

    }

    private List<String> getToAddressList(String roleName) {
        List<String> toAddressList = new ArrayList<>();
        UpRole role = ListUtil.first(dao.list(UpRole.class, "name", roleName));
        if (role != null) {
            Map<Integer, String> userMap = upRightService.findUserMap(role.getId());
            for (Integer userId : userMap.keySet()) {
                UpUser user = dao.load(UpUser.class, userId);
                if (user != null && Utility.isNotEmpty(user.getEmail())) {
                    toAddressList.add(user.getEmail());
                }
            }
        }
        return toAddressList;
    }

    private String getVmInfo(CmVm cmVm, boolean addPwd) {
        String result = cmVm.getName();
        if (Utility.isNotEmpty(cmVm.getNetworkList())) {
            CmVmNetwork cmVmNetwork = (CmVmNetwork) cmVm.getNetworkList().get(0);
            result += " (" + cmVmNetwork.getIpAddress() + ")";
        }
        if (addPwd) {
            result += " (系统管理员密码：" + EncryptUtil.decryptWithRSA(cmVm.getOsPassword()) + ")";
        }
        return result;
    }

    @Override
    public void sendSystemExceptionMail(String errorMsg, String errorStackTrace) {
//        ThreadUtil.execute(new Runnable() {
//            @Override
//            public void run() {
//                // todo
//            }
//        });
    }
}
