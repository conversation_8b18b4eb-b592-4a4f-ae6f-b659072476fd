package io.aicloudware.portal.framework.remote;


import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;
import org.apache.http.HttpEntity;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

public class StringResponse<PERSON>andler extends BaseResponseHandler<String> {
    private static final Logger logger = Logger.getLogger(RemoteUtil.class);

    private final String charset;

    public StringResponseHandler(String charset) {
        this.charset = charset;
    }

    @Override
    protected String handleEntity(HttpEntity entity) {
        if (Utility.isNotEmpty(getLocation())) {
            return getLocation();
        }
        try {
            String body = EntityUtils.toString(entity, charset);
            RemoteUtil.outputLog("= Response =", body, false);
            return body;
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
