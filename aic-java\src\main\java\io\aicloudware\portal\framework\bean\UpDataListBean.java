package io.aicloudware.portal.framework.bean;

import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.validate.V02;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

public abstract class UpDataListBean<B extends RecordBean> extends UpApplicationBean {

    @ApiModelProperty(value = "实例数组")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private B[] dataList;

    public B[] getDataList() {
        return dataList;
    }

    public void setDataList(B[] dataList) {
        this.dataList = dataList;
    }

}
