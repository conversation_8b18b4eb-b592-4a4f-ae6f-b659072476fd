package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.KeyType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisNodeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "云服务器")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderCloudServerBean.class})
public class UpOrderCloudServerBean extends RecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "云主机类型")
	private ServerType serverType;
	
	@ApiModelProperty(value = "付费方式")
	private PaymentType paymentType;
	
	@ApiModelProperty(value = "计费方式")
	private CloudServerChargeType chargeType;
	
	@ApiModelProperty(value = "配置ID")
	private Integer serverConfigId;
	
	@ApiModelProperty(value = "CPU")
	private Integer cpu;
	
	@ApiModelProperty(value = "内存")
	private Integer memory;
	
	@ApiModelProperty(value = "镜像ID")
	private Integer imageId;
	
	@ApiModelProperty(value = "磁盘")
	private UpOrderCloudDiskBean[] cloudDiskList;
	
	@ApiModelProperty(value = "专有网络")
	private Integer vpcId;
	
	@ApiModelProperty(value = "子网")
	private Integer networkId;
	
	@ApiModelProperty(value = "分配弹性公网IP")
	private Boolean isElasticIp;

	@ApiModelProperty(value = "可用区")
	private String resourceRegion;
	
	@ApiModelProperty(value = "弹性公网IP")
	private UpOrderElasticIpBean[] elasticIpList;
	
	@ApiModelProperty(value = "密钥方式")
	private KeyType keyType;
	
	@ApiModelProperty(value = "密钥ID")
	private Integer keyId;
	
	@ApiModelProperty(value = "登录账号")
	private String account;
	
	@ApiModelProperty(value = "密码")
	private String password;
	
	@ApiModelProperty(value = "主机名")
	private String hostname;
	
	@ApiModelProperty(value = "购买数量")
	private Integer amount;
	
	@ApiModelProperty(value = "所有者")
	private Integer ownerId;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "Redis类型")
	private RedisType redisType;
	
	@ApiModelProperty(value = "Redis节点类型")
	private RedisNodeType redisNodeType;
	
	@ApiModelProperty(value = "分片数")
	private Integer sharding;
	
	// 虚机变更使用
	@ApiModelProperty(value = "变更vmId")
	private Integer vmId;
	
	// redis rds 变更使用
	@ApiModelProperty(value = "变更vappId")
	private Integer updateVappId;

	private Integer securityGroupId;

	public String getSecurityGroupName() {
		return securityGroupName;
	}

	public void setSecurityGroupName(String securityGroupName) {
		this.securityGroupName = securityGroupName;
	}

	private String securityGroupName;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public CloudServerChargeType getChargeType() {
		return chargeType;
	}

	public void setChargeType(CloudServerChargeType chargeType) {
		this.chargeType = chargeType;
	}

	public Integer getServerConfigId() {
		return serverConfigId;
	}

	public void setServerConfigId(Integer serverConfigId) {
		this.serverConfigId = serverConfigId;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemory() {
		return memory;
	}

	public void setMemory(Integer memory) {
		this.memory = memory;
	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public Boolean getIsElasticIp() {
		return isElasticIp;
	}

	public void setIsElasticIp(Boolean isElasticIp) {
		this.isElasticIp = isElasticIp;
	}
	
	public KeyType getKeyType() {
		return keyType;
	}

	public void setKeyType(KeyType keyType) {
		this.keyType = keyType;
	}

	public Integer getKeyId() {
		return keyId;
	}

	public void setKeyId(Integer keyId) {
		this.keyId = keyId;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getHostname() {
		return hostname;
	}

	public void setHostname(String hostname) {
		this.hostname = hostname;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public UpOrderCloudDiskBean[] getCloudDiskList() {
		return cloudDiskList;
	}

	public void setCloudDiskList(UpOrderCloudDiskBean[] cloudDiskList) {
		this.cloudDiskList = cloudDiskList;
	}

	public UpOrderElasticIpBean[] getElasticIpList() {
		return elasticIpList;
	}

	public void setElasticIpList(UpOrderElasticIpBean[] elasticIpList) {
		this.elasticIpList = elasticIpList;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public ServerType getServerType() {
		return serverType;
	}

	public void setServerType(ServerType serverType) {
		this.serverType = serverType;
	}

	public RedisType getRedisType() {
		return redisType;
	}

	public void setRedisType(RedisType redisType) {
		this.redisType = redisType;
	}

	public RedisNodeType getRedisNodeType() {
		return redisNodeType;
	}

	public void setRedisNodeType(RedisNodeType redisNodeType) {
		this.redisNodeType = redisNodeType;
	}

	public Integer getSharding() {
		return sharding;
	}

	public void setSharding(Integer sharding) {
		this.sharding = sharding;
	}

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public Integer getUpdateVappId() {
		return updateVappId;
	}

	public void setUpdateVappId(Integer updateVappId) {
		this.updateVappId = updateVappId;
	}

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }

	public Integer getSecurityGroupId() {
		return securityGroupId;
	}

	public void setSecurityGroupId(Integer securityGroupId) {
		this.securityGroupId = securityGroupId;
	}


}
