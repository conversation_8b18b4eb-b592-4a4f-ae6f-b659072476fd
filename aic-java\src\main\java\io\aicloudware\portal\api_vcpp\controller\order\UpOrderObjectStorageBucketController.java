package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderObjectStorageBucketService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 对象存储
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/order/objectStorageBucket")
public class UpOrderObjectStorageBucketController extends BaseController {

	@Autowired
	private IUpOrderObjectStorageBucketService objectStorageBucketService;

	@Autowired
	private IUpOrderService spOrderService;

	@Autowired
	private IUpProductService productService;

	@RequestMapping(value = "/init/{id}")
	@ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg() != null, "用户信息异常");

		Map<String, Object> datas = new HashMap<>();
		// 配置
		datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.object_storage_bucket));
		return ResponseBean.success(datas);
	}

	@RequestMapping(value = "/quota/init/{code}")
	@ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg() != null, "用户信息异常");
		Map<String, Object> datas = new HashMap<>();

		// 配置
		datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.object_storage_bucket, code));

		return ResponseBean.success(datas);
	}

	/**
	 * 保存
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "管理员新增订单")
	public ResponseBean save(@RequestBody UpOrderObjectStorageBucketBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(objectStorageBucketService.save(bean, applyUser));
	}

	/**
	 * 修改
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/change", method = RequestMethod.POST)
	@ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "更新订单")
	public ResponseBean change(@RequestBody UpOrderObjectStorageBucketBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(objectStorageBucketService.change(bean, applyUser));
	}

	/**
	 * 保存
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
	@ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderObjectStorageBucketBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(objectStorageBucketService.save(bean, ThreadCache.getUser()));
	}

	/**
	 * 创建桶(admin)
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/createBucket", method = RequestMethod.POST)
	@ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "管理员新增订单")
	public ResponseBean createBucket(@RequestBody UpOrderObjectStorageBucketBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(objectStorageBucketService.createBucket(bean, ThreadCache.getUser()));
	}

	/**
	 * 创建桶(quota)
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/createBucket", method = RequestMethod.POST)
	@ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "用户新增订单")
	public ResponseBean quotaCreateBucket(@RequestBody UpOrderObjectStorageBucketBean bean,
			HttpServletRequest request) {
//		AssertUtil.check(bean.getQuotaId(), "请选择协议");
//		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");   
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(objectStorageBucketService.createBucket(bean, ThreadCache.getUser()));
	}

	
//	/**
//	 * 文件上传(quota)
//	 * 
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/uploadBucketFile", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean uploadBucketFile(@RequestParam(value="files")  MultipartFile file,UpOrderObjectStorageBucketBean bean, HttpServletRequest request) {
//		AssertUtil.check(file, "请选择上传文件!");
//		bean.setOwnerId(ThreadCache.getUserId());
//		return ResponseBean.success(objectStorageBucketService.uploadBucketFile(bean, ThreadCache.getUser(),file));
//	}
	

}