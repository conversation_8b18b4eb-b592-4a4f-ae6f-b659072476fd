package io.aicloudware.portal.api_vcpp.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetBean;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetStatus;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetType;

@Entity
@Table(name = "up_work_sheet")
@Access(AccessType.FIELD)
public final class UpWorkSheet extends BaseUpEntity<UpWorkSheetBean> {

	private static final long serialVersionUID = -4794495107924954516L;

	@Column(name = "code")
	private String code;
	
	@JoinColumn(name = "user_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser user;

	@JoinColumn(name = "service_user_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser serviceUser;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "workSheet")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpWorkSheetDetail> workSheetDetailList;

	@Column(name = "work_sheet_status", nullable = false)
	@Enumerated(EnumType.STRING)
	private UpWorkSheetStatus workSheetStatus;
	
	@Column(name = "type", nullable = false)
	@Enumerated(EnumType.STRING)
	private UpWorkSheetType type;
	
	public UpUser getUser() {
		return user;
	}

	public void setUser(UpUser user) {
		this.user = user;
	}

	public UpUser getServiceUser() {
		return serviceUser;
	}

	public void setServiceUser(UpUser serviceUser) {
		this.serviceUser = serviceUser;
	}

	public List<UpWorkSheetDetail> getWorkSheetDetailList() {
		return workSheetDetailList;
	}

	public void setWorkSheetDetailList(List<UpWorkSheetDetail> workSheetDetailList) {
		this.workSheetDetailList = workSheetDetailList;
	}

	public UpWorkSheetStatus getWorkSheetStatus() {
		return workSheetStatus;
	}

	public void setWorkSheetStatus(UpWorkSheetStatus workSheetStatus) {
		this.workSheetStatus = workSheetStatus;
	}

	public UpWorkSheetType getType() {
		return type;
	}

	public void setType(UpWorkSheetType type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
}
