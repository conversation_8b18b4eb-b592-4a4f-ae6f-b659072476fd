package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpScriptCategory;
import io.aicloudware.portal.framework.sdk.contants.UpScriptType;
import io.aicloudware.portal.framework.bean.IPassword;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "脚本")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpScriptBean.class})
public class UpScriptBean extends RecordBean implements IPassword {

    @ApiModelProperty(value = "脚本分类")
    private UpScriptCategory category;

    @ApiModelProperty(value = "主脚本")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String mainScript;

    @ApiModelProperty(value = "配置文件")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String configFile;

    @ApiModelProperty(value = "脚本类型")
    private UpScriptType type;

    @ApiModelProperty(value = "脚本运行平台")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String platform;

    @ApiModelProperty(value = "运行时是否更新配置文件")
    private Boolean uploadConfig;

    @ApiModelProperty(value = "脚本参数")
    private String params;

    @ApiModelProperty(value = "操作系统用户名")
    private String username;

    @ApiModelProperty(value = "操作系统用户密码")
    private String password;

    @ApiModelProperty(value = "脚本运行等待超时时间(分钟)")
    private Integer timeout;

    @ApiModelProperty(value = "脚本输出路径")
    private String outputPath;

    @ApiModelProperty(value = "脚本输出文件")
    private String resultFile;

    public UpScriptCategory getCategory() {
        return category;
    }

    public void setCategory(UpScriptCategory category) {
        this.category = category;
    }

    public String getMainScript() {
        return mainScript;
    }

    public void setMainScript(String mainScript) {
        this.mainScript = mainScript;
    }

    public String getConfigFile() {
        return configFile;
    }

    public void setConfigFile(String configFile) {
        this.configFile = configFile;
    }

    public UpScriptType getType() {
        return type;
    }

    public void setType(UpScriptType type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Boolean getUploadConfig() {
        return uploadConfig;
    }

    public void setUploadConfig(Boolean uploadConfig) {
        this.uploadConfig = uploadConfig;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getOutputPath() {
        return outputPath;
    }

    public void setOutputPath(String outputPath) {
        this.outputPath = outputPath;
    }

    public String getResultFile() {
        return resultFile;
    }

    public void setResultFile(String resultFile) {
        this.resultFile = resultFile;
    }

}
