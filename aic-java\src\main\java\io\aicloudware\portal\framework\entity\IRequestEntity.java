package io.aicloudware.portal.framework.entity;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.framework.bean.RecordBean;

public interface IRequestEntity<B extends RecordBean> extends ISpEntity<B> {

    public UpOrder getOrder();

    public void setOrder(UpOrder order);

    public Integer getOrigId();

    public void setOrigId(Integer origId);

    public UpOperateType getOperateType();

    public void setOperateType(UpOperateType operateType);
    
//    public Integer getTaskSequence();
    
//    public void setTaskSequence(Integer taskSequence);
}
