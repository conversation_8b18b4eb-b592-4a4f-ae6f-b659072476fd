package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductCceSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;

import javax.persistence.*;

@Entity
@Table(name = "up_product_cce_set")
@Access(AccessType.FIELD)
public class UpProductCceSet extends BaseUpEntity<UpProductCceSetBean> {

	private static final long serialVersionUID = -1L;

	@Column(name = "product_code")
	private String productCode;

	@Column(name = "payment_type")
	private String paymentType;
	
	@Column(name = "node_unit")
	private Integer nodeUnit;

	@Column(name = "control_node_unit")
	private Integer controlNodeUnit;

	@Column(name = "info", length = ApiConstants.STRING_MAX_LENGTH)
	private String info;

	@Column(name = "cpu_unit")
	private Integer cpuUnit;

	@Column(name = "memory_unit")
	private Integer memoryUnit;

	@Column(name = "server_type")
	@Enumerated(EnumType.STRING)
	private UpOrderSystemEnums.ServerType serverType;

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getNodeUnit() {
		return nodeUnit;
	}

	public void setNodeUnit(Integer nodeUnit) {
		this.nodeUnit = nodeUnit;
	}

	public Integer getControlNodeUnit() {
		return controlNodeUnit;
	}

	public void setControlNodeUnit(Integer controlNodeUnit) {
		this.controlNodeUnit = controlNodeUnit;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public UpOrderSystemEnums.ServerType getServerType() {
		return serverType;
	}

	public void setServerType(UpOrderSystemEnums.ServerType serverType) {
		this.serverType = serverType;
	}

	public Integer getCpuUnit() {
		return cpuUnit;
	}

	public void setCpuUnit(Integer cpuUnit) {
		this.cpuUnit = cpuUnit;
	}

	public Integer getMemoryUnit() {
		return memoryUnit;
	}

	public void setMemoryUnit(Integer memoryUnit) {
		this.memoryUnit = memoryUnit;
	}
}
