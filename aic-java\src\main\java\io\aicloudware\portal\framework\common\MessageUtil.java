package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.framework.utility.Utility;

import java.util.Locale;

public final class MessageUtil {
    public static String format(String code, Locale locale, Object... args) {
        if (locale == null) {
            locale = SystemConfigUtil.getLocale();
        }

        String msg = BeanFactory.getMessageSource().getMessage(code, args, locale);
        if (Utility.isEmpty(msg)) {
            return code;
        } else {
            return msg;
        }
    }

    public static String format(String code, Object... args) {
        Locale locale = SystemConfigUtil.getLocale();
        String msg = BeanFactory.getMessageSource().getMessage(code, args, locale);
        if (Utility.isEmpty(msg)) {
            return code;
        } else {
            return msg;
        }
    }
}
