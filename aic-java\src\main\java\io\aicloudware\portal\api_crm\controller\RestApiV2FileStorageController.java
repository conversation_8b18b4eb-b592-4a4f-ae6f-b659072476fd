package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestFileStorageService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpFileStorageBean;
import io.aicloudware.portal.framework.sdk.bean.SpFileStorageResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpFileStorageSearchBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderFileStorageBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpFileStorage;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.service.ISpFileStorageService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/api/v2/nas/quota")
@Api(value = "/api/v2/nas/quota", description = "云安全", position = 140)
public class RestApiV2FileStorageController extends BaseEntityController<SpFileStorage, SpFileStorageBean, SpFileStorageResultBean> {

    @Autowired
    private ISpFileStorageService spFileStorageService;

    @Autowired
    private IRestFileStorageService restFileStorageService;

    /**
     * 保存
     * @param request
     * @return
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.FILE_STORAGE, description = "新增订单")
    public ResponseBean add(@RequestBody UpOrderFileStorageBean bean, HttpServletRequest request) {
        return ResponseBean.success(restFileStorageService.save(bean));
    }

    @RequestMapping(value = "/change", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.FILE_STORAGE, description = "变更订单")
    public ResponseBean change(@RequestBody UpOrderFileStorageBean bean, HttpServletRequest request) {
        return ResponseBean.success(restFileStorageService.change(bean));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpFileStorageResultBean.class) })
    @ResponseBody
    public ResponseBean queryFileStorage(@ApiParam(value = "查询条件") @RequestBody SpFileStorageSearchBean searchBean) {
        SpFileStorage entity = BeanCopyUtil.copy(searchBean.getBean(), SpFileStorage.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if (StringUtils.isNotEmpty(entity.getName())) {
            SpFileStorageBean fuzzyBean = new SpFileStorageBean();
            fuzzyBean.setName(entity.getName());
            fuzzyBean.setSharePath(fuzzyBean.getSharePath());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            SpFileStorageBean[] entityList = this.doQuery(searchBean, entity);
            SpFileStorageResultBean result = new SpFileStorageResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpFileStorageBean.class) })
//    @ResponseBody
//    public ResponseBean getFileStorage(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        commonService.load(SpFileStorage.class, SpFileStorageBean.class, id, ThreadCache.getOrgId());
//        return ResponseBean.success(spFileStorageService.getQuota(id));
//    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.FILE_STORAGE, description = "删除")
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable String id) {
        commonService.load(SpFileStorage.class, Integer.valueOf(id), ThreadCache.getOrgId());
        spFileStorageService.deleteFileStorage(Integer.valueOf(id));
        return ResponseBean.success(true);
    }
    
}
