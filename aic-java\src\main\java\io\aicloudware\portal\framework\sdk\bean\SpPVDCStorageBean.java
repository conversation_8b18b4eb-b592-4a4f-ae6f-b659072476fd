package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "计算资源存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpPVDCStorageBean.class})
public class SpPVDCStorageBean extends SpRecordBean {

    @ApiModelProperty(value = "计算资源ID")
    private Integer computeResourceId;

    @ApiModelProperty(value = "计算资源名称")
    private String computeResourceName;

    @ApiModelProperty(value = "计算资源显示名称")
    private String computeResourceDisplayName;

    @ApiModelProperty(value = "存储ID")
    private Integer storageId;

    @ApiModelProperty(value = "存储名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String storageName;

    @ApiModelProperty(value = "存储显示名称")
    private String storageDisplayName;

    @ApiModelProperty(value = "存储策略ID")
    private Integer storageReservationPolicyId;

    @ApiModelProperty(value = "存储策略名称")
    private String storageReservationPolicyName;

    @ApiModelProperty(value = "存储策略显示名称")
    private String storageReservationPolicyDisplayName;

    public Integer getComputeResourceId() {
        return computeResourceId;
    }

    public void setComputeResourceId(Integer computeResourceId) {
        this.computeResourceId = computeResourceId;
    }

    public Integer getStorageId() {
        return storageId;
    }

    public void setStorageId(Integer storageId) {
        this.storageId = storageId;
    }

    public Integer getStorageReservationPolicyId() {
        return storageReservationPolicyId;
    }

    public void setStorageReservationPolicyId(Integer storageReservationPolicyId) {
        this.storageReservationPolicyId = storageReservationPolicyId;
    }

    public String getComputeResourceName() {
        return computeResourceName;
    }

    public void setComputeResourceName(String computeResourceName) {
        this.computeResourceName = computeResourceName;
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = storageName;
    }

    public String getStorageReservationPolicyName() {
        return storageReservationPolicyName;
    }

    public void setStorageReservationPolicyName(String storageReservationPolicyName) {
        this.storageReservationPolicyName = storageReservationPolicyName;
    }

    public String getComputeResourceDisplayName() {
        return computeResourceDisplayName;
    }

    public void setComputeResourceDisplayName(String computeResourceDisplayName) {
        this.computeResourceDisplayName = computeResourceDisplayName;
    }

    public String getStorageDisplayName() {
        return storageDisplayName;
    }

    public void setStorageDisplayName(String storageDisplayName) {
        this.storageDisplayName = storageDisplayName;
    }

    public String getStorageReservationPolicyDisplayName() {
        return storageReservationPolicyDisplayName;
    }

    public void setStorageReservationPolicyDisplayName(String storageReservationPolicyDisplayName) {
        this.storageReservationPolicyDisplayName = storageReservationPolicyDisplayName;
    }

}
