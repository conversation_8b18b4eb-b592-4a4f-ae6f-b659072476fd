package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.util.Date;

@ApiModel(value = "任务")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpTaskBean.class})
public class UpTaskBean extends RecordBean {

    @ApiModelProperty(value = "父任务ID")
    private Integer parentId;

    @ApiModelProperty(value = "父任务名称")
    private String parentName;

    @ApiModelProperty(value = "类型")
    private UpTaskType type;

    @ApiModelProperty(value = "申请单ID")
    private Integer applicationId;

    @ApiModelProperty(value = "申请单名称")
    private String applicationName;

    @ApiModelProperty(value = "任务对象ID")
    private Integer targetId;

    @ApiModelProperty(value = "vRA请求编号")
    private Integer requestNumber;

    @ApiModelProperty(value = "vRA请求状态")
    private UpTaskStatus requestStatus;

    @ApiModelProperty(value = "状态")
    private UpTaskStatus taskStatus;

    @ApiModelProperty(value = "状态消息")
    private String statusMessage;

    @ApiModelProperty(value = "更新时间")
    private Date updateTm;

    @ApiModelProperty(value = "子任务列表")
    private UpTaskBean[] childList;

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public UpTaskType getType() {
        return type;
    }

    public void setType(UpTaskType type) {
        this.type = type;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public Integer getTargetId() {
        return targetId;
    }

    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    public Integer getRequestNumber() {
        return requestNumber;
    }

    public void setRequestNumber(Integer requestNumber) {
        this.requestNumber = requestNumber;
    }

    public UpTaskStatus getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(UpTaskStatus requestStatus) {
        this.requestStatus = requestStatus;
    }

    public UpTaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(UpTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public Date getUpdateTm() {
        return updateTm;
    }

    public void setUpdateTm(Date updateTm) {
        this.updateTm = updateTm;
    }

    public UpTaskBean[] getChildList() {
        return childList;
    }

    public void setChildList(UpTaskBean[] childList) {
        this.childList = childList;
    }
}
