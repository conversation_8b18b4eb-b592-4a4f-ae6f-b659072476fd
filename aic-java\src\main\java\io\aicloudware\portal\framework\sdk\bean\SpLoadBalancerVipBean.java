package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpIpStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡VIP绑定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpLoadBalancerVipBean extends SpRecordBean {

	@ApiModelProperty(value = "vdcId")
	private String vdcId;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "IP状态")
    private SpIpStatus ipStatus;
    
    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpIpStatus getIpStatus() {
        return ipStatus;
    }

    public void setIpStatus(SpIpStatus status) {
        this.ipStatus = status;
    }

	public String getVdcId() {
		return vdcId;
	}

	public void setVdcId(String vdcId) {
		this.vdcId = vdcId;
	}

}
