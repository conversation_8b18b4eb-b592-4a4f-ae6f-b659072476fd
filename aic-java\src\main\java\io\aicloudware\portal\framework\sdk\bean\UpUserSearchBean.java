package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "用户查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpUserSearchBean extends SearchBean<UpUserBean> {

    @ApiModelProperty(value = "状态列表")
    private RecordStatus[] statusList;

    public RecordStatus[] getStatusList() {
        return statusList;
    }

    public void setStatusList(RecordStatus[] statusList) {
        this.statusList = statusList;
    }
}
