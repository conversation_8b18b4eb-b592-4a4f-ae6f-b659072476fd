package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanSubscription;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.sdk.contants.UpServiceUsgeRecordStatus;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "服务使用记录")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpServiceUsageRecordBean.class})
public class UpServiceUsageRecordBean extends SpRecordBean {

    private Integer ownerId;

    private String ownerName;

    private String ownerDisplayName;

    private Integer spOrgId;

    private String spOrgName;

    private Integer upServicePlanId;

    private String upServicePlanName;

    private Integer upAppSystemId;

    private String upAppSystemName;

    @ApiModelProperty(value = "录入状态")
    private UpServiceUsgeRecordStatus recordStatus;

    @ApiModelProperty(value = "服务开始日期")
    private Date servicePlanStartTime;

    @ApiModelProperty(value = "服务结束日期")
    private Date servicePlanEndTime;

//    @ApiModelProperty(value = "预估费用")
//    private BigDecimal costExplorer;

    @ApiModelProperty(value = "实际费用")
    private BigDecimal actualCost;

    @ApiModelProperty(value = "实际费用")
    private BigDecimal originalCost;

    @ApiModelProperty(value = "服务类型")
    private UpServicePlanType servicePlanType;

    private String servicePlanTypeText;

    @ApiModelProperty(value = "实例ID")
    private Integer instanceId;

    private String instanceName;

    @ApiModelProperty(value = "订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "变更订单ID")
    private Integer opOrderId;

    @ApiModelProperty(value = "总记录")
    private UpServiceUsageRecordBean parent;

    @ApiModelProperty(value = "明细记录")
    private UpServiceUsageRecordBean[] childList;

    @ApiModelProperty(value = "折扣")
    private Integer discount;

    @ApiModelProperty(value = "服务时长（月）")
    private Integer billingCycle;


//    @ApiModelProperty(value = "订阅类别")
//    private UpServicePlanSubscription servicePlanSubscription;

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public Integer getSpOrgId() {
        return spOrgId;
    }

    public void setSpOrgId(Integer spOrgId) {
        this.spOrgId = spOrgId;
    }

    public String getSpOrgName() {
        return spOrgName;
    }

    public void setSpOrgName(String spOrgName) {
        this.spOrgName = spOrgName;
    }

    public Integer getUpServicePlanId() {
        return upServicePlanId;
    }

    public void setUpServicePlanId(Integer upServicePlanId) {
        this.upServicePlanId = upServicePlanId;
    }

    public String getUpServicePlanName() {
        return upServicePlanName;
    }

    public void setUpServicePlanName(String upServicePlanName) {
        this.upServicePlanName = upServicePlanName;
    }

    public Integer getUpAppSystemId() {
        return upAppSystemId;
    }

    public void setUpAppSystemId(Integer upAppSystemId) {
        this.upAppSystemId = upAppSystemId;
    }

    public String getUpAppSystemName() {
        return upAppSystemName;
    }

    public void setUpAppSystemName(String upAppSystemName) {
        this.upAppSystemName = upAppSystemName;
    }

    public UpServiceUsgeRecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(UpServiceUsgeRecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public Date getServicePlanStartTime() {
        return servicePlanStartTime;
    }

    public void setServicePlanStartTime(Date servicePlanStartTime) {
        this.servicePlanStartTime = servicePlanStartTime;
    }

    public Date getServicePlanEndTime() {
        return servicePlanEndTime;
    }

    public void setServicePlanEndTime(Date servicePlanEndTime) {
        this.servicePlanEndTime = servicePlanEndTime;
    }

//    public BigDecimal getCostExplorer() {
//        return costExplorer;
//    }
//
//    public void setCostExplorer(BigDecimal costExplorer) {
//        this.costExplorer = costExplorer;
//    }

    public BigDecimal getActualCost() {
        return actualCost;
    }

    public void setActualCost(BigDecimal actualCost) {
        this.actualCost = actualCost;
    }

    public UpServicePlanType getServicePlanType() {
        return servicePlanType;
    }

    public void setServicePlanType(UpServicePlanType servicePlanType) {
        this.servicePlanType = servicePlanType;
    }

    public Integer getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Integer instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOpOrderId() {
        return opOrderId;
    }

    public void setOpOrderId(Integer opOrderId) {
        this.opOrderId = opOrderId;
    }

    public UpServiceUsageRecordBean getParent() {
        return parent;
    }

    public void setParent(UpServiceUsageRecordBean parent) {
        this.parent = parent;
    }

    public UpServiceUsageRecordBean[] getChildList() {
        return childList;
    }

    public void setChildList(UpServiceUsageRecordBean[] childList) {
        this.childList = childList;
    }

//    public UpServicePlanSubscription getServicePlanSubscription() {
//        return servicePlanSubscription;
//    }
//
//    public void setServicePlanSubscription(UpServicePlanSubscription servicePlanSubscription) {
//        this.servicePlanSubscription = servicePlanSubscription;
//    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public String getServicePlanTypeText() {
        return servicePlanTypeText;
    }

    public void setServicePlanTypeText(String servicePlanTypeText) {
        this.servicePlanTypeText = servicePlanTypeText;
    }

    public BigDecimal getOriginalCost() {
        return originalCost;
    }

    public void setOriginalCost(BigDecimal originalCost) {
        this.originalCost = originalCost;
    }

    public Integer getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(Integer billingCycle) {
        this.billingCycle = billingCycle;
    }
}
