package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderElasticIp;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBandwidthSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class RestElasticIpService extends BaseService implements IRestElasticIpService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService upOrderQuotaService;

	@Autowired
	private IUpProductService upProductService;
	
	@Override
	public Integer save(UpOrderElasticIpBean bean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), bean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.NEW
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.ELASTIC_IP
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");
//		AssertUtil.check(bean.getBandwidth(), "请选择带宽峰值！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_elasticIp, user.getId()) == 0, "您有未完成的弹性公网IP申请！");

		String name = "ELASTICIP-"+System.currentTimeMillis() + String.format("%04d",(int)(Math.random()*1000));
		
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_elasticIp);
		order.setName("[" + OrderType.new_elasticIp + "]" + name);
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setPaymentType(UpOrderSystemEnums.PaymentType.postpayment);
		order.setBandwidthNum(bean.getBandwidth());
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);
		this.dao.insert(order);
		
		UpOrderElasticIp entity = BeanCopyUtil.copy(bean, UpOrderElasticIp.class);

		if(quotaDetail.getIsCustom()!= null && quotaDetail.getIsCustom()){
			entity.setBandwidth(Integer.valueOf(quotaDetail.getValue()));
		}else{
			UpProductBandwidthSetBean bandwidthSetBean = upProductService.getBandwidthSet(quotaDetail.getProductCode());
			AssertUtil.check(bandwidthSetBean, "产品编码异常");
			entity.setBandwidth(bandwidthSetBean.getUnit());
		}

		entity.setOrder(order);
		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setName(name);
		entity.setElasticIP(bean.getElasticIP());
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        task.setRegion(order.getRegion());
        dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
	}
}
