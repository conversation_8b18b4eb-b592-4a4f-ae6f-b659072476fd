package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpLicenseVersion;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "License")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpLicenseBean.class})
public class UpLicenseBean extends RecordBean {

    @ApiModelProperty(value = "机器码")
    private String serialNumber;

    @ApiModelProperty(value = "License机器码")
    private String licenseSerialNumber;

    @ApiModelProperty(value = "版本")
    private UpLicenseVersion version;

    @ApiModelProperty(value = "到期日期")
    private String expireDt;

    @ApiModelProperty(value = "私钥")
    private String privateKey;

    @ApiModelProperty(value = "License")
    private String licenseCode;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getLicenseSerialNumber() {
        return licenseSerialNumber;
    }

    public void setLicenseSerialNumber(String licenseSerialNumber) {
        this.licenseSerialNumber = licenseSerialNumber;
    }

    public UpLicenseVersion getVersion() {
        return version;
    }

    public void setVersion(UpLicenseVersion version) {
        this.version = version;
    }

    public String getExpireDt() {
        return expireDt;
    }

    public void setExpireDt(String expireDt) {
        this.expireDt = expireDt;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }
}
