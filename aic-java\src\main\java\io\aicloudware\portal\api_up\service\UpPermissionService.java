package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.*;
import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.executor.IExecutorAA;
import io.aicloudware.portal.framework.executor.IExecutorAR;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEcsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEvsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudVpcOperation;
import io.aicloudware.portal.platform_vcd.entity.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpPermissionService extends BaseTaskService implements IUpPermissionService {

    @Autowired
    protected IUpApplicationService upApplicationService;

    @Override
    public void notifyRefreshUsage() {
        ThreadCache.setSystemAdminLogin();
        upApplicationService.createSimpleOperateTask(UpTaskType.quota_refresh, "配额使用刷新", null);
    }

    @Override
    protected void doTaskRunning(UpTask task) {
        if (UpTaskType.quota_refresh.equals(task.getType())) {
            refreshUsage();
        }
        task.setTaskStatus(UpTaskStatus.finish);
        task.setStatusMessage(null);
    }

    private void refreshUsage() {
        List<SpVapp> deploymentList = dao.list(SpVapp.class);
        List<UpQuota> quotaList = dao.list(UpQuota.class);
        Map<String, UpQuota> quotaMap = ListUtil.map(quotaList, new ListUtil.Convert<UpQuota, String, UpQuota>() {
            @Override
            public String getKey(UpQuota quota) {
                return ""+getEntityId(quota.getOwner());
            }

            @Override
            public UpQuota getValue(UpQuota quota) {
//                quota.setVmUsedNum(0);
//                quota.setCpuUsedNum(0);
//                quota.setMemoryUsedGB(0);
//                quota.setDiskUsedGB(0);
//                quota.setIpUsedNum(0);
                return quota;
            }
        });

        for (SpVapp deployment : deploymentList) {
            UpUser user = deployment.getOwner();
            if (user == null ) {
                continue;
            }
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:0:0", deployment);
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:" + getEntityId(user) + ":0", deployment);
//            for (Integer groupId : groupService.getUserParentGroupIdSet(user.getId())) {
//                calculateResource(quotaMap, getEntityId(tenant) + ":" + groupId + ":0:0", deployment);
//            }
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:0:" + getEntityId(appSystem), deployment);
        }
        for (UpQuota quota : quotaMap.values()) {
            dao.update(quota);
        }
    }

    @Override
    public void checkDeploymentAdd(SpDeploymentListBean bean) {
        checkQuota(Arrays.asList(bean.getDataList()),
                (deployment, quota) -> calculateResource(deployment, quota),
                SpDeploymentBean::getOwnerId,
                true);
    }

    @Override
    public void checkDeploymentAdd(List<ReqDeployment> requestList, boolean isCheckOrSave) {
//        checkQuota(requestList,
//                (deployment, quota) -> calculateResource(deployment, quota),
//                deployment -> deployment.getOrder().getOwner().getId(),
//                isCheckOrSave);
    }

    @Override
    public void checkVmChangeResource(SpVmListBean bean) {
//        checkQuota(Arrays.asList(bean.getDataList()),
//                (vm, quota) -> calculateResource(getVmEntity(vm.getId()), vm, quota),
//                vm -> getVmEntity(vm.getId()).getSpDeployment().getOwner().getId(),
//                vm -> getVmEntity(vm.getId()).getSpDeployment().getAppSystem().getId(),
//                true);
    }

    @Override
    public void checkVmChangeResource(List<ReqVm> requestList, boolean isCheckOrSave) {
//        checkQuota(requestList,
//                (vm, quota) -> calculateResource(getVmEntity(vm.getOrigId()), vm, quota),
//                vm -> getVmEntity(vm.getOrigId()).getSpDeployment().getOwner().getId(),
//                vm -> getVmEntity(vm.getOrigId()).getSpDeployment().getAppSystem().getId(),
//                isCheckOrSave);
    }

    private SpVm getVmEntity(Integer id) {
        return dao.load(SpVm.class, id);
    }

    private <T> void checkQuota(List<T> dataList, IExecutorAA<T, UpQuota> executor, IExecutorAR<T, Integer> getUserIdExecutor, boolean isCheckOrSave) {
        Map<Integer, List<T>> userDataListMap = new HashMap<>();
        for (Integer userId : userDataListMap.keySet()) {
            UpUser user = dao.load(UpUser.class, userId);
            List<T> _dataList = userDataListMap.get(userId);

            UpQuota quota = new UpQuota();
            quota.setOwner(user);
            checkQuota(quota, _dataList, executor, isCheckOrSave);

            quota.setOwner(null);
            checkQuota(quota, _dataList, executor, isCheckOrSave);
        }
    }


    private <T> void checkQuota(UpQuota quota, List<T> dataList, IExecutorAA<T, UpQuota> executor, boolean isCheckOrSave) {
        quota = ListUtil.first(dao.query(new UpQuotaSearchBean(), quota));
        if (quota == null) {
            return;
        }
        for (T data : dataList) {
            executor.doExecute(data, quota);
        }
        if (isCheckOrSave) {
            dao.evict(Arrays.asList(quota));
//            checkQuota(quota, Utility.toZero(quota.getVmMaxNum()), Utility.toZero(quota.getVmUsedNum()), "虚机", "个");
//            checkQuota(quota, Utility.toZero(quota.getCpuMaxNum()), Utility.toZero(quota.getCpuUsedNum()), "CPU", "核");
//            checkQuota(quota, Utility.toZero(quota.getCpuMaxGHz()), Utility.toZero(quota.getCpuUsedGHz()), "CPU", "GHz");
//            checkQuota(quota, Utility.toZero(quota.getMemoryMaxGB()), Utility.toZero(quota.getMemoryUsedGB()), "内存", "GB");
//            checkQuota(quota, Utility.toZero(quota.getDiskMaxGB()), Utility.toZero(quota.getDiskUsedGB()), "存储", "GB");
//            checkQuota(quota, Utility.toZero(quota.getIpMaxNum()), Utility.toZero(quota.getIpUsedNum()), "IP", "个");
//            checkQuota(quota, Utility.toZero(quota.getCostMaxMoney()), Utility.toZero(quota.getCostUsedMoney()), "金额", "元");
        } else {
            dao.update(quota);
        }
    }

    private Integer getEntityId(IEntity entity) {
        return entity == null ? Utility.ZERO : Utility.toZero(entity.getId());
    }

    private void checkQuota(UpQuota quota, Number maxValue, Number usedValue, String title, String unit) {
//        AssertUtil.check(Utility.isZero(maxValue.doubleValue()) || maxValue.doubleValue() * quota.getThreshold() >= usedValue.doubleValue(),
//                "请求资源超出配额（配额ID:" + quota.getId() + ", " + title
//                        + "上限:" + Double.valueOf(maxValue.doubleValue() * quota.getThreshold()).longValue() + unit
//                        + ", 请求使用:" + usedValue.longValue() + "" + unit + "）"
//        );
    }

    private void calculateResource(Map<String, UpQuota> quotaMap, String key, SpVapp deployment) {
        UpQuota quota = quotaMap.get(key);
        if (quota != null) {
            calculateResource(deployment, quota);
        }
    }

    private void calculateResource(SpDeploymentBean deployment, UpQuota quota) {
        if (Utility.isNotEmpty(deployment.getVmList())) {
            for (SpVmBean vm : deployment.getVmList()) {
                calculateResource(null, vm, quota);
            }
        }
        if (Utility.isNotEmpty(deployment.getChildList())) {
            for (SpDeploymentBean deploymentBean : deployment.getChildList()) {
                calculateResource(deploymentBean, quota);
            }
        }
    }

    private void calculateResource(ReqDeployment deployment, UpQuota quota) {
        if (Utility.isNotEmpty(deployment.getVmList())) {
            for (ReqVm vm : deployment.getVmList()) {
                calculateResource(null, vm, quota);
            }
        }
    }

    private void calculateResource(SpVapp deployment, UpQuota quota) {
        if (Utility.isNotEmpty(deployment.getVmList())) {
            for (SpVm vm : deployment.getVmList()) {
                calculateResource(vm, quota);
            }
        }
    }

    private void calculateResource(SpVm oldVm, SpVmBean vm, UpQuota quota) {
//        subtractOldResource(oldVm, quota);
//        quota.setVmUsedNum(quota.getVmUsedNum() + vm.getInstanceNum());
//        quota.setCpuUsedNum(quota.getCpuUsedNum() + vm.getCpuNum() * vm.getInstanceNum());
//        quota.setMemoryUsedGB(quota.getMemoryMaxGB() + vm.getMemoryGB() * vm.getInstanceNum());
//        for (SpVmDiskBean disk : vm.getDiskList()) {
//            quota.setDiskUsedGB(quota.getDiskUsedGB() + disk.getDiskGB() * vm.getInstanceNum());
//        }
//        quota.setIpUsedNum(quota.getIpUsedNum() + vm.getNetworkList().length * vm.getInstanceNum());
    }

    private void calculateResource(SpVm oldVm, ReqVm vm, UpQuota quota) {
        subtractOldResource(oldVm, quota);
//        quota.setVmUsedNum(quota.getVmUsedNum() + vm.getInstanceNum());
//        quota.setCpuUsedNum(quota.getCpuUsedNum() + vm.getCpuNum() * vm.getInstanceNum());
//        quota.setMemoryUsedGB(quota.getMemoryMaxGB() + vm.getMemoryGB() * vm.getInstanceNum());
//        for (ReqVmDisk disk : vm.getDiskList()) {
//            quota.setDiskUsedGB(quota.getDiskUsedGB() + disk.getDiskGB() * vm.getInstanceNum());
//        }
//        quota.setIpUsedNum(quota.getIpUsedNum() + vm.getNetworkList().size() * vm.getInstanceNum());
    }

    private void calculateResource(SpVm vm, UpQuota quota) {
//        quota.setVmUsedNum(quota.getVmUsedNum() + 1);
//        quota.setCpuUsedNum(quota.getCpuUsedNum() + vm.getCpuNum());
//        quota.setMemoryUsedGB(quota.getMemoryMaxGB() + vm.getMemoryGB());
//        for (SpVmDisk disk : vm.getDiskList()) {
//            quota.setDiskUsedGB(quota.getDiskUsedGB() + disk.getDiskGB());
//        }
//        quota.setIpUsedNum(quota.getIpUsedNum() + vm.getNetworkList().size());
    }

    private void subtractOldResource(SpVm oldVm, UpQuota quota) {
        if (oldVm != null) {
//            quota.setVmUsedNum(quota.getVmUsedNum() - 1);
//            quota.setCpuUsedNum(quota.getCpuUsedNum() - oldVm.getCpuNum());
//            quota.setMemoryUsedGB(quota.getMemoryMaxGB() - oldVm.getMemoryGB());
//            for (SpVmDisk disk : oldVm.getDiskList()) {
//                quota.setDiskUsedGB(quota.getDiskUsedGB() - disk.getDiskGB());
//            }
//            quota.setIpUsedNum(quota.getIpUsedNum() - oldVm.getNetworkList().size());
        }
    }

    @Override
    public void sync(Integer orgId, SpRegionEntity region) {
        SpOrg org = dao.load(SpOrg.class, orgId);
        if (CloudType.ecloud.equals(region.getType())) {
            throw new RuntimeException("暂不支持");
        } else if (CloudType.jointecloud.equals(region.getType()) ) {
            Optional<SpProject> spProject = dao.list(SpProject.class, MapUtil.of("region", region, "status", RecordStatus.active)).stream().findFirst();
            AssertUtil.check(spProject.isPresent(), "未找到对应的项目");
            List<UpQuota> serverDataList = JointEcloudEvsOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get());
            serverDataList.addAll(JointEcloudEcsOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get()));
            serverDataList.addAll(JointEcloudVpcOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get()));

            serverDataList.forEach(o -> {
                o.setOrg(org);
                o.setRegion(region);
                o.setStatus(RecordStatus.active);
                o.setIsAdminQuota(true);
            });

            Comparator<UpQuota> comparator = Comparator
                    .comparing(UpQuota::getResourceType) // 先按 resourceType 比较
                    .thenComparing(UpQuota::getService);   // 再按 service 比较
            List<UpQuota> tableDataList = dao.list(UpQuota.class, MapUtil.of("region", region, "org", org, "status", RecordStatus.active, "isAdminQuota", true));

            List<UpQuota> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, comparator);
            List<UpQuota> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, comparator);
            List<UpQuota> dataToUpdate = ListUtil.getIntersection(tableDataList, serverDataList, comparator,
                    (o1, o2) -> {
                        o1.setQuota(o2.getQuota());
                        o1.setUsedQuota(o2.getUsedQuota());
                    });
            dataToAdd.forEach( o -> {
                dao.insert(o);
            } );
            dataToDel.forEach( o -> {
                dao.delete(UpProductVmSet.class, o.getId());
            } );
            dataToUpdate.forEach( o -> {
                dao.update(o);
            } );
        }
    }

    @Override
    public UpPermissionBean[] queryUserTemplate(UpPermissionSearchBean searchBean) {
        final SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        List<UpPermission> dbUserTemplates = dao.list(UpPermission.class, Map.of("isUserTemplate", true, "org.id", ThreadCache.getOrgId(), "region", ThreadCache.getRegion()));
        Map<SpService, List<UpPermission>> dbUserTemplateMap = dbUserTemplates.stream().collect(Collectors.groupingBy(item -> item.getService()));

        Map<SpService, List<String>> currentPermissionTypes = ListUtil.map(Arrays.asList(SpService.values()), (map, service) -> {
            map.put(service, Arrays.stream(service.getPermissionTypes()).map(UpPermissionType::name).collect(Collectors.toList()));
        });

        List<UpPermission> entitys = new ArrayList<>();
        currentPermissionTypes.keySet().stream().forEach(service -> {
            if(!dbUserTemplateMap.containsKey(service)){
                currentPermissionTypes.get(service).stream().forEach(permissionType -> {
                    entitys.add(buildEntity(org,service,permissionType, true, false, null));
                });
            }else{
                List<String> dbExsitPermissionTypes = dbUserTemplateMap.get(service).stream().map(UpPermission::getPermissionType).map(UpPermissionType::name).distinct().collect(Collectors.toList());
                List<String> items = currentPermissionTypes.get(service);
                items.removeAll(dbExsitPermissionTypes);
                items.forEach(permissionType -> {
                    entitys.add(buildEntity(org,service,permissionType, true, false,null));
                });
            }
        });
        if(!entitys.isEmpty()){
            dao.insert(entitys);
            dao.flush();
        }
        UpPermission entity = BeanCopyUtil.copy(searchBean.getBean(),UpPermission.class);
        entity.setOrg(org);
        entity.setRegion(ThreadCache.getRegion());
        entity.setUserTemplate(true);
        if (StringUtils.isNotEmpty(entity.getName())) {
            UpPermissionBean fuzzyBean = new UpPermissionBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        UpPermissionBean[] beans = BeanCopyUtil.copy2BeanList(dao.query(searchBean, entity), UpPermissionBean.class);
        Arrays.stream(beans).forEach(item -> {
            item.setServiceText(item.getService().getTitle());
            item.setPermissionTypeText(item.getPermissionType().getTitle());
        });
        return beans;
    }

    @Override
    public void saveUserTemplate(UpPermissionBean bean) {
//        UpQuota entity = dao.load(UpQuota.class, bean.getId());
//        AssertUtil.check(entity != null && entity.getOrg().getId().equals(ThreadCache.getOrgId()) && entity.getRegion().equals(ThreadCache.getRegion()) && entity.getIsUserTemplate(), "无操作权限！");
//        entity.setQuota(bean.getQuota());
//        dao.update(entity, "quota");
    }

    @Override
    public UpPermissionBean adminDetail(Integer id) {

//        return adminList(id).get(0);
        return null;
    }

    @Override
    public void saveUserPermission(UpPermissionListBean listBean) {
        UpPermission template = dao.load(UpPermission.class, listBean.getUserTemplateId());

        List<UpPermission> dbEntitys = dao.list(UpPermission.class, Map.of("isUserTemplate", false, "org.id", ThreadCache.getOrgId(),
                "region", ThreadCache.getRegion(), "service", template.getService(),
                "permissionType", template.getPermissionType()));

        List<Integer> ownerIds = Arrays.stream(listBean.getDataList()).map(UpPermissionBean::getOwnerId).collect(Collectors.toList());
        List<Integer> removeIds = dbEntitys.stream().filter(item -> ownerIds.indexOf(item.getOwner().getId()) < 0).map(UpPermission::getId).collect(Collectors.toList());
        dao.destroy(UpPermission.class, removeIds);
        dao.flush();

        List<Integer> dbOwnerIds = dbEntitys.stream().map(UpPermission::getOwner).map(UpUser::getId).collect(Collectors.toList());

        ownerIds.removeAll(dbOwnerIds);
        List<UpPermission> entitys = ownerIds.stream().map(i -> {
            return this.buildEntity(template.getOrg(),template.getService(), template.getPermissionType().name(),false, true, new UpUser(i));
//            UpPermission entity = new UpPermission();
//            entity.setOwner(new UpUser(i));
//            entity.setService(template.getService());
//            entity.setPermissionType(template.getPermissionType());
//            entity.setOrg(template.getOrg());
//            entity.setEnabledStatus(true);
//            entity.setRegion(template.getRegion());
//            entity.setUrl(template.getUrl());
//            entity.setUserTemplate(false);
//            entity.setName(template.getName() + "-" + i);
//            return entity;
        }).collect(Collectors.toList());
        dao.insert(entitys);
    }

    @Override
    public void savePermissionByUserId(UpPermissionListBean listBean) {
        List<UpPermission> dbEntitys = dao.list(UpPermission.class, Map.of("isUserTemplate", false, "org.id", ThreadCache.getOrgId(),
                "region", ThreadCache.getRegion(), "owner", new UpUser(listBean.getUserId())));

        List<Integer> removeIds = dbEntitys.stream().filter(item ->
            Arrays.stream(listBean.getDataList()).filter(bean -> bean.getPermissionType() == item.getPermissionType() && bean.getService() == item.getService()).count() == 0
        ).map(UpPermission::getId).collect(Collectors.toList());
        dao.destroy(UpPermission.class, removeIds);
        dao.flush();

        List<UpPermission> entitys = Arrays.stream(listBean.getDataList()).filter(bean ->
                dbEntitys.stream().filter(item -> item.getPermissionType() == bean.getPermissionType() && item.getService() == bean.getService()).count() == 0
        ).map(item ->
             this.buildEntity(new SpOrg(ThreadCache.getOrgId()),item.getService(),item.getPermissionType().name(),false, true, new UpUser(listBean.getUserId()))
        ).collect(Collectors.toList());
        dao.insert(entitys);
    }

    @Override
    public List<UpPermissionBean> adminList(Integer id) {
//        String sql =
//            "WITH \n" +
//            "user_count AS ( SELECT COUNT ( 1 ) AS user_count FROM up_user WHERE is_system_admin = FALSE and status = 'active'),\n" +
//            "tpl AS ( SELECT quota, resource_type AS resource_type FROM up_quota q WHERE is_user_template = TRUE and region = :region and sp_org_id = :orgId),\n" +
//            "quota AS ( SELECT COUNT ( DISTINCT owner_id ) as assigend_user_count, SUM ( quota ) AS assigend_quota, resource_type FROM up_quota q WHERE is_admin_quota = FALSE and region = :region and sp_org_id = :orgId GROUP BY resource_type ) \n" +
//            "SELECT t.service,t.resource_type, t.used_quota as usedQuota\n" +
//            ",(c.user_count-(case when q.assigend_user_count is null then 0 else assigend_user_count end)) * tpl.quota + (case when q.assigend_quota is null then 0 else q.assigend_quota end) as assigendQuota, " +
//            "t.quota, t.id\n" +
//            "FROM\n" +
//            "up_quota t \n" +
//            "LEFT JOIN user_count C ON 1 = 1 \n" +
//            "left join tpl tpl on t.resource_type = tpl.resource_type\n" +
//            "left join quota q on t.resource_type = q.resource_type\n" +
//            "WHERE\n" +
//            "is_admin_quota = TRUE and region = :region and sp_org_id = :orgId " +
//                    (id == null ? "": " and id = " + id) +
//            " order by service, resource_type";
//        List<Object[]> result = queryDao.querySql(sql, MapUtil.of("region",ThreadCache.getRegion().name(), "orgId", ThreadCache.getOrgId()));
////        Object obj = queryDao.querySql(sql, null);
//
//        return result.stream().map(item -> {
//            UpQuotaBean bean = new UpQuotaBean();
//            bean.setService(SpService.valueOf((String) item[0]));
//            bean.setResourceType(SpResourceType.valueOf((String) item[1]));
//            bean.setUsedQuota((Integer) item[2]);
//            bean.setAssignedQuota(((BigInteger) item[3]).intValue());
//            bean.setQuota((Integer) item[4]);
//            bean.setId((Integer) item[5]);
//            return bean;
//        }).collect(Collectors.toList());
        return null;
    }

    @Override
    public void updateUserTemplate(UpPermissionBean bean) {
        UpPermission entity = dao.load(UpPermission.class,bean.getId());
        entity.setEnabledStatus(bean.getEnabledStatus());
        dao.update(entity, "enabledStatus");
    }

    @Override
    public UpPermissionBean[] listPermissionByTemplate(Integer id) {
        UpPermission template = dao.load(UpPermission.class,id);
        List<UpPermission> entitys = dao.list(UpPermission.class, MapUtil.of("org", new SpOrg(ThreadCache.getOrgId()),
                "region", ThreadCache.getRegion(), "service", template.getService(),
                "permissionType", template.getPermissionType(),"enabledStatus", true,
                "isUserTemplate", false));

        return BeanCopyUtil.copy2BeanList(entitys, UpPermissionBean.class);
    }

    @Override
    public UpPermissionBean[] userList(Integer id) {
        List<UpPermission> entitys = dao.list(UpPermission.class, MapUtil.of("org", new SpOrg(ThreadCache.getOrgId()),
                "region", ThreadCache.getRegion(),"owner", new UpUser(id),
                "isUserTemplate", false));
        List<UpPermission> templates = dao.list(UpPermission.class, MapUtil.of("org", new SpOrg(ThreadCache.getOrgId()),
                "region", ThreadCache.getRegion(),"isUserTemplate", true, "enabledStatus", true));
        entitys.addAll(templates);
        return BeanCopyUtil.copy2BeanList(entitys, UpPermissionBean.class);
    }

    private UpPermission buildEntity(SpOrg org, SpService service, String permissionType, Boolean isUserTemplate, Boolean enabledStatus, UpUser owner){
        UpPermission entity = new UpPermission();
        entity.setPermissionType(UpPermissionType.valueOf(permissionType));
        entity.setService(service);
        entity.setName(service.name() + "-" + permissionType);
        entity.setOrg(org);
        entity.setRegion(ThreadCache.getRegion());
        entity.setUrl(service.getUrl());
        entity.setUserTemplate(isUserTemplate);
        entity.setEnabledStatus(enabledStatus);
        entity.setOwner(owner);
        return entity;
    }
}
