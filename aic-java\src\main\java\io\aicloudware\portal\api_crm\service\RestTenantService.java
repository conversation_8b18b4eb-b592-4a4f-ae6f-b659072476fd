package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpOperationLogService;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.RSAUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.entity.SpServerConnection;
import io.aicloudware.portal.platform_vcd.service.ISpOVDCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class RestTenantService extends BaseService implements IRestTenantService {

	@Autowired
	private IUpOperationLogService upOperationLogService;

	@Autowired
	private ISpOVDCService spOVDCService;


	private Map<String, Object> buildTokenMap(Integer userId, SpRegionEntity region){
		UpUser user = dao.load(UpUser.class, userId);
//		AssertUtil.check(user.getOrg(),user.getName()+ "未绑定租户资源");
		List<SpServerConnection> serverConnectionList = dao.list(SpServerConnection.class);
		serverConnectionList = serverConnectionList.stream().filter(serverConnection -> RecordStatus.active.equals(serverConnection.getStatus())).collect(Collectors.toList());
		SpOrg org = user.getOrg();
		Map<String, Object> map = new HashMap<>();
		//map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + user.getOrg().getId() + ":" + bean.getSsoToken()));
		map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + (user.getOrg() == null ? "" : user.getOrg().getId()) + ":" + region + ":" + System.currentTimeMillis()));
		map.put("username", user.getUsername());
		map.put("isAdmin", user.getSystemAdmin() == null ? false : user.getSystemAdmin());
		if(org != null) {
			map.put("isArchiveUser", org.getIsArchive() == null ? false : org.getIsArchive());
		}
		map.put("region", region);
		return map;
	}

}
