package io.aicloudware.portal.framework.sdk.bean.finance;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "账户余额")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpFinanceBalanceBean extends RecordBean {

	public UpFinanceBalanceBean(BigDecimal cash_amount, BigDecimal total_amount, BigDecimal unclear_amount) {
		this.cash_amount = cash_amount;
		this.total_amount = total_amount;
		this.unclear_amount = unclear_amount;
	}

	@ApiModelProperty(value = "现金余额")
	private BigDecimal cash_amount;

	@ApiModelProperty(value = "账户总余额")
	private BigDecimal total_amount;

	@ApiModelProperty(value = "未结清账单金额")
	private BigDecimal unclear_amount;
	
	@ApiModelProperty(value = "限制金额")
	private BigDecimal limit_amount;

	public BigDecimal getCash_amount() {
		return cash_amount;
	}

	public void setCash_amount(BigDecimal cash_amount) {
		this.cash_amount = cash_amount;
	}

	public BigDecimal getTotal_amount() {
		return total_amount;
	}

	public void setTotal_amount(BigDecimal total_amount) {
		this.total_amount = total_amount;
	}

	public BigDecimal getUnclear_amount() {
		return unclear_amount;
	}

	public void setUnclear_amount(BigDecimal unclear_amount) {
		this.unclear_amount = unclear_amount;
	}

	public BigDecimal getLimit_amount() {
		return limit_amount;
	}

	public void setLimit_amount(BigDecimal limit_amount) {
		this.limit_amount = limit_amount;
	}
	
}
