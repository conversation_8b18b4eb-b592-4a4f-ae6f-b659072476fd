package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "redis产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductRedisSetBean extends RecordBean {
	
	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "CPU配件ID")
    private Integer cpuProductItemId;
	
	@ApiModelProperty(value = "CPU单位数")
	private Integer cpuUnit;
	
	@ApiModelProperty(value = "CPU价格")
	private BigDecimal cpuPrice;
	
	@ApiModelProperty(value = "内存配件ID")
    private Integer memoryProductItemId;

	@ApiModelProperty(value = "内存单位数")
	private Integer memoryUnit;
	
	@ApiModelProperty(value = "内存价格")
	private BigDecimal memoryPrice;
	
	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;
	
	@ApiModelProperty(value = "类型")
	private RedisType type;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getCpuProductItemId() {
		return cpuProductItemId;
	}

	public void setCpuProductItemId(Integer cpuProductItemId) {
		this.cpuProductItemId = cpuProductItemId;
	}

	public Integer getCpuUnit() {
		return cpuUnit;
	}

	public void setCpuUnit(Integer cpuUnit) {
		this.cpuUnit = cpuUnit;
	}

	public BigDecimal getCpuPrice() {
		return cpuPrice;
	}

	public void setCpuPrice(BigDecimal cpuPrice) {
		this.cpuPrice = cpuPrice;
	}

	public Integer getMemoryProductItemId() {
		return memoryProductItemId;
	}

	public void setMemoryProductItemId(Integer memoryProductItemId) {
		this.memoryProductItemId = memoryProductItemId;
	}

	public Integer getMemoryUnit() {
		return memoryUnit;
	}

	public void setMemoryUnit(Integer memoryUnit) {
		this.memoryUnit = memoryUnit;
	}

	public BigDecimal getMemoryPrice() {
		return memoryPrice;
	}

	public void setMemoryPrice(BigDecimal memoryPrice) {
		this.memoryPrice = memoryPrice;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public RedisType getType() {
		return type;
	}

	public void setType(RedisType type) {
		this.type = type;
	}

}
