package io.aicloudware.portal.api_up.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.ReqDeployment;
import io.aicloudware.portal.api_up.entity.ReqVm;
import io.aicloudware.portal.api_up.entity.UpApplication;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.common.SdkConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.sdk.bean.ReqBlueprintDeploymentBean;
import io.aicloudware.portal.framework.sdk.bean.SpOVDCBean;
import io.aicloudware.portal.framework.sdk.bean.SpOVDCSearchBean;
import io.aicloudware.portal.framework.sdk.bean.SpPropertyGroupBean;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateMachineBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmSearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOVDC;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplateRelation;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Service
@Transactional
public class UpNewApplicationService extends BaseService implements IUpNewApplicationService {

    @Autowired
    private IUpApplicationService upApplicationService;

    @Override
    public UpApplicationBean getApplication(Integer applicationId) {
        UpApplication upApplication = dao.load(UpApplication.class, applicationId);
        return BeanCopyUtil.copy(upApplication, UpApplicationBean.class);
    }

    @Override
    public UpApplicationBean createApplication(UpApplicationBean applicationBean) {
        applicationBean.setType(UpApplicationType.deployment_add);
        applicationBean.setOwnerId(ThreadCache.getUserId());
        applicationBean.setApplicationStatus(UpApplicationStatus.pending_submit);
        UpApplication upApplication = BeanCopyUtil.copy(applicationBean, UpApplication.class);
        dao.insert(upApplication);
        return BeanCopyUtil.copy(upApplication, UpApplicationBean.class);
    }

    @Override
    public UpApplicationBean updateApplication(UpApplicationBean applicationBean) {
        UpApplication upApplication = dao.load(UpApplication.class, applicationBean.getId());
        BeanCopyUtil.copy(applicationBean, upApplication);
        dao.update(upApplication);
        return BeanCopyUtil.copy(upApplication, UpApplicationBean.class);
    }

    @Override
    public void submitApplication(Integer applicationId) {
//        UpApplication upApplication = dao.load(UpApplication.class, applicationId);
//        upApplication.setApplicationStatus(UpApplicationStatus.pending_approve);
//        dao.insert(upApplication);
//        upApplicationService.handleApplicationProcess(upApplication);
    }

    @Override
    public SpOVDCBean[] getReservationList() {
        SpOVDCSearchBean searchBean = new SpOVDCSearchBean();
        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
        searchBean.setOrderName1("displayName");
        searchBean.setOrderBy1(Boolean.FALSE);
        searchBean.setBean(new SpOVDCBean());

        List<SpOVDC> reservationList = dao.query(searchBean, new SpOVDC());
        reservationList = reservationList.stream()
                .filter(reservation -> !reservation.getName().endsWith("_disable"))
                .collect(Collectors.toList());
        return BeanCopyUtil.copy2BeanList(reservationList, SpOVDCBean.class);
    }

    @Override
    public String getAvailableFirstVmName(Integer appSystemId, Integer reservationId) {
        String vmNamePrefix = "";
        SpOVDC reservation = dao.load(SpOVDC.class, reservationId);

        SpVmSearchBean searchBean = new SpVmSearchBean();
        searchBean.setFuzzyBean(new SpVmBean());
        searchBean.getFuzzyBean().setName(vmNamePrefix);
        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
        searchBean.setOrderName1("name");
        searchBean.setOrderBy1(Boolean.FALSE);
        List<ReqVm> reqVmList = dao.query(searchBean, new ReqVm());
        List<SpVm> spVmList = dao.query(searchBean, new SpVm());
        reqVmList = reqVmList.stream()
                .filter(reqVm -> OrderStatus.close_error != reqVm.getOrder().getOrderStatus()
                        && OrderStatus.close_give_up != reqVm.getOrder().getOrderStatus())
                .collect(Collectors.toList());
        int vmNameIndex1 = Utility.isEmpty(reqVmList) ? 0 : Integer.parseInt(ListUtil.first(reqVmList).getName().substring(5));
        int vmNameIndex2 = Utility.isEmpty(spVmList) ? 0 : Integer.parseInt(ListUtil.first(spVmList).getName().substring(5));
        return vmNamePrefix + String.valueOf(10001 + Math.max(vmNameIndex1, vmNameIndex2)).substring(1);
    }

//    @Override
//    public SpIpUsageBean[] getAvailableIpList(Integer appSystemId, Integer reservationId) {
//        Set<Integer> reservationNetworkProfileIdSet = new HashSet<>();
//        Set<String> usedIpSet = new HashSet<>();
//        List<ReqVm> reqVmList = dao.list(ReqVm.class, "reservation.id", reservationId);
//        List<SpVm> spVmList = dao.list(SpVm.class, "reservation.id", reservationId);
//        for (ReqVm reqVm : reqVmList) {
//            if (OrderStatus.close_error == reqVm.getOrder().getOrderStatus()
//                    || OrderStatus.close_give_up == reqVm.getOrder().getOrderStatus()) {
//                continue;
//            }
//            for (ReqVmNetwork network : reqVm.getNetworkList()) {
//                reservationNetworkProfileIdSet.add(network.getIpScope().getId());
//                if (Utility.isNotEmpty(network.getIpAddress())) {
//                    usedIpSet.add(network.getIpAddress());
//                }
//            }
//        }
//        for (SpVm spVm : spVmList) {
//            for (SpVmNetwork network : spVm.getNetworkList()) {
//                reservationNetworkProfileIdSet.add(network.getIpScope().getId());
//                if (Utility.isNotEmpty(network.getIpAddress())) {
//                    usedIpSet.add(network.getIpAddress());
//                }
//            }
//        }
//
//
//        List<SpIpUsage> ipUsageList = dao.list(SpIpUsage.class, "ipScope.id", ListUtil.first(reservationNetworkProfileIdSet));
//        ipUsageList = ipUsageList.stream()
//                .filter(ipUsage -> SpIpStatus.unassigned.equals(ipUsage.getIpStatus()) && !usedIpSet.contains(ipUsage.getIpAddress()))
//                .collect(Collectors.toList());
//        return BeanCopyUtil.copy2BeanList(ipUsageList, SpIpUsageBean.class);
//    }

    @Override
    public SpVappTemplateMachineBean[] getBlueprintMachineList(Integer blueprintId) {
        SpVappTemplate blueprint = dao.load(SpVappTemplate.class, blueprintId);
        LinkedHashMap<Integer, SpVappTemplateMachineBean> blueprintMachineMap = new LinkedHashMap<>();
        Map<Integer, List<SpPropertyGroupBean>> propertyGroupMap = new HashMap<>();
        loadSpBlueprintMachineList(blueprint, blueprintMachineMap, propertyGroupMap);
        for (Integer blueprintMachineRelationId : propertyGroupMap.keySet()) {
            List<SpPropertyGroupBean> propertyGroupList = propertyGroupMap.get(blueprintMachineRelationId);
            blueprintMachineMap.get(blueprintMachineRelationId).setPropertyGroupList(propertyGroupList.toArray(new SpPropertyGroupBean[propertyGroupList.size()]));
        }
        return blueprintMachineMap.values().toArray(new SpVappTemplateMachineBean[blueprintMachineMap.size()]);
    }

    private void loadSpBlueprintMachineList(SpVappTemplate blueprint,
                                            LinkedHashMap<Integer, SpVappTemplateMachineBean> blueprintMachineMap,
                                            Map<Integer, List<SpPropertyGroupBean>> propertyGroupMap) {
    }

    @Override
    public ReqBlueprintDeploymentBean[] getReqBlueprintDeploymentList(Integer applicationId) {
        List<ReqDeployment> reqDeploymentList = dao.list(ReqDeployment.class, "application.id", applicationId);
        reqDeploymentList = reqDeploymentList.stream()
                .collect(Collectors.toList());
        List<ReqBlueprintDeploymentBean> reqBlueprintDeploymentList = new ArrayList<>();
        for (ReqDeployment reqDeployment : reqDeploymentList) {
            loadReqBlueprintDeploymentList(reqDeployment, reqDeployment, reqBlueprintDeploymentList);
        }
        return reqBlueprintDeploymentList.toArray(new ReqBlueprintDeploymentBean[reqBlueprintDeploymentList.size()]);
    }

    private void loadReqBlueprintDeploymentList(final ReqDeployment rootDeployment, ReqDeployment reqDeployment,
                                                List<ReqBlueprintDeploymentBean> reqBlueprintDeploymentList) {
        if (Utility.isNotEmpty(reqDeployment.getVmList())) {
            for (ReqVm reqVm : reqDeployment.getVmList()) {
                ReqBlueprintDeploymentBean bean = BeanCopyUtil.copy(reqVm, ReqBlueprintDeploymentBean.class);
                bean.setRootDeploymentId(rootDeployment.getId());
                reqBlueprintDeploymentList.add(bean);
            }
        }
    }

    @Override
    public void saveReqBlueprintDeploymentList(Integer applicationId, ReqBlueprintDeploymentBean[] blueprintDeploymentList) {
        List<ReqBlueprintDeploymentBean> reqBlueprintDeploymentBeanList = new ArrayList<>(Arrays.asList(blueprintDeploymentList));
        Map<Integer, SpVappTemplateMachineBean[]> blueprintMachineMap = new HashMap<>();
        Map<Integer, List<List<ReqBlueprintDeploymentBean>>> blueprintDeploymentMap = new LinkedHashMap<>();
        while (!reqBlueprintDeploymentBeanList.isEmpty()) {
            ReqBlueprintDeploymentBean bean = reqBlueprintDeploymentBeanList.remove(0);
            List<List<ReqBlueprintDeploymentBean>> dataList = blueprintDeploymentMap.computeIfAbsent(bean.getBlueprintId(), blueprintId -> new ArrayList<>());
            SpVappTemplateMachineBean[] beanList = blueprintMachineMap.computeIfAbsent(bean.getBlueprintId(), this::getBlueprintMachineList);
            if (Utility.isEmpty(beanList) || beanList.length == 1) {
                dataList.add(Collections.singletonList(bean));
            } else {
                List<ReqBlueprintDeploymentBean> beanList1 = new ArrayList<>();
                beanList1.add(bean);
                for (int i = 0; i < reqBlueprintDeploymentBeanList.size(); i++) {
                    if (reqBlueprintDeploymentBeanList.get(i).getBlueprintId().equals(bean.getBlueprintId())
                            && Utility.equals(reqBlueprintDeploymentBeanList.get(i).getRootDeploymentId(), bean.getRootDeploymentId())) {
                        beanList1.add(reqBlueprintDeploymentBeanList.remove(i--));
                    }
                }
                AssertUtil.check(beanList.length == beanList1.size(), "多机蓝图提交的虚机数量不对");
                dataList.add(beanList1);
            }
        }
        UpOrder order = dao.load(UpOrder.class, applicationId);
        for (Integer blueprintId : blueprintDeploymentMap.keySet()) {
            for (List<ReqBlueprintDeploymentBean> deploymentList : blueprintDeploymentMap.get(blueprintId)) {
                ReqBlueprintDeploymentBean bean = deploymentList.get(0);
                if (UpOperateType.update.equals(bean.getOperateType()) || UpOperateType.delete.equals(bean.getOperateType())) {
                    dao.delete(ReqDeployment.class, bean.getRootDeploymentId());
                }
                if (UpOperateType.add.equals(bean.getOperateType()) || UpOperateType.update.equals(bean.getOperateType())) {
                    SpVappTemplate blueprint = dao.load(SpVappTemplate.class, blueprintId);
                    Map<Integer, ReqVm> reqVmMap = new HashMap<>();
                    ReqDeployment reqDeployment = buildReqDeployment(order, null, blueprint, deploymentList, reqVmMap);
                    if (Utility.isNotEmpty(reqVmMap)) {
                        Set<String> vmNameSet = reqVmMap.values().stream().map(BaseEntity::getName).collect(Collectors.toSet());
                        AssertUtil.check(vmNameSet.size() == reqVmMap.size(), "虚机名称有重复");
                        dao.validateDuplicateName(ReqVm.class, vmNameSet);
                        dao.validateDuplicateName(SpVm.class, vmNameSet);
                    }
                    dao.insert(reqDeployment);
                }
            }
        }
    }

    private ReqDeployment buildReqDeployment(UpOrder order,
                                             SpVappTemplateRelation blueprintRelation,
                                             SpVappTemplate vappTemplate,
                                             List<ReqBlueprintDeploymentBean> deploymentList,
                                             Map<Integer, ReqVm> reqVmMap) {
        ReqDeployment reqDeployment = new ReqDeployment();
        reqDeployment.setOrder(order);
        reqDeployment.setUuid(UUID.randomUUID().toString());
        reqDeployment.setName(vappTemplate.getName());
        reqDeployment.setSpOrg(vappTemplate.getSpOrg());
        reqDeployment.setVappTemplate(vappTemplate);
        reqDeployment.setOwner(order.getOwner());

//            for (SpVappTemplateRelation relation : blueprint.getRelationList()) {
//                if (relation.getBlueprintComponent() != null) {
//                    if (reqDeployment.getChildList() == null) {
//                        reqDeployment.setChildList(new ArrayList<>());
//                    }
//                    reqDeployment.getChildList().add(buildReqDeployment(application, relation, relation.getBlueprintComponent(), deploymentList, reqVmMap));
//                } else if (relation.getBlueprintMachine() != null) {
//                    for (ReqBlueprintDeploymentBean bean : deploymentList) {
//                        if (bean.getBlueprintMachineRelationId().equals(relation.getId())) {
//                            ReqVm reqVm = reqVmMap.computeIfAbsent(bean.getBlueprintMachineRelationId(), blueprintMachineRelationId -> new ReqVm());
//                            reqVm.setInstanceNum(1);
//                            reqVm.setBlueprintMachine(relation.getBlueprintMachine());
//                            reqVm.setName(bean.getName());
//                            reqVm.setVmComment(bean.getVmComment());
//                            reqVm.setSpOrg(relation.getSpOrg());
//                            reqVm.setOwner(application.getOwner());
//                            reqVm.setBlueprintMachineRelation(relation);
//                            reqVm.setCpuNum(bean.getCpuNum());
//                            reqVm.setMemoryGB(bean.getMemoryGB());
//                            reqVm.setDiskGB(bean.getDiskGB());
//                            List<ReqVmDisk> reqVmDiskList = Arrays.stream(bean.getDiskList())
//                                    .map(vmDiskBean -> BeanCopyUtil.copy(vmDiskBean, ReqVmDisk.class))
//                                    .collect(Collectors.toList());
//                            reqVm.setDiskList(reqVmDiskList);
//                            List<ReqVmNetwork> reqVmNetworkList = Arrays.stream(bean.getNetworkList())
//                                    .map(vmNetworkBean -> BeanCopyUtil.copy(vmNetworkBean, ReqVmNetwork.class))
//                                    .collect(Collectors.toList());
//                            reqVm.setNetworkList(reqVmNetworkList);
//                            if (reqDeployment.getVmList() == null) {
//                                reqDeployment.setVmList(new ArrayList<>());
//                            }
//                            reqDeployment.getVmList().add(reqVm);
//                            break;
//                        }
//                    }
//                } else if (relation.getPropertyGroup() != null) {
//                    for (ReqBlueprintDeploymentBean bean : deploymentList) {
//                        if (bean.getBlueprintMachineRelationId().equals(relation.getId())) {
//                            for (ReqPropertyGroupBean propertyGroupBean : bean.getPropertyGroupList()) {
//                                if (propertyGroupBean.getId().equals(relation.getPropertyGroup().getId())) {
//                                    ReqPropertyGroup propertyGroup = BeanCopyUtil.copy(propertyGroupBean, ReqPropertyGroup.class);
//                                    propertyGroup.setReqDeploymentUuid(reqDeployment.getUuid());
//                                    List<ReqPropertyItem> propertyItemList = new ArrayList<>();
//                                    for (ReqPropertyItemBean propertyItemBean : propertyGroupBean.getPropertyItemList()) {
//                                        propertyItemList.add(BeanCopyUtil.copy(propertyItemBean, ReqPropertyItem.class));
//                                    }
//                                    propertyGroup.setPropertyItemList(propertyItemList);
//                                    ReqVm reqVm = reqVmMap.computeIfAbsent(bean.getBlueprintMachineRelationId(), blueprintMachineRelationId -> new ReqVm());
//                                    if (reqVm.getPropertyGroupList() == null) {
//                                        reqVm.setPropertyGroupList(new ArrayList<>());
//                                    }
//                                    reqVm.getPropertyGroupList().add(propertyGroup);
//                                }
//                            }
//                            break;
//                        }
//                    }
//                } 
//            }
        return reqDeployment;
    }
}
