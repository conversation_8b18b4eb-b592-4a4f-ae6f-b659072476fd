package io.aicloudware.portal.framework.remote;

import io.aicloudware.portal.framework.bean.BaseBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.utility.FileUtil;
import io.aicloudware.portal.framework.executor.IExecutorA;
import io.aicloudware.portal.framework.utility.Utility;
import org.apache.http.Header;
import org.apache.http.auth.Credentials;
import org.apache.http.client.CookieStore;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

public class RemoteHost extends BaseBean {
    private String host;
    private final String charset;
    private final Credentials credentials;
    private List<Header> headerList;
    private CookieStore cookieStore;

    public RemoteHost(String host) {
        this(host, FileUtil.UTF8, null);
    }

    public RemoteHost(String host, String charset, Credentials credentials) {
        this.host = Utility.toEmpty(host).toLowerCase();
        this.credentials = credentials;
        this.charset = charset;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getCharset() {
        return charset;
    }

    public Credentials getCredentials() {
        return credentials;
    }

    public List<Header> getHeaderList() {
        return headerList;
    }

    public void setHeaderList(List<Header> headerList) {
        this.headerList = headerList;
    }

    public CookieStore getCookieStore() {
        return cookieStore;
    }

    public void setCookieStore(CookieStore cookieStore) {
        this.cookieStore = cookieStore;
    }

    public String get(String uri) {
        StringResponseHandler handler = new StringResponseHandler(charset);
        return RemoteUtil.get(this, uri, handler);
    }

    public void get(String uri, IExecutorA<InputStream> callback) {
        InputStreamResponseHandler handler = new InputStreamResponseHandler(callback);
        RemoteUtil.get(this, uri, handler);
    }

    public void download(String uri, OutputStream outputStream) {
        OutputStreamResponseHandler handler = new OutputStreamResponseHandler(outputStream);
        RemoteUtil.get(this, uri, handler);
    }

    public void download(String uri, Map<String, String> formDataMap, OutputStream outputStream) {
        OutputStreamResponseHandler handler = new OutputStreamResponseHandler(outputStream);
        RemoteUtil.post(this, uri, formDataMap, null, handler);
    }

    public String upload(String uri, Map<String, String> formDataMap, Map<String, File> uploadFileMap) {
        StringResponseHandler handler = new StringResponseHandler(charset);
        return RemoteUtil.post(this, uri, formDataMap, uploadFileMap, handler);
    }

    public String post(String uri, Map<String, String> formDataMap) {
        StringResponseHandler handler = new StringResponseHandler(charset);
        return RemoteUtil.post(this, uri, formDataMap, null, handler);
    }

    public void post(String uri, Map<String, String> formDataMap, IExecutorA<InputStream> callback) {
        InputStreamResponseHandler handler = new InputStreamResponseHandler(callback);
        RemoteUtil.post(this, uri, formDataMap, null, handler);
    }

    public String login(String uri, Map<String, String> formDataMap) {
        LoginResponseHandler handler = new LoginResponseHandler(this);
        return RemoteUtil.post(this, uri, formDataMap, null, handler);
    }

    public String stringGet(String uri) {
        return RemoteUtil.stringGet(this, uri);
    }
    
    public ResponseBean jsonGet(String uri) {
        return RemoteUtil.jsonGet(this, uri);
    }

    public ResponseBean jsonGet(String uri, Object param) {
        return RemoteUtil.jsonGet(this, uri, param);
    }

    public ResponseBean jsonPost(String uri, Object param) {
        return RemoteUtil.jsonPost(this, uri, param);
    }

    public String bodyPostNsx(String uri, String body) {
        return RemoteUtil.bodyPostNsx(this, uri, body, "utf8");
    }

    public String bodyPutNsx(String uri, String body) {
        return RemoteUtil.bodyPutNsx(this, uri, body, "utf8");
    }


    public String stringJsonPost(String uri, Object param) {
        return RemoteUtil.stringJsonPost(this, uri, param);
    }
    
    public ResponseBean jsonPut(String uri, Object param) {
        return RemoteUtil.jsonPut(this, uri, param);
    }

    public ResponseBean jsonDelete(String uri) {
        return RemoteUtil.jsonDelete(this, uri);
    }
}
