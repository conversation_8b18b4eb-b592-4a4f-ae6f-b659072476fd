package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmListBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightListBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.TreeMap;

@Service
@Transactional
public interface IUpRightService {

    public void createWebMenu(UpRightListBean bean);

    public void createRight(UpRightListBean bean);

    public void deleteRight(List<Integer> rightIdList);

    public UpRightBean getRight(Integer rightId);

    public TreeMap<Integer, String> findRoleMap(Integer userId);

    public TreeMap<Integer, String> findUserMap(Integer roleId);

    public List<UpRightBean> findUserRightList();

    public List<UpRightBean> findTenantRightList();

    public boolean checkBlueprintView(SpVappTemplateBean bean);

    public void checkDeploymentLifecycleLow(SpSimpleOperateBean bean);

    public void checkDeploymentLifecycleHigh(SpSimpleOperateBean bean);

    public void checkVmPower(SpSimpleOperateBean bean);

    public void checkVmSnapshot(SpSimpleOperateBean bean);

    public void checkVmConsole(Integer vmId);

    public void checkVmChangeResource(SpVmListBean bean);

    public void clearCache();

}
