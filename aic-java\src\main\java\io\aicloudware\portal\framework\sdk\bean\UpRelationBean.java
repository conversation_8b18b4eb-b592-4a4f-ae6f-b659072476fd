package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "授权关系")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpRelationBean.class})
public class UpRelationBean extends RecordBean {

    @ApiModelProperty(value = "关系类型")
    private UpRelationType type;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户显示名称")
    private String userDisplayName;

    @ApiModelProperty(value = "组ID")
    private Integer groupId;

    @ApiModelProperty(value = "组名称")
    private String groupName;

    @ApiModelProperty(value = "角色ID")
    private Integer roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "子角色ID")
    private Integer subRoleId;

    @ApiModelProperty(value = "子角色名称")
    private String subRoleName;

    @ApiModelProperty(value = "权限ID")
    private Integer rightId;

    @ApiModelProperty(value = "权限名称")
    private String rightName;

    public UpRelationType getType() {
        return type;
    }

    public void setType(UpRelationType type) {
        this.type = type;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getSubRoleId() {
        return subRoleId;
    }

    public void setSubRoleId(Integer subRoleId) {
        this.subRoleId = subRoleId;
    }

    public String getSubRoleName() {
        return subRoleName;
    }

    public void setSubRoleName(String subRoleName) {
        this.subRoleName = subRoleName;
    }

    public Integer getRightId() {
        return rightId;
    }

    public void setRightId(Integer rightId) {
        this.rightId = rightId;
    }

    public String getRightName() {
        return rightName;
    }

    public void setRightName(String rightName) {
        this.rightName = rightName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UpRelationBean bean = (UpRelationBean) o;

        if (type != bean.type) return false;
        if (userId != null ? !userId.equals(bean.userId) : bean.userId != null) return false;
        if (groupId != null ? !groupId.equals(bean.groupId) : bean.groupId != null) return false;
        if (roleId != null ? !roleId.equals(bean.roleId) : bean.roleId != null) return false;
        if (subRoleId != null ? !subRoleId.equals(bean.subRoleId) : bean.subRoleId != null) return false;
        if (rightId != null ? !rightId.equals(bean.rightId) : bean.rightId != null) return false;

        return true;
    }

    public String getUserDisplayName() {
        return userDisplayName;
    }

    public void setUserDisplayName(String userDisplayName) {
        this.userDisplayName = userDisplayName;
    }

}
