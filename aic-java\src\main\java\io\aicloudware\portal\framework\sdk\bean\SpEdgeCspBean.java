package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpIpStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "EdgeCSP接口")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpEdgeCspBean extends SpRecordBean {

	@ApiModelProperty(value = "edgeId")
	private String edgeId;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "prefix")
    private String prefixLen;

	@ApiModelProperty(value = "分段名称")
	private String segmentName;

    @ApiModelProperty(value = "IP状态")
    private SpIpStatus ipStatus;



    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public SpIpStatus getIpStatus() {
        return ipStatus;
    }

    public void setIpStatus(SpIpStatus status) {
        this.ipStatus = status;
    }

	public String getEdgeId() {
		return edgeId;
	}

	public void setEdgeId(String edgeId) {
		this.edgeId = edgeId;
	}

	public String getSegmentName() {
		return segmentName;
	}

	public void setSegmentName(String segmentName) {
		this.segmentName = segmentName;
	}

    public String getPrefixLen() {
        return prefixLen;
    }

    public void setPrefixLen(String prefixLen) {
        this.prefixLen = prefixLen;
    }
}
