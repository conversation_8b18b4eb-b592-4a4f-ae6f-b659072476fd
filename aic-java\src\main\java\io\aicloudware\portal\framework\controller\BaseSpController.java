package io.aicloudware.portal.framework.controller;


import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.entity.ISpResourceEntity;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.api_up.service.IUpApplicationService;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SpDataListBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import io.aicloudware.portal.framework.executor.IExecutorA;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;

import java.util.ArrayList;
import java.util.List;

@Controller
public abstract class BaseSpController<E extends ISpResourceEntity<B>, R extends IRequestEntity, B extends RecordBean, RL extends ResultListBean<B>>
        extends BaseEntityController<E, B, RL> {

    @Autowired
    protected IUpApplicationService applicationService;

    protected Class<R> getRequestType() {
        return (Class<R>) types[1];
    }

    protected Class<B> getBeanType() {
        return (Class<B>) types[2];
    }

    protected Class<RL> getResultType() {
        return (Class<RL>) types[3];
    }

    protected abstract UpApplicationType getAddType();

    protected abstract UpApplicationType getUpdateType();

    protected abstract UpApplicationType getDeleteType();

    protected ResponseBean createAddApplication(SpDataListBean<B> bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        List<R> entityList = new ArrayList<>(bean.getDataList().length);
        for (B b : bean.getDataList()) {
            entityList.add(BeanCopyUtil.copy(b, getRequestType()));
        }
        UpApplicationBean applicationBean = applicationService.createAddApplication(getAddType(), entityList, bean, getEntityType());
        return ResponseBean.success(applicationBean);
    }

    protected ResponseBean createUpdateApplication(SpDataListBean<B> bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        List<R> entityList = new ArrayList<>(bean.getDataList().length);
        for (B b : bean.getDataList()) {
            entityList.add(BeanCopyUtil.copy(b, getRequestType()));
        }
        UpApplicationBean applicationBean = applicationService.createUpdateApplication(getUpdateType(), entityList, bean, getEntityType());
        return ResponseBean.success(applicationBean);
    }

    protected ResponseBean createDeleteApplication(SpSimpleOperateBean bean, BindingResult bindingResult) {
        handleValidateResult(bindingResult);
        bean.setApplicationType(getDeleteType());
        return createSimpleOperateApplication(bean, null);
    }

    protected ResponseBean createSimpleOperateApplication(SpSimpleOperateBean bean, IExecutorA<R> executor) {
        UpApplicationBean applicationBean = applicationService.createSimpleOperateApplication(getEntityType(), getRequestType(), bean, executor);
        return ResponseBean.success(applicationBean);
    }

    protected ResponseBean createSimpleOperateTask(UpTaskType taskType, Integer id) {
        B bean = doLoad(id);
        UpTaskBean taskBean = applicationService.createSimpleOperateTask(taskType, bean.getName(), id);
        return ResponseBean.success(taskBean);
    }
}
