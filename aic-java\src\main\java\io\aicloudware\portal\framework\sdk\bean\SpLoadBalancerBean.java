package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "资源负载均衡")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerBean.class})
public class SpLoadBalancerBean extends SpRecordBean {

    @ApiModelProperty(value = "组织ID")
    private Integer spOrgId;
    
    @ApiModelProperty(value = "已启用")
    private Boolean enabled;
    
    @ApiModelProperty(value = "已启用加速")
    private Boolean accelerated;
    
    @ApiModelProperty(value = "禁用日志记录")
    private Boolean disableLog;
    
    @ApiModelProperty(value = "日志级别")
    private String logLevel;

    @ApiModelProperty(value = "池")
    private SpLoadBalancerPoolBean[] loadBalancerPoolList;
    
    @ApiModelProperty(value = "虚拟机服务器")
    private SpLoadBalancerVirtualServerBean[] loadBalancerVirutalServerList;

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Boolean getAccelerated() {
		return accelerated;
	}

	public void setAccelerated(Boolean accelerated) {
		this.accelerated = accelerated;
	}

	public Boolean getDisableLog() {
		return disableLog;
	}

	public void setDisableLog(Boolean disableLog) {
		this.disableLog = disableLog;
	}

	public String getLogLevel() {
		return logLevel;
	}

	public void setLogLevel(String logLevel) {
		this.logLevel = logLevel;
	}

	public SpLoadBalancerPoolBean[] getLoadBalancerPoolList() {
		return loadBalancerPoolList;
	}

	public void setLoadBalancerPoolList(SpLoadBalancerPoolBean[] loadBalancerPoolList) {
		this.loadBalancerPoolList = loadBalancerPoolList;
	}

	public SpLoadBalancerVirtualServerBean[] getLoadBalancerVirutalServerList() {
		return loadBalancerVirutalServerList;
	}

	public void setLoadBalancerVirutalServerList(SpLoadBalancerVirtualServerBean[] loadBalancerVirutalServerList) {
		this.loadBalancerVirutalServerList = loadBalancerVirutalServerList;
	}

	
    
}
