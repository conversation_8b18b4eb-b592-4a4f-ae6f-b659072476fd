package io.aicloudware.portal.api_vcpp.controller.profile;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_vcpp.entity.UpMessage;
import io.aicloudware.portal.api_vcpp.service.profile.IUpMessageService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.profile.UpMessageBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpMessageResultBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpMessageSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpMessageStatus;
import io.aicloudware.portal.framework.sdk.contants.UpMessageType;

/**
 * 消息
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/message")
public class UpMessageController extends BaseUpController<UpMessage, UpMessageBean, UpMessageResultBean> {

	@Autowired
	private IUpMessageService messageService;

	/**
	 * 配置
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/init")
	@ResponseBody
	public ResponseBean init(HttpServletRequest request) {
		Map<String, Map<String, ?>> datas = new HashMap<>();
		// 消息类型
		Map<String, String> messageType = new LinkedHashMap<String, String>();
		for (UpMessageType type : UpMessageType.values()) {
			messageType.put(type.toString(), type.getTitle());
		}
		datas.put("messageType", messageType);

		// 消息状态
		Map<String, String> messageStatus = new LinkedHashMap<String, String>();
		for (UpMessageStatus type : UpMessageStatus.values()) {
			messageStatus.put(type.toString(), type.getTitle());
		}
		datas.put("messageStatus", messageStatus);
		return ResponseBean.success(datas);
	}
	
	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/query", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean query(@RequestBody UpMessageSearchBean searchBean, HttpServletRequest request) {
		UpMessageBean bean = searchBean.getBean();
		if (bean == null) {
			bean = new UpMessageBean();
		}else if(StringUtils.isNotEmpty(bean.getName())) {
			UpMessageBean fuzzyBean = new UpMessageBean();
			fuzzyBean.setName(bean.getName());
			searchBean.setFuzzyBean(fuzzyBean);
		}
		bean.setName(null);
		bean.setUserId(ThreadCache.getUserId());
		searchBean.setBean(bean);
		return this.queryEntity(searchBean);
	}
	
	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/countNewMsg", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean countNewMsg(HttpServletRequest request) {
		return ResponseBean.success(messageService.countNewMsg(ThreadCache.getUserId()));
	}

	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/detail", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean detail(@RequestBody Integer id, HttpServletRequest request) {
		Integer userId = ThreadCache.getUserId();
		return ResponseBean.success(messageService.detail(id,userId));
	}

	/**
	 * 更新
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateToRead", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean updateToRead(@RequestBody Integer id, HttpServletRequest request) {
		Integer userId = ThreadCache.getUserId();
		return ResponseBean.success(messageService.updateToRead(id,userId));
	}

}