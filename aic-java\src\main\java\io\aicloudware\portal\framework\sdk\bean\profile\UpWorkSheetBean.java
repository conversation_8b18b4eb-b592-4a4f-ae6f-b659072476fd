package io.aicloudware.portal.framework.sdk.bean.profile;

import java.util.Date;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetStatus;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "工单")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class})
public final class UpWorkSheetBean extends RecordBean {

	@ApiModelProperty(value = "code")
	private String code;
	
	@ApiModelProperty(value = "工单所有人ID")
	private Integer userId;

	@ApiModelProperty(value = "受理客服")
	private UpUser serviceUser;
	
	@ApiModelProperty(value = "工单状态")
	private UpWorkSheetStatus workSheetStatus;
	
	@ApiModelProperty(value = "工单类型")
	private UpWorkSheetType type;
	
	@ApiModelProperty(value = "工单明细")
	private UpWorkSheetDetailBean[] detailList;

    @ApiModelProperty(value = "提交时间 ")
    private Date createTm;
    
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public UpUser getServiceUser() {
		return serviceUser;
	}

	public void setServiceUser(UpUser serviceUser) {
		this.serviceUser = serviceUser;
	}

	public UpWorkSheetStatus getWorkSheetStatus() {
		return workSheetStatus;
	}

	public void setWorkSheetStatus(UpWorkSheetStatus workSheetStatus) {
		this.workSheetStatus = workSheetStatus;
	}

	public UpWorkSheetType getType() {
		return type;
	}

	public void setType(UpWorkSheetType type) {
		this.type = type;
	}

	public UpWorkSheetDetailBean[] getDetailList() {
		return detailList;
	}

	public void setDetailList(UpWorkSheetDetailBean[] detailList) {
		this.detailList = detailList;
	}

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
    
    
}