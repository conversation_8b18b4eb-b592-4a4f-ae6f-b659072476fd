package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpStorageType;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpStorageBean.class})
public class SpStorageBean extends SpRecordBean {

    @ApiModelProperty(value = "端点ID")
    private Integer endpointId;

    @ApiModelProperty(value = "端点名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String endpointName;

    @ApiModelProperty(value = "端点显示名称")
    private String endpointDisplayName;

    @ApiModelProperty(value = "存储容量(GB)")
    private Integer physicalGB;

    @ApiModelProperty(value = "存储已使用容量(GB)")
    private Integer usedGB;

    @ApiModelProperty(value = "类型")
    private SpStorageType type;

    public Integer getEndpointId() {
        return endpointId;
    }

    public void setEndpointId(Integer endpointId) {
        this.endpointId = endpointId;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public Integer getPhysicalGB() {
        return physicalGB;
    }

    public void setPhysicalGB(Integer physicalGB) {
        this.physicalGB = physicalGB;
    }

    public Integer getUsedGB() {
        return usedGB;
    }

    public void setUsedGB(Integer usedGB) {
        this.usedGB = usedGB;
    }

    public SpStorageType getType() {
        return type;
    }

    public void setType(SpStorageType type) {
        this.type = type;
    }

    public String getEndpointDisplayName() {
        return endpointDisplayName;
    }

    public void setEndpointDisplayName(String endpointDisplayName) {
        this.endpointDisplayName = endpointDisplayName;
    }

}
