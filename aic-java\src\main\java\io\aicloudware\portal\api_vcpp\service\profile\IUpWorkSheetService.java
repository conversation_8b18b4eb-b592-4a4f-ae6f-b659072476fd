package io.aicloudware.portal.api_vcpp.service.profile;

import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetDetailBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetResultBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetSearchBean;

public interface IUpWorkSheetService {

	public UpWorkSheetBean detail(Integer workSheetId, Integer userId);

	public UpWorkSheetDetailBean save(UpWorkSheetDetailBean bean, Integer userId);

	public UpWorkSheetBean update(UpWorkSheetBean bean, Integer userId);

	public UpWorkSheetResultBean query(UpWorkSheetSearchBean searchBean, Integer userId);

}
