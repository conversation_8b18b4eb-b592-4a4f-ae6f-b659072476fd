package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpInstanceIndexBean;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.SpServiceCatalog;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "up_instance_index")
@Access(AccessType.FIELD)
public class UpInstanceIndex extends BaseUpEntity<UpInstanceIndexBean>{

	@JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

	@JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @Column(name = "instance_id")
	private Integer instanceId;

    @Column(name = "service_type")
    @Enumerated(EnumType.STRING)
	private SpService serviceType;

    @Column(name = "service_catalog")
    @Enumerated(EnumType.STRING)
    private SpServiceCatalog serviceCatalog;

    @Column(name = "instance_create_tm")
    private Date instanceCreateTm;

    @Column(name = "instance_update_tm")
    private Date instanceUpdateTm;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public Integer getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Integer instanceId) {
        this.instanceId = instanceId;
    }

    public SpService getServiceType() {
        return serviceType;
    }

    public void setServiceType(SpService serviceType) {
        this.serviceType = serviceType;
    }

    public SpServiceCatalog getServiceCatalog() {
        return serviceCatalog;
    }

    public void setServiceCatalog(SpServiceCatalog serviceCatalog) {
        this.serviceCatalog = serviceCatalog;
    }

    public Date getInstanceCreateTm() {
        return instanceCreateTm;
    }

    public void setInstanceCreateTm(Date instanceCreateTime) {
        this.instanceCreateTm = instanceCreateTime;
    }

    public Date getInstanceUpdateTm() {
        return instanceUpdateTm;
    }

    public void setInstanceUpdateTm(Date instanceUpdateTm) {
        this.instanceUpdateTm = instanceUpdateTm;
    }
}
