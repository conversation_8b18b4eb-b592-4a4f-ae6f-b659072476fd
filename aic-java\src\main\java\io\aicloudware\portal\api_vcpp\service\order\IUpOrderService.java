package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBackupSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductSnapshotSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CatalogType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.platform_vcd.entity.*;

import java.util.List;
import java.util.Map;

public interface IUpOrderService {

	/**
	 * 虚机模板
	 * @return
	 */
	Map<CatalogType,List<Map<String,Object>>> getTemplatesByCatalog(CatalogType... types);
	
	/**
	 * 虚机模板，支持私有镜像
	 * @return
	 */
	public List<Map<String,Object>> getTemplatesByCatalog(SpRegionEntity region, Integer orgId, CatalogType... types);
	/**
	 * 查询未绑弹性公网IP/负载均衡的云服务器
	 * @param userId
	 * @return
	 */
	public List<SpVmBean> queryUnbindSpVm(Integer userId);
	
	/**
	 * 查询用户的云服务器
	 * @param userId
	 * @return
	 */
	public List<SpVmBean> querySpVm(Integer userId);
	
	/**
	 * 查询未绑定的负载均衡
	 * @param userId
	 * @return
	 */
	public List<SpLoadBalancerVirtualServerBean> queryUnbindLoadbalance(Integer userId);
	
	/**
	 * VPC
	 * @param userId
	 * @return
	 */
	public List<Map<String,Object>> getVPC(Integer userId);
	
	/**
	 * 查询未绑定的IP
	 * @param userId
	 * @return
	 */
	public List<SpElasticIpBean> queryUnbindIp(Integer userId);
	
	/**
	 * 查询未使用的负载均衡VIP
	 * @param userId
	 * @return
	 */
	public List<SpLoadBalancerVipBean> queryUnbindLBVip(Integer userId);
	
	/**
	 * 
	 * @param snapshot
	 */
	@Deprecated
	public Integer createSnapshotOrder(SpSnapshot snapshot, UpProductSnapshotSetBean snapshotSetBean);

	@Deprecated
	public Integer createBackupOrder(SpVm vm, UpProductBackupSetBean backupSetBean);
	
	/**
	 * 校验未完成订单
	 * @param type
	 * @param userId
	 * @return
	 */
	public Integer queryActiveOrder(OrderType type,Integer userId);

	/**
	 * 云主机操作
	 * @param
	 * @param type
	 * @return
	 */
	public Integer createSimpleOrderCloudServer(SpVm[] vms, OrderType type);
	
	/**
	 * 快照操作
	 * @param
	 * @param type
	 * @return
	 */
	public Integer createSimpleOrderSnapshot(SpSnapshot[] snapshots, OrderType type);
	
	/**
	 * 备份操作
	 * @param
	 * @param type
	 * @return
	 */
	public Integer createSimpleOrderBackup(SpVm[] backups, OrderType type, SpVmBackupStrategy backupStrategy);

	/**
	 * wenj操作
	 * @param
	 * @param type
	 * @return
	 */
	public Integer createSimpleOrderFileStorage(SpFileStorage[] vpns, OrderType type);

	/**
	 * bucket操作
	 * @param
	 * @param type
	 * @return
	 */
	public Integer createSimpleOrderObjectStroageBucket(SpObjectStorageBucket[] objectStorageBuckets, OrderType type);

	/**
	 * 创建私有镜像
	 * @param vm
	 * @param imageName
	 * @return
	 */
	public Integer createPrivateImgOrder(SpVm vm, SpCatalog catalog, String imageName);

	/**
	 * 删除私有镜像
	 * @param vappTemplate
	 * @return
	 */
	public Integer createPrivateImageDeleteOrder(SpVappTemplate vappTemplate);

	Integer createSimpleOrderK8sCluster(SpK8sCluster[] clusters, OrderType type);

	Integer createSimpleOrderK8sClusterNodePool(SpK8sClusterNodePool[] nodePools, OrderType type);

	public UpOrderBean[] query(UpOrderSearchBean searchBean);

    void update(UpOrderBean bean);

	void redepoly(Integer integer);
}