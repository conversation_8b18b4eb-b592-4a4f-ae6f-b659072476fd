package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpPermission;
import io.aicloudware.portal.api_up.service.IUpPermissionService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.UpPermissionType;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/permission")
@Api(value = "/permission", description = "权限", position = 508)
public class UpPermissionController extends BaseUpController<UpPermission, UpPermissionBean, UpPermissionResultBean> {

    @Autowired
    private IUpPermissionService upPermissionService;

    @RequestMapping(value = "/admin/queryUserTemplate", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/queryUserTemplate", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpPermissionResultBean.class)})
    @ResponseBody
    public ResponseBean queryUserTemplate(@ApiParam(value = "查询条件") @RequestBody UpPermissionSearchBean searchBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        UpPermissionBean[] beans = upPermissionService.queryUserTemplate(searchBean);
        UpPermissionResultBean result = new UpPermissionResultBean();
        super.fillPageInfo(searchBean, result);
        result.setDataList(beans);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/admin/updateUserTemplate", method = RequestMethod.PUT)
    @ApiOperation(notes = "/admin/updateUserTemplate", httpMethod = "PUT", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpPermissionResultBean.class)})
    @ResponseBody
    public ResponseBean saveUserTemplate(@RequestBody UpPermissionBean bean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        upPermissionService.updateUserTemplate(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/admin/listPermissionByTemplate/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/admin/listPermissionByTemplate/{id}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean listPermissionByTemplate(@PathVariable Integer id) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        UpPermissionBean[] beans = upPermissionService.listPermissionByTemplate(id);
        return ResponseBean.success(beans);
    }

    @RequestMapping(value = "/admin/saveUserPermission", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/saveUserPermission", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = Boolean.class)})
    @ResponseBody
    public ResponseBean saveUserPermission(@ApiParam(value = "查询条件") @RequestBody UpPermissionListBean listBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        this.upPermissionService.saveUserPermission(listBean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/admin/savePermissionByUserId", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/savePermissionByUserId", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = Boolean.class)})
    @ResponseBody
    public ResponseBean savePermissionByUserId(@ApiParam(value = "查询条件") @RequestBody UpPermissionListBean listBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        this.upPermissionService.savePermissionByUserId(listBean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/user/list/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/user/list/{id}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean userList(@PathVariable Integer id) {
        return ResponseBean.success(this.upPermissionService.userList(id));
    }

    @RequestMapping(value = "/initService", method = RequestMethod.GET)
    @ApiOperation(notes = "/initService", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpPermissionResultBean.class)})
    @ResponseBody
    public ResponseBean initService() {
        return ResponseBean.success(ListUtil.map(Arrays.asList(SpService.values()), (map, service) -> {
//            map.put(service.getTitle(), Arrays.stream(service.getPermissionTypes()).map(UpPermissionType::name).collect(Collectors.toList()));
            map.put(service.getTitle(), Arrays.stream(service.getPermissionTypes()).map(
                    permissionType -> {
                        UpPermissionBean bean = new UpPermissionBean();
                        bean.setPermissionType(permissionType);
                        bean.setPermissionTypeText(permissionType.getTitle());
                        bean.setService(service);
                        return bean;
                    }).collect(Collectors.toList()));
        }));
    }

}
