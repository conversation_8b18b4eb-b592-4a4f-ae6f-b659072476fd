package io.aicloudware.portal.api_up.service;

import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;

@Transactional
public interface IUpOperationLogService {

    public <E extends IEntity> void saveSuccess(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                                Integer targetId, String targetName, UpOrder order);

    public <E extends IEntity> void saveError(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                              Integer targetId, String targetName, String errorMsg, UpOrder order);

    public <E extends IEntity> void saveOperationLog(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                                     Integer targetId, String targetName, UpOrder order);

}
