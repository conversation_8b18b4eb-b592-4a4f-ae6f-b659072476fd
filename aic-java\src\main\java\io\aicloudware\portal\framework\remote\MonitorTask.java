package io.aicloudware.portal.framework.remote;

import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.executor.IExecutorA;
import io.aicloudware.portal.framework.utility.Utility;

public class MonitorTask {
    private final String title;
    private long length;
    private final IExecutorA<String> executor;

    private long bCount = 0;
    private long kbCount = 0;

    public MonitorTask(String title, Long length, IExecutorA<String> executor) {
        this.title = title;
        this.executor = executor;
        setLength(length);
    }

    public void setLength(Long length) {
        this.length = Math.max(Utility.toZero(length), 0);
    }

    public void add() {
        bCount++;
        if (kbCount != bCount / 1024) {
            kbCount = bCount / 1024;
            String message = title + bCount / 1024 + " K";
            if (length != 0) {
                message += " / " + (length / 1024) + " K  -  "
                        + FormatUtil.formatPercent(bCount * 10000d / length);
            }
            executor.doExecute(message);
        }
    }
}
