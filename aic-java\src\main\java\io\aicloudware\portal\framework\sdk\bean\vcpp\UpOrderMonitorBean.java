package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.BalanceArithmetic;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.TransportLayer;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "SLB监听")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderMonitorBean.class})
public class UpOrderMonitorBean extends SpRecordBean {

	@ApiModelProperty(value = "前端协议")
	private TransportLayer frontLayer;
	
	@ApiModelProperty(value = "前端端口号")
	private String frontPort;
	
	@ApiModelProperty(value = "均衡算法")
	private BalanceArithmetic balanceArithmetic;
	
	@ApiModelProperty(value = "后端协议")
	private TransportLayer backendLayer;
	
	@ApiModelProperty(value = "后端端口号")
	private String backendPort;
	
	@ApiModelProperty(value = "负载均衡ID")
	private Integer loadBalanceId;

	@ApiModelProperty(value = "检查间隔")
	private Integer interval;
	
	@ApiModelProperty(value = "超时时间")
	private Integer timeout;
	
	@ApiModelProperty(value = "最大重试次数")
	private Integer maxNumber;
	
	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	public TransportLayer getFrontLayer() {
		return frontLayer;
	}

	public void setFrontLayer(TransportLayer frontLayer) {
		this.frontLayer = frontLayer;
	}

	public String getFrontPort() {
		return frontPort;
	}

	public void setFrontPort(String frontPort) {
		this.frontPort = frontPort;
	}

	public BalanceArithmetic getBalanceArithmetic() {
		return balanceArithmetic;
	}

	public void setBalanceArithmetic(BalanceArithmetic balanceArithmetic) {
		this.balanceArithmetic = balanceArithmetic;
	}

	public TransportLayer getBackendLayer() {
		return backendLayer;
	}

	public void setBackendLayer(TransportLayer backendLayer) {
		this.backendLayer = backendLayer;
	}

	public String getBackendPort() {
		return backendPort;
	}

	public void setBackendPort(String backendPort) {
		this.backendPort = backendPort;
	}

	public Integer getLoadBalanceId() {
		return loadBalanceId;
	}

	public void setLoadBalanceId(Integer loadBalanceId) {
		this.loadBalanceId = loadBalanceId;
	}

	public Integer getInterval() {
		return interval;
	}

	public void setInterval(Integer interval) {
		this.interval = interval;
	}

	public Integer getTimeout() {
		return timeout;
	}

	public void setTimeout(Integer timeout) {
		this.timeout = timeout;
	}

	public Integer getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(Integer maxNumber) {
		this.maxNumber = maxNumber;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

}
