package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "蓝图关系表")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVappTemplateRelationBean.class})
public class SpVappTemplateRelationBean extends SpRecordBean {

    @ApiModelProperty(value = "蓝图ID")
    private Integer blueprintId;

    @ApiModelProperty(value = "蓝图名称")
    private String blueprintName;

    @ApiModelProperty(value = "蓝图显示名称")
    private String blueprintDisplayName;

    @ApiModelProperty(value = "蓝图组件ID")
    private Integer blueprintComponentId;

    @ApiModelProperty(value = "蓝图组件名称")
    private String blueprintComponentName;

    @ApiModelProperty(value = "蓝图组件显示名称")
    private String blueprintComponentDisplayName;

    @ApiModelProperty(value = "蓝图机器ID")
    private Integer blueprintMachineId;

    @ApiModelProperty(value = "蓝图机器名称")
    private String blueprintMachineName;

    @ApiModelProperty(value = "蓝图机器显示名称")
    private String blueprintMachineDisplayName;

    @ApiModelProperty(value = "属性组ID")
    private Integer propertyGroupId;

    @ApiModelProperty(value = "属性组名称")
    private String propertyGroupName;

    @ApiModelProperty(value = "属性组显示名称")
    private String propertyGroupDisplayName;

    @ApiModelProperty(value = "蓝图软件组件ID")
    private Integer softComponentId;

    @ApiModelProperty(value = "蓝图软件组件名称")
    private String softComponentName;

    @ApiModelProperty(value = "蓝图软件组件显示名称")
    private String softComponentDisplayName;

    @ApiModelProperty(value = "蓝图软件组件路径")
    private String softComponentFullPath;

    @ApiModelProperty(value = "目标所在蓝图机器ID")
    private Integer targetMachineRelationId;

    @ApiModelProperty(value = "目标所在蓝图机器名称")
    private String targetMachineRelationName;

    @ApiModelProperty(value = "目标所在蓝图机器显示名称")
    private String targetMachineRelationDisplayName;

    public Integer getBlueprintId() {
        return blueprintId;
    }

    public void setBlueprintId(Integer blueprintId) {
        this.blueprintId = blueprintId;
    }

    public String getBlueprintName() {
        return blueprintName;
    }

    public void setBlueprintName(String blueprintName) {
        this.blueprintName = blueprintName;
    }

    public String getBlueprintDisplayName() {
        return blueprintDisplayName;
    }

    public void setBlueprintDisplayName(String blueprintDisplayName) {
        this.blueprintDisplayName = blueprintDisplayName;
    }

    public Integer getBlueprintComponentId() {
        return blueprintComponentId;
    }

    public void setBlueprintComponentId(Integer blueprintComponentId) {
        this.blueprintComponentId = blueprintComponentId;
    }

    public String getBlueprintComponentName() {
        return blueprintComponentName;
    }

    public void setBlueprintComponentName(String blueprintComponentName) {
        this.blueprintComponentName = blueprintComponentName;
    }

    public String getBlueprintComponentDisplayName() {
        return blueprintComponentDisplayName;
    }

    public void setBlueprintComponentDisplayName(String blueprintComponentDisplayName) {
        this.blueprintComponentDisplayName = blueprintComponentDisplayName;
    }

    public Integer getBlueprintMachineId() {
        return blueprintMachineId;
    }

    public void setBlueprintMachineId(Integer blueprintMachineId) {
        this.blueprintMachineId = blueprintMachineId;
    }

    public String getBlueprintMachineName() {
        return blueprintMachineName;
    }

    public void setBlueprintMachineName(String blueprintMachineName) {
        this.blueprintMachineName = blueprintMachineName;
    }

    public String getBlueprintMachineDisplayName() {
        return blueprintMachineDisplayName;
    }

    public void setBlueprintMachineDisplayName(String blueprintMachineDisplayName) {
        this.blueprintMachineDisplayName = blueprintMachineDisplayName;
    }

    public Integer getPropertyGroupId() {
        return propertyGroupId;
    }

    public void setPropertyGroupId(Integer propertyGroupId) {
        this.propertyGroupId = propertyGroupId;
    }

    public String getPropertyGroupName() {
        return propertyGroupName;
    }

    public void setPropertyGroupName(String propertyGroupName) {
        this.propertyGroupName = propertyGroupName;
    }

    public String getPropertyGroupDisplayName() {
        return propertyGroupDisplayName;
    }

    public void setPropertyGroupDisplayName(String propertyGroupDisplayName) {
        this.propertyGroupDisplayName = propertyGroupDisplayName;
    }

    public Integer getSoftComponentId() {
        return softComponentId;
    }

    public void setSoftComponentId(Integer softComponentId) {
        this.softComponentId = softComponentId;
    }

    public String getSoftComponentName() {
        return softComponentName;
    }

    public void setSoftComponentName(String softComponentName) {
        this.softComponentName = softComponentName;
    }

    public String getSoftComponentDisplayName() {
        return softComponentDisplayName;
    }

    public void setSoftComponentDisplayName(String softComponentDisplayName) {
        this.softComponentDisplayName = softComponentDisplayName;
    }

    public String getSoftComponentFullPath() {
        return softComponentFullPath;
    }

    public void setSoftComponentFullPath(String softComponentFullPath) {
        this.softComponentFullPath = softComponentFullPath;
    }

    public Integer getTargetMachineRelationId() {
        return targetMachineRelationId;
    }

    public void setTargetMachineRelationId(Integer targetMachineRelationId) {
        this.targetMachineRelationId = targetMachineRelationId;
    }

    public String getTargetMachineRelationName() {
        return targetMachineRelationName;
    }

    public void setTargetMachineRelationName(String targetMachineRelationName) {
        this.targetMachineRelationName = targetMachineRelationName;
    }

    public String getTargetMachineRelationDisplayName() {
        return targetMachineRelationDisplayName;
    }

    public void setTargetMachineRelationDisplayName(String targetMachineRelationDisplayName) {
        this.targetMachineRelationDisplayName = targetMachineRelationDisplayName;
    }
}
