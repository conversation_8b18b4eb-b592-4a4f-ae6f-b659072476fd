package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "用户")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpUserLdapBean.class})
public class UpUserLdapBean extends RecordBean {

    @ApiModelProperty(value = "姓名", position = 120)
    private String displayName;

    @ApiModelProperty(value = "邮箱地址", position = 130)
    private String email;

    @ApiModelProperty(value = "手机号", position = 140)
    private String mobile;

    @ApiModelProperty(value = "电话号码", position = 150)
    private String telephone;

    @ApiModelProperty(value = "用户DN", position = 160)
    private String userDn;

    @ApiModelProperty(value = "用户组织", position = 170)
    private String userOu;

    @ApiModelProperty(value = "用户ID", position = 180)
    private Integer userId;

    @ApiModelProperty(value = "用户名称", position = 190)
    private String userName;

    @ApiModelProperty(value = "状态", position = 200)
    private RecordStatus status;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getUserDn() {
        return userDn;
    }

    public void setUserDn(String userDn) {
        this.userDn = userDn;
    }

    public String getUserOu() {
        return userOu;
    }

    public void setUserOu(String userOu) {
        this.userOu = userOu;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }
}
