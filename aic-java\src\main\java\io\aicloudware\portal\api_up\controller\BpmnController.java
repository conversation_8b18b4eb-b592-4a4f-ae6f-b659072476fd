package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmSearchBean;
import io.aicloudware.portal.framework.sdk.contants.SpRecycleStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnDiagram;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnEdge;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnPlane;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnShape;
import org.camunda.bpm.model.bpmn.instance.dc.Bounds;
import org.camunda.bpm.model.bpmn.instance.di.Waypoint;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/bpmn")
public class BpmnController {

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
    @ResponseBody
    public ResponseBean queryVm(@ApiParam(value = "查询条件") @RequestBody SpVmSearchBean searchBean) {
        SpVmBean bean = new SpVmBean();
        bean.setId(1);
        bean.setName("test");
        SpVmBean[] entityList = new SpVmBean[]{bean};
        SpVmResultBean result = new SpVmResultBean();
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }


    @RequestMapping(value = "/getTest/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/getTest/{id}", httpMethod = "GET", value = "获取BPMN流程定义")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回BPMN对象", response = Object.class)})
    @ResponseBody
    public ResponseBean getTest(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        try {
            // 使用 Camunda BpmnModelInstance 创建标准 BPMN 对象
            BpmnModelInstance modelInstance = Bpmn.createEmptyModel();
            Definitions definitions = modelInstance.newInstance(Definitions.class);
            definitions.setTargetNamespace("http://camunda.org/examples");
            definitions.setExporter("Camunda Modeler");
            definitions.setExporterVersion("4.12.0");
            modelInstance.setDefinitions(definitions);

            // 创建流程
            org.camunda.bpm.model.bpmn.instance.Process process = modelInstance.newInstance(org.camunda.bpm.model.bpmn.instance.Process.class);
            process.setId("sample-process-" + id);
            process.setName("示例流程 " + id);
            process.setExecutable(true);
            definitions.addChildElement(process);

            // 创建开始事件
            StartEvent startEvent = modelInstance.newInstance(StartEvent.class);
            startEvent.setId("start");
            startEvent.setName("流程开始");
            process.addChildElement(startEvent);

            // 创建用户任务
            UserTask userTask = modelInstance.newInstance(UserTask.class);
            userTask.setId("userTask");
            userTask.setName("用户审批");
            process.addChildElement(userTask);

            // 创建结束事件
            EndEvent endEvent = modelInstance.newInstance(EndEvent.class);
            endEvent.setId("end");
            endEvent.setName("流程结束");
            process.addChildElement(endEvent);

            // 创建序列流
            SequenceFlow flow1 = modelInstance.newInstance(SequenceFlow.class);
            flow1.setId("flow1");
            flow1.setSource(startEvent);
            flow1.setTarget(userTask);
            process.addChildElement(flow1);

            SequenceFlow flow2 = modelInstance.newInstance(SequenceFlow.class);
            flow2.setId("flow2");
            flow2.setSource(userTask);
            flow2.setTarget(endEvent);
            process.addChildElement(flow2);

            // ===== 重要：添加图形信息 (BPMN DI) =====
            // 创建 BPMN 图表
            BpmnDiagram bpmnDiagram = modelInstance.newInstance(BpmnDiagram.class);
            bpmnDiagram.setId("BPMNDiagram_1");
            definitions.addChildElement(bpmnDiagram);

            // 创建 BPMN 平面
            BpmnPlane bpmnPlane = modelInstance.newInstance(BpmnPlane.class);
            bpmnPlane.setId("BPMNPlane_1");
            bpmnPlane.setBpmnElement(process);
            bpmnDiagram.addChildElement(bpmnPlane);

            // 为开始事件添加图形信息
            BpmnShape startShape = modelInstance.newInstance(BpmnShape.class);
            startShape.setId("_BPMNShape_StartEvent_2");
            startShape.setBpmnElement(startEvent);
            Bounds startBounds = modelInstance.newInstance(Bounds.class);
            startBounds.setX(179);
            startBounds.setY(99);
            startBounds.setWidth(36);
            startBounds.setHeight(36);
            startShape.setBounds(startBounds);
            bpmnPlane.addChildElement(startShape);

            // 为用户任务添加图形信息
            BpmnShape taskShape = modelInstance.newInstance(BpmnShape.class);
            taskShape.setId("_BPMNShape_UserTask_2");
            taskShape.setBpmnElement(userTask);
            Bounds taskBounds = modelInstance.newInstance(Bounds.class);
            taskBounds.setX(300);
            taskBounds.setY(77);
            taskBounds.setWidth(100);
            taskBounds.setHeight(80);
            taskShape.setBounds(taskBounds);
            bpmnPlane.addChildElement(taskShape);

            // 为结束事件添加图形信息
            BpmnShape endShape = modelInstance.newInstance(BpmnShape.class);
            endShape.setId("_BPMNShape_EndEvent_2");
            endShape.setBpmnElement(endEvent);
            Bounds endBounds = modelInstance.newInstance(Bounds.class);
            endBounds.setX(500);
            endBounds.setY(99);
            endBounds.setWidth(36);
            endBounds.setHeight(36);
            endShape.setBounds(endBounds);
            bpmnPlane.addChildElement(endShape);

            // 为序列流1添加图形信息
            BpmnEdge flow1Edge = modelInstance.newInstance(BpmnEdge.class);
            flow1Edge.setId("_BPMNEdge_SequenceFlow_1");
            flow1Edge.setBpmnElement(flow1);
            // 添加路径点
            Waypoint flow1Start = modelInstance.newInstance(Waypoint.class);
            flow1Start.setX(215);
            flow1Start.setY(117);
            flow1Edge.addChildElement(flow1Start);
            Waypoint flow1End = modelInstance.newInstance(Waypoint.class);
            flow1End.setX(300);
            flow1End.setY(117);
            flow1Edge.addChildElement(flow1End);
            bpmnPlane.addChildElement(flow1Edge);

            // 为序列流2添加图形信息
            BpmnEdge flow2Edge = modelInstance.newInstance(BpmnEdge.class);
            flow2Edge.setId("_BPMNEdge_SequenceFlow_2");
            flow2Edge.setBpmnElement(flow2);
            // 添加路径点
            Waypoint flow2Start = modelInstance.newInstance(Waypoint.class);
            flow2Start.setX(400);
            flow2Start.setY(117);
            flow2Edge.addChildElement(flow2Start);
            Waypoint flow2End = modelInstance.newInstance(Waypoint.class);
            flow2End.setX(500);
            flow2End.setY(117);
            flow2Edge.addChildElement(flow2End);
            bpmnPlane.addChildElement(flow2Edge);

            // 验证模型
            Bpmn.validateModel(modelInstance);

            // 转换为 XML 字符串
            String bpmnXml = Bpmn.convertToString(modelInstance);

            // 创建返回对象
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            result.put("name", "示例流程 " + id);
            result.put("processDefinitionKey", "sample-process-" + id);
            result.put("bpmnXml", bpmnXml);
            result.put("version", "1.0");

            return ResponseBean.success(result);

        } catch (Exception e) {
            e.printStackTrace(); // 添加详细错误日志
            return ResponseBean.error(500, "BPMN创建失败", e.getMessage());
        }
    }
}
