package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

import javax.persistence.*;

@Entity
@Table(name = "up_rest_log")
@Access(AccessType.FIELD)
public class UpRestLog extends BaseEntity {

	@Column(name = "type")
	private String type;

	@JoinColumn(name = "sp_org_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;

	@Column(name = "region")
	private String oldRegion;

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "region_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpRegionEntity region;

	@Column(name = "method")
	private String method;

	@Column(name = "main_agreement_id")
	private String mainAgreementId;

	@Column(name = "custom_no")
	private String customNo;

	@Column(name = "rest_status")
	private String restStatus;

	@Column(name = "parameters", length = ApiConstants.STRING_MAX_LENGTH)
	private String parameters;

	@Column(name = "callback", length = ApiConstants.STRING_MIDDLE_LENGTH)
	private String callback;

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}

	public String getRestStatus() {
		return restStatus;
	}

	public void setRestStatus(String restStatus) {
		this.restStatus = restStatus;
	}

	public String getParameters() {
		return parameters;
	}

	public void setParameters(String parameters) {
		this.parameters = parameters;
	}

	public String getCallback() {
		return callback;
	}

	public void setCallback(String callback) {
		this.callback = callback;
	}

	public String getMainAgreementId() {
		return mainAgreementId;
	}

	public void setMainAgreementId(String mainAgreementId) {
		this.mainAgreementId = mainAgreementId;
	}

	public String getOldRegion() {
		return oldRegion;
	}

	public void setOldRegion(String oldRegion) {
		this.oldRegion = oldRegion;
	}

	public SpRegionEntity getRegion() {
		return region;
	}

	public void setRegion(SpRegionEntity region) {
		this.region = region;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
