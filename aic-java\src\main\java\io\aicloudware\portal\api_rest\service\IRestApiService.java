package io.aicloudware.portal.api_rest.service;

import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_rest.framework.bean.RestResponseBean;

@Service
@Transactional
public interface IRestApiService{

	RestResponseBean userAdd(JSONObject params);

	RestResponseBean userDelete(JSONObject params);
	
	RestResponseBean quotaAdd(JSONObject params);
	
	RestResponseBean quotaChange(JSONObject params);
	
//	RestResponseBean quotaCancel(JSONObject params);
	
	RestResponseBean orgAdd(JSONObject params);

	RestResponseBean orgStart(JSONObject params);

	RestResponseBean orgStop(JSONObject params);

	RestResponseBean orgDelete(JSONObject params);

	RestResponseBean vappVmmetrics(JSONObject params);

	RestResponseBean vappNetmetrics(JSONObject params);

	RestResponseBean reservationInquire(JSONObject params);

    RestResponseBean createVPCMessage(JSONObject params);

    RestResponseBean createOrderMessage(JSONObject params);

	String ping(Integer ovdcId, String ip, String type);

    // GY节点资源耗尽，暂时将GY的新租户申请移至GY3
    SpRegionEntity transformGY_GY3(SpOrg spOrg, SpRegionEntity region);
}
