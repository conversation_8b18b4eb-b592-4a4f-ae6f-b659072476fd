package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡池")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerPoolBean.class})
public class SpLoadBalancerPoolBean extends SpRecordBean {

	@ApiModelProperty(value = "租户ID")
	private Integer spOrgId;
	
    @ApiModelProperty(value = "负载均衡ID")
    private Integer loadBalancerId;
    
    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "算法")
    private String algorithm;
    
    @ApiModelProperty(value = "算法参数")
    private String algorithmParam;
    
    @ApiModelProperty(value = "透明")
    private Boolean transparent;
    
    @ApiModelProperty(value = "poolId")
    private String poolId;
    
    @ApiModelProperty(value = "成员列表")
    private SpLoadBalancerMemberBean[] loadBalancerMemberList;

    public Integer getLoadBalancerId() {
		return loadBalancerId;
	}

	public void setLoadBalancerId(Integer loadBalancerId) {
		this.loadBalancerId = loadBalancerId;
	}

	public SpLoadBalancerMemberBean[] getLoadBalancerMemberList() {
		return loadBalancerMemberList;
	}

	public void setLoadBalancerMemberList(SpLoadBalancerMemberBean[] loadBalancerMemberList) {
		this.loadBalancerMemberList = loadBalancerMemberList;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getAlgorithm() {
		return algorithm;
	}

	public void setAlgorithm(String algorithm) {
		this.algorithm = algorithm;
	}

	public String getAlgorithmParam() {
		return algorithmParam;
	}

	public void setAlgorithmParam(String algorithmParam) {
		this.algorithmParam = algorithmParam;
	}

	public Boolean getTransparent() {
		return transparent;
	}

	public void setTransparent(Boolean transparent) {
		this.transparent = transparent;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public String getPoolId() {
		return poolId;
	}

	public void setPoolId(String poolId) {
		this.poolId = poolId;
	}
	
	
	
}
