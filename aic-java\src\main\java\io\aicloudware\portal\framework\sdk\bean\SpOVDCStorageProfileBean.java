package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "OVDC存储策略配置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOVDCStorageProfileBean.class})
public class SpOVDCStorageProfileBean extends SpRecordBean {

    @ApiModelProperty(value = "预留ID")
    private Integer ovdcId;

    @ApiModelProperty(value = "预留名称")
    private String ovdcName;

    @ApiModelProperty(value = "预留显示名称")
    private String ovdcDisplayName;

    @ApiModelProperty(value = "已使用存储")
    private Long storageUsedMB;

    @ApiModelProperty(value = "限额")
    private Long limit;
    
    @ApiModelProperty(value = "已启用")
    private Boolean enabled;
    
    @ApiModelProperty(value = "已分配IOPS")
    private Long iopsAllocated;


    public Integer getOvdcId() {
		return ovdcId;
	}

	public void setOvdcId(Integer ovdcId) {
		this.ovdcId = ovdcId;
	}

	public String getOvdcName() {
		return ovdcName;
	}

	public void setOvdcName(String ovdcName) {
		this.ovdcName = ovdcName;
	}

	public String getOvdcDisplayName() {
		return ovdcDisplayName;
	}

	public void setOvdcDisplayName(String ovdcDisplayName) {
		this.ovdcDisplayName = ovdcDisplayName;
	}

	public Long getStorageUsedMB() {
		return storageUsedMB;
	}

	public void setStorageUsedMB(Long storageUsedMB) {
		this.storageUsedMB = storageUsedMB;
	}

	public Long getLimit() {
		return limit;
	}

	public void setLimit(Long limit) {
		this.limit = limit;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Long getIopsAllocated() {
		return iopsAllocated;
	}

	public void setIopsAllocated(Long iopsAllocated) {
		this.iopsAllocated = iopsAllocated;
	}

	
}
