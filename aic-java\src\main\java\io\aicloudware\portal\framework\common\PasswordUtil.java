package io.aicloudware.portal.framework.common;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class PasswordUtil {

    private static final List<String> letterList = Arrays.asList(
            "ABCDEFGHJKLMNPQRSTUVWXYZ",
            "abcdefghijkmnopqrstuvwxyz",
            "23456789",
            "!@#$%&:;,"
    );

    private static final Random random = new Random();

    public static String generateRandomPassword() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            String letter = letterList.get(i % 4);
            sb.append(letter.charAt(random.nextInt(letter.length())));
        }
        return sb.toString();
    }
}
