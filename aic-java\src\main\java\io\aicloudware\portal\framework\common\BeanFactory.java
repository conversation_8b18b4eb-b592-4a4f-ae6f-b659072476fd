package io.aicloudware.portal.framework.common;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import io.aicloudware.portal.api_up.service.IUpEmailService;
import io.aicloudware.portal.api_up.service.IUpRightService;
import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.dao.ICloudDao;

@Component
public class BeanFactory {
    private static ICloudDao _cloudDao;
    private static IUpEmailService _emailService;
    private static MessageSource _messageSource;
    private static IUpSystemConfigService _systemConfigService;
    private static IUpRightService _rightService;

    @Autowired
    private ICloudDao cloudDao;

    @Autowired
    private IUpEmailService emailService;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private IUpSystemConfigService systemConfigService;

    @Autowired
    private IUpRightService rightService;

    @PostConstruct
    public void init() {
        _cloudDao = cloudDao;
        _emailService = emailService;
        _messageSource = messageSource;
        _systemConfigService = systemConfigService;
        _rightService = rightService;
    }

    public static ICloudDao getCloudDao() {
        return _cloudDao;
    }

    public static IUpEmailService getEmailService() {
        return _emailService;
    }

    public static MessageSource getMessageSource() {
        return _messageSource;
    }

    public static IUpSystemConfigService getSystemConfigService() {
        return _systemConfigService;
    }

    public static IUpRightService getRightService() {
        return _rightService;
    }

}
