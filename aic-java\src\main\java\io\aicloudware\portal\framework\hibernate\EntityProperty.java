package io.aicloudware.portal.framework.hibernate;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target(FIELD)
@Retention(RUNTIME)
public @interface EntityProperty {

    boolean isParentEntity() default true;

    boolean isCopyOnUpdate() default true;

    boolean isNullCopy() default true;

}
