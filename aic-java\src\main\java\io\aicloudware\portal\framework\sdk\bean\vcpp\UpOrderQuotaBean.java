package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "配额")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderQuotaBean.class})
public class UpOrderQuotaBean extends SpRecordBean {

	@ApiModelProperty(value = "协议号")
	private String code;
	
	@ApiModelProperty(value = "用户ID")
	private Integer ownerId;
	
	@ApiModelProperty(value = "用户NAME")
	private String ownerName;
	
	@ApiModelProperty(value = "租户ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "类型")
	private ProductType type;
	
	@ApiModelProperty(value = "栏目")
	private QuotaCatalog catalog;
	
	@ApiModelProperty(value = "明细")
    private UpOrderQuotaDetailBean[] quotaDetailList;
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public UpOrderQuotaDetailBean[] getQuotaDetailList() {
		return quotaDetailList;
	}

	public void setQuotaDetailList(UpOrderQuotaDetailBean[] quotaDetailList) {
		this.quotaDetailList = quotaDetailList;
	}

	public ProductType getType() {
		return type;
	}

	public void setType(ProductType type) {
		this.type = type;
	}

	public String getOwnerName() {
		return ownerName;
	}

	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}

	public QuotaCatalog getCatalog() {
		return catalog;
	}

	public void setCatalog(QuotaCatalog catalog) {
		this.catalog = catalog;
	}

}
