package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ObjectStorageType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "对象存储")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderObjectStorageBucketBean.class })
public class UpOrderObjectStorageBucketBean extends RecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;

	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;

	@ApiModelProperty(value = "类型")
	private ObjectStorageType type;

	@ApiModelProperty(value = "所有者")
	private Integer ownerId;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;

	@ApiModelProperty(value = "组织名称")
	private String spOrgName;

	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;

	@ApiModelProperty(value = "云主机类型")
	private Long sizeG;

	@ApiModelProperty(value = "云盘产品配置ID")
	private Integer diskConfigId;

	@ApiModelProperty(value = "对象存储ID")
	private Integer spObjectStorageBucketId;


	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public ObjectStorageType getType() {
		return type;
	}

	public void setType(ObjectStorageType type) {
		this.type = type;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public Long getSizeG() {
		return sizeG;
	}

	public void setSizeG(Long sizeG) {
		this.sizeG = sizeG;
	}

	public Integer getDiskConfigId() {
		return diskConfigId;
	}

	public void setDiskConfigId(Integer diskConfigId) {
		this.diskConfigId = diskConfigId;
	}

	public Integer getSpObjectStorageBucketId() {
		return spObjectStorageBucketId;
	}

	public void setSpObjectStorageBucketId(Integer spObjectStorageBucketId) {
		this.spObjectStorageBucketId = spObjectStorageBucketId;
	}


}