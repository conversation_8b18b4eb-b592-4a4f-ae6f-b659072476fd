package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "网络配置文件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpIpScopeBean.class})
public class SpIpScopeBean extends SpRecordBean {

    @ApiModelProperty(value = "网关")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String gateway;

    @ApiModelProperty(value = "子网掩码")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String netMask;

    @ApiModelProperty(value = "cidr")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String cidr;

    @ApiModelProperty(value = "主DNS")
    private String dns1;

    @ApiModelProperty(value = "辅助DNS")
    private String dns2;

    @ApiModelProperty(value = "DNS后缀")
    private String dnsSuffix;
    
    @ApiModelProperty(value = "已使用IP数")
    private Long numberOfUsedIp;
    
    @ApiModelProperty(value = "总IP数")
    private Long numberOfTotalIp;

    @ApiModelProperty(value = "IP地址范围列表")
    private SpIpRangeBean[] ipRangeList;

    @ApiModelProperty(value = "预留网络关系列表")
    private SpOVDCNetworkBean[] ovdcNetworkList;

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getNetMask() {
        return netMask;
    }

    public void setNetMask(String netMask) {
        this.netMask = netMask;
    }

    public String getDns1() {
        return dns1;
    }

    public void setDns1(String dns1) {
        this.dns1 = dns1;
    }

    public String getDns2() {
        return dns2;
    }

    public void setDns2(String dns2) {
        this.dns2 = dns2;
    }
    
    public String getDnsSuffix() {
		return dnsSuffix;
	}

	public void setDnsSuffix(String dnsSuffix) {
		this.dnsSuffix = dnsSuffix;
	}

    public SpIpRangeBean[] getIpRangeList() {
        return ipRangeList;
    }

    public void setIpRangeList(SpIpRangeBean[] ipRangeList) {
        this.ipRangeList = ipRangeList;
    }

	public SpOVDCNetworkBean[] getOvdcNetworkList() {
		return ovdcNetworkList;
	}

	public void setOvdcNetworkList(SpOVDCNetworkBean[] ovdcNetworkList) {
		this.ovdcNetworkList = ovdcNetworkList;
	}

	public Long getNumberOfUsedIp() {
		return numberOfUsedIp;
	}

	public void setNumberOfUsedIp(Long numberOfUsedIp) {
		this.numberOfUsedIp = numberOfUsedIp;
	}

	public Long getNumberOfTotalIp() {
		return numberOfTotalIp;
	}

	public void setNumberOfTotalIp(Long numberOfTotalIp) {
		this.numberOfTotalIp = numberOfTotalIp;
	}


    public String getCidr() {
        return cidr;
    }

    public void setCidr(String cidr) {
        this.cidr = cidr;
    }
}
