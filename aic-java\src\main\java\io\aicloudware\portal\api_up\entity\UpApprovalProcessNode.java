package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessNodeBean;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_approval_process_node")
@Access(AccessType.FIELD)
public class UpApprovalProcessNode extends BaseUpEntity<UpApprovalProcessNodeBean> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approval_process_id", nullable = false)
    private UpApprovalProcess approvalProcess;

    @Column(name = "seq", nullable = false)
    private Integer seq;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private UpUser user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id")
    private UpRole role;

    @Column(name = "is_app_system_group", nullable = false)
    private Boolean isAppSystemGroup;

    public UpApprovalProcess getApprovalProcess() {
        return approvalProcess;
    }

    public void setApprovalProcess(UpApprovalProcess approvalProcess) {
        this.approvalProcess = approvalProcess;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }

    public UpRole getRole() {
        return role;
    }

    public void setRole(UpRole role) {
        this.role = role;
    }

    public Boolean getAppSystemGroup() {
        return isAppSystemGroup;
    }

    public void setAppSystemGroup(Boolean appSystemGroup) {
        isAppSystemGroup = appSystemGroup;
    }
}
