package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.ReqVm;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/req_vm")
@Api(value = "/req_vm", description = "申请单虚机信息", position = 602)
public class ReqVmController extends BaseUpController<ReqVm, SpVmBean, SpVmResultBean> {

//    @Autowired
//    private ISpVmService spVmService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryReqVm(@ApiParam(value = "查询条件") @RequestBody SpVmSearchBean searchBean) {
//        ReqVm entity = BeanCopyUtil.copy(searchBean.getBean(), ReqVm.class);
//        SpVmBean[] entityList = commonService.query(searchBean, entity, SpVmBean.class);
//        SpVmResultBean result = new SpVmResultBean();
//        fillPageInfo(searchBean, result);
//        result.setDataList(entityList);
//        return ResponseBean.success(result);
//    }
//
//    @Override
//    protected SpVmBean[] doUpdate(List<SpVmBean> beanList) {
//        return spVmService.updateReqVmList(beanList);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class)})
//    @ResponseBody
//    public ResponseBean updateReqVm(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                    @ApiParam(value = "实例对象") @Valid @RequestBody SpVmBean bean,
//                                    BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = ReqVmListBean.class)})
//    @ResponseBody
//    public ResponseBean updateReqVm(@ApiParam(value = "实例对象") @Valid @RequestBody ReqVmListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/get_orig_vm/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_orig_vm/{id}", httpMethod = "GET", value = "获取原始虚机信息")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取原始虚机信息", response = SpVmBean.class)})
//    @ResponseBody
//    public ResponseBean getOrigVm(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return ResponseBean.success(spVmService.getSpVmByReqVmId(id));
//    }
}
