package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "安全组规则")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpSecurityGroupRuleBean.class})
public class SpSecurityGroupRuleBean extends SpRecordBean {

    @ApiModelProperty(value = "描述")
    private String description;

	@ApiModelProperty(name = "enabled")
	private Boolean enabled;
	
	@ApiModelProperty(name = "policy")
	private String policy;
	
	@ApiModelProperty(name = "协议")
	private String protocol;

	@ApiModelProperty(name = "portRangeMax")
	private Integer portRangeMax;

	@ApiModelProperty(name = "portRangeMin")
	private Integer portRangeMin;

	@ApiModelProperty(name = "remoteGroupId")
	private String remoteGroupId;

	@ApiModelProperty(name = "remoteIpPrefix")
	private String remoteIpPrefix;
	
	@ApiModelProperty(name = "目标端口范围")
	private String destinationPortRange;
	
	@ApiModelProperty(name = "目标IP")
	private String destinationIp;
	
	@ApiModelProperty(name = "源端口范围")
	private String sourcePortRange;
	
	@ApiModelProperty(name = "源IP")
	private String sourceIp;
	
	@ApiModelProperty(name = "方向")
	private String direction;

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public String getPolicy() {
		return policy;
	}

	public void setPolicy(String policy) {
		this.policy = policy;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public String getDestinationPortRange() {
		return destinationPortRange;
	}

	public void setDestinationPortRange(String destinationPortRange) {
		this.destinationPortRange = destinationPortRange;
	}

	public String getDestinationIp() {
		return destinationIp;
	}

	public void setDestinationIp(String destinationIp) {
		this.destinationIp = destinationIp;
	}

	public String getSourcePortRange() {
		return sourcePortRange;
	}

	public void setSourcePortRange(String sourcePortRange) {
		this.sourcePortRange = sourcePortRange;
	}

	public String getSourceIp() {
		return sourceIp;
	}

	public void setSourceIp(String sourceIp) {
		this.sourceIp = sourceIp;
	}

	public String getDirection() {
		return direction;
	}

	public void setDirection(String direction) {
		this.direction = direction;
	}

	public Integer getPortRangeMax() {
		return portRangeMax;
	}

	public void setPortRangeMax(Integer portRangeMax) {
		this.portRangeMax = portRangeMax;
	}

	public Integer getPortRangeMin() {
		return portRangeMin;
	}

	public void setPortRangeMin(Integer portRangeMin) {
		this.portRangeMin = portRangeMin;
	}

	public String getRemoteGroupId() {
		return remoteGroupId;
	}

	public void setRemoteGroupId(String remoteGroupId) {
		this.remoteGroupId = remoteGroupId;
	}

	public String getRemoteIpPrefix() {
		return remoteIpPrefix;
	}

	public void setRemoteIpPrefix(String remoteIpPrefix) {
		this.remoteIpPrefix = remoteIpPrefix;
	}
}
