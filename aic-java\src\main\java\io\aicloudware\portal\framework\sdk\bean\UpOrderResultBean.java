package io.aicloudware.portal.framework.sdk.bean;

import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "订单查询结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpOrderResultBean extends ResultListBean<UpOrderBean> {
}
