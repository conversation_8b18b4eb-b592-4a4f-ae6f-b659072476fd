package io.aicloudware.portal.framework.bean;

import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.framework.validate.V02;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

public abstract class RecordBean extends BaseBean implements IDProvider {

    @ApiModelProperty(value = "Quota code")
    private String doorOrderItemId;

    @ApiModelProperty(value = "对象ID", position = 10)
    private Integer id;

    @ApiModelProperty(value = "对象名称", position = 20)
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String name;

    @ApiModelProperty(value = "变更类型", position = 30)
    private UpOperateType operateType;

    private Integer appSystemId;

    private String appSystemName;

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UpOperateType getOperateType() {
        return operateType;
    }

    public void setOperateType(UpOperateType operateType) {
        this.operateType = operateType;
    }

    public String getDoorOrderItemId() {
        return doorOrderItemId;
    }

    public void setDoorOrderItemId(String doorOrderItemId) {
        this.doorOrderItemId = doorOrderItemId;
    }
}
