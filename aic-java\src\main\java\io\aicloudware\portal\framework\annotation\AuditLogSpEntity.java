package io.aicloudware.portal.framework.annotation;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditLogSpEntity {

	UpProductSystemEnums.ProductType type();
	String description();
	
}
