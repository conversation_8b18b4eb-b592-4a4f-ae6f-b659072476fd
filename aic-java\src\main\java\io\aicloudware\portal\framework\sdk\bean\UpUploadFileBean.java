package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpUploadFileType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "文件上传")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpUploadFileBean.class})
public class UpUploadFileBean extends RecordBean {
    @ApiModelProperty(value = "业务租户ID", position = 60)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 70)
    private String upTenantName;

    @ApiModelProperty(value = "用户ID", position = 80)
    private Integer userId;

    @ApiModelProperty(value = "用户名称", position = 90)
    private String userName;

    @ApiModelProperty(value = "用户显示名称")
    private String userDisplayName;

    @ApiModelProperty(value = "文件类型", position = 100)
    private UpUploadFileType type;

    @ApiModelProperty(value = "文件地址", position = 110)
    private String path;

    @ApiModelProperty(value = "显示名称", position = 120)
    private String displayName;


    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserDisplayName() {
        return userDisplayName;
    }

    public void setUserDisplayName(String userDisplayName) {
        this.userDisplayName = userDisplayName;
    }

    public UpUploadFileType getType() {
        return type;
    }

    public void setType(UpUploadFileType type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}
