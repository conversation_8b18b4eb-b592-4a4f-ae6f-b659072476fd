package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.framework.dao.IQueryDao;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.Statement;
import java.util.Arrays;
import java.util.List;
import java.util.TreeMap;

@Service
public class SystemVersionUpgrade {

    @Autowired
    private IQueryDao queryDao;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private IUpSystemConfigService systemConfigService;

    private static final TreeMap<String, Runnable> versionTaskMap = new TreeMap<>();

    @PostConstruct
    private void initTask() {
        versionTaskMap.put("doUpgrade_Ver700_20241210_00932", this::doUpgrade_Ver700_20241210_00932);
    }

    private void doUpgrade_Ver700_20241210_00932() {
        String[] sqlList = {
        };
        executeSqlList(Arrays.asList(sqlList));
    }

    public void doUpgrade_VersionFirst(String dbVersion) {
        String[] sqlList = {
        };
        executeSqlList(Arrays.asList(sqlList));
    }

    private void doUpgrade_VersionLast() {
        String[] sqlList = {
        };
        executeSqlList(Arrays.asList(sqlList));
    }

    public void executeSqlList(List<String> sqlList) {
        if (Utility.isNotEmpty(sqlList)) {
            commonService.doTransactionalTask(() -> queryDao.execute(connection -> {
                Statement statement = connection.createStatement();
                for (String sql : sqlList) {
                    if (Utility.isNotEmpty(sql)) {
                        statement.execute(sql);
                    }
                }
            }));
        }
    }

    public static String getLastVersion() {
        return convertVersion(null);
    }

    public static String convertVersion(String version) {
        if (Utility.isEmpty(version)) {
            version = versionTaskMap.lastKey();
        }
        version = version.replace("doUpgrade_", "");
        return version.substring(0, 4) + "." + version.substring(4);
    }

    public void doVersionUpgrade(String dbVersion) {
        for (String key : versionTaskMap.keySet()) {
            String version = convertVersion(key);
            if (version.compareTo(dbVersion) > 0) {
                versionTaskMap.get(key).run();
                insertSystemConfig(UpSystemConfigKey.version, version, true);
            }
        }
        doUpgrade_VersionLast();
    }

    public void insertDefaultSystemConfig() {
        insertSystemConfig(UpSystemConfigKey.version, getLastVersion(), false);
    }

    private void insertSystemConfig(UpSystemConfigKey key, String value, boolean force) {
        UpSystemConfigBean bean = systemConfigService.get(key);
        if (Utility.isEmpty(bean.getValue()) || force) {
            bean.setValue(value);
            UpSystemConfigListBean listBean = new UpSystemConfigListBean();
            listBean.setDataList(new UpSystemConfigBean[]{bean});
            systemConfigService.save(listBean);
        }
    }
}
