package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.sdk.bean.UpFormDefinitionBean;

import javax.persistence.*;

@Entity
@Table(name = "up_form_definitions")
@Access(AccessType.FIELD)
public final class UpFormDefinition extends BaseEntity<UpFormDefinitionBean> {

	@Column(name = "description", length = 1000)
	private String description;

	@Column(name = "schema", length = ApiConstants.STRING_MAX_LENGTH)
	private String schema;

	@Column(name = "uuid")
	private String uuid;

	@Column(name = "version")
	private Integer version;

	public UpFormDefinition() {
	}

	public UpFormDefinition(Integer id){
		super(id);
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getSchema() {
		return schema;
	}

	public void setSchema(String schema) {
		this.schema = schema;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}
}
