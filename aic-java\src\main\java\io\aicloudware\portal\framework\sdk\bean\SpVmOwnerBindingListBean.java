package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpDataListBean;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "虚机绑定用户批量操作对象")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVmOwnerBindingListBean extends SpDataListBean<SpVmOwnerBindingBean> {

    private Integer vmId;

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }
}
