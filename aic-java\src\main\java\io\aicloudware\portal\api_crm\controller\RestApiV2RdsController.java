package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.framework.bean.RestV2RdsBean;
import io.aicloudware.portal.api_crm.service.IRestRdsService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.SpDeployStatus;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRds;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.service.ISpMySqlService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Controller
@RequestMapping("/api/v2/mysql")
@Api(value = "/api/v2/mysql", description = "mysql", position = 47)
public class RestApiV2RdsController extends BaseEntityController<SpRds, SpRdsBean, SpRdsResultBean> {
	@Autowired
	private IRestRdsService restRdsService;

    @Autowired
    protected ISpMySqlService spMySqlService;

    private Long ESTIMATED_PROVISIONING_TIME_IN_SECOND = 300L;
	
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "新建MYSQL")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = ResponseBean.class)})
    @ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.CLOUD_MYSQL, description = "新增")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody RestV2RdsBean bean, BindingResult bindingResult) {
        bean.setAmount(bean.getAmount() == null ? 1 : bean.getAmount());
        return ResponseBean.success(restRdsService.save(bean));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpRdsResultBean.class) })
    @ResponseBody
    public ResponseBean queryrDS(@ApiParam(value = "查询条件") @RequestBody SpRdsSearchBean searchBean) {
        SpRds entity = BeanCopyUtil.copy(searchBean.getBean(), SpRds.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        SpVapp vapp = new SpVapp();
        vapp.setDeployStatus(SpDeployStatus.COMPLETE);
        vapp.setRegion(ThreadCache.getRegion());
        entity.setSpVapp(vapp);

        if (StringUtils.isNotEmpty(entity.getName())) {
            SpRdsBean fuzzyBean = new SpRdsBean();
            fuzzyBean.setName(entity.getName());
            fuzzyBean.setIpAddress(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            SpRdsBean[] entityList = this.doQuery(searchBean, entity);
            SpRdsResultBean result = new SpRdsResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class) })
    @ResponseBody
    public ResponseBean getVm(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        SpRdsBean bean = commonService.load(SpRds.class, SpRdsBean.class, id, ThreadCache.getOrgId());
        SpVappBean vapp = bean.getSpVapp();
        if (SpDeployStatus.INIT.equals(vapp.getDeployStatus())) {
            int progres = 0;
            long provisioningTime = System.currentTimeMillis() - bean.getCreateTm().getTime();
            if (provisioningTime < 0) {
                provisioningTime = 0;
            }
            progres = (int) (100 - (ESTIMATED_PROVISIONING_TIME_IN_SECOND - provisioningTime / 1000) * 100
                    / ESTIMATED_PROVISIONING_TIME_IN_SECOND);
            if (progres >= 99) {
                progres = 99;
            }
            bean.setProgres(progres);
        } else if (SpDeployStatus.COMPLETE.equals(vapp.getDeployStatus())) {
            bean.setProgres(100);
        } else if (SpDeployStatus.ERROR.equals(vapp.getDeployStatus())) {
            bean.setProgres(99);
        }
        return ResponseBean.success(bean);
    }

    @RequestMapping(value = "/power_on/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_on/{id}", httpMethod = "POST", value = "rds开机")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.CLOUD_MYSQL, description = "开机")
    public ResponseBean on(@ApiParam(value = "对象ID") @PathVariable String id) {
        SpRdsBean bean = commonService.load(SpRds.class, SpRdsBean.class, Integer.parseInt(id), ThreadCache.getOrgId());
        spMySqlService.powerOn(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/power_off/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_off/{id}", httpMethod = "POST", value = "rds关机")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.CLOUD_MYSQL, description = "关机")
    public ResponseBean off(@ApiParam(value = "对象ID") @PathVariable String id) {
        SpRdsBean bean = commonService.load(SpRds.class, SpRdsBean.class, Integer.parseInt(id), ThreadCache.getOrgId());
        spMySqlService.powerShutdown(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/power_reboot/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/power_reboot/{id}", httpMethod = "POST", value = "rds重启")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.CLOUD_MYSQL, description = "重启")
    public ResponseBean reboot(@ApiParam(value = "对象ID") @PathVariable String id) {
        SpRdsBean bean = commonService.load(SpRds.class, SpRdsBean.class, Integer.parseInt(id), ThreadCache.getOrgId());
        spMySqlService.powerReboot(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除rds")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.CLOUD_MYSQL, description = "删除")
    public ResponseBean deleteRedis(@ApiParam(value = "对象ID") @PathVariable String id) {
        SpRdsBean bean = commonService.load(SpRds.class, SpRdsBean.class, Integer.parseInt(id), ThreadCache.getOrgId());
        spMySqlService.deleteRds(id);
        return ResponseBean.success(true);
    }

}
