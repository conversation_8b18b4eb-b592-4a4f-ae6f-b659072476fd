package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpIpBindingType;
import io.aicloudware.portal.framework.sdk.contants.SpProtocolType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "弹性公网IP绑定")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpIpBindingBean.class})
public class SpIpBindingBean extends SpRecordBean {

    @ApiModelProperty(value = "公网IPID")
    private Integer elasticIpId;

    @ApiModelProperty(value = "公网IP名称")
    private String elasticIpName;
    
    @ApiModelProperty(value = "公网IP显示名称")
    private String elasticIpDisplayName;

    @ApiModelProperty(value = "虚拟机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚拟机名称")
    private String vmName;
    
    @ApiModelProperty(value = "虚拟机显示名称")
    private String vmDisplayName;
    
    @ApiModelProperty(value = "负载均衡ID")
    private Integer virtualServerId;

    @ApiModelProperty(value = "负载均衡名称")
    private String virtualServerName;
    
    @ApiModelProperty(value = "负载均衡显示名称")
    private String virtualServerDisplayName;
    
    @ApiModelProperty(value = "云主机端口")
    private String vm_port;

	@ApiModelProperty(value = "云主机端口")
	private String vmPort;
    
    @ApiModelProperty(value = "公网端口")
    private String public_port;

	@ApiModelProperty(value = "公网端口")
	private String publicPort;
    
    @ApiModelProperty(value = "协议")
    private SpProtocolType protocol;
    
    @ApiModelProperty(value = "类型")
    private SpIpBindingType type;

	public Integer getElasticIpId() {
		return elasticIpId;
	}

	public void setElasticIpId(Integer elasticIpId) {
		this.elasticIpId = elasticIpId;
	}

	public String getElasticIpName() {
		return elasticIpName;
	}

	public void setElasticIpName(String elasticIpName) {
		this.elasticIpName = elasticIpName;
	}

	public String getElasticIpDisplayName() {
		return elasticIpDisplayName;
	}

	public void setElasticIpDisplayName(String elasticIpDisplayName) {
		this.elasticIpDisplayName = elasticIpDisplayName;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public String getVmName() {
		return vmName;
	}

	public void setVmName(String vmName) {
		this.vmName = vmName;
	}

	public String getVmDisplayName() {
		return vmDisplayName;
	}

	public void setVmDisplayName(String vmDisplayName) {
		this.vmDisplayName = vmDisplayName;
	}

	public Integer getVirtualServerId() {
		return virtualServerId;
	}

	public void setVirtualServerId(Integer virtualServerId) {
		this.virtualServerId = virtualServerId;
	}

	public String getVirtualServerName() {
		return virtualServerName;
	}

	public void setVirtualServerName(String virtualServerName) {
		this.virtualServerName = virtualServerName;
	}

	public String getVirtualServerDisplayName() {
		return virtualServerDisplayName;
	}

	public void setVirtualServerDisplayName(String virtualServerDisplayName) {
		this.virtualServerDisplayName = virtualServerDisplayName;
	}
	
	public String getVm_port() {
		return vm_port;
	}

	public void setVm_port(String vm_port) {
		this.vm_port = vm_port;
	}

	public String getPublic_port() {
		return public_port;
	}

	public void setPublic_port(String public_port) {
		this.public_port = public_port;
	}

	public SpProtocolType getProtocol() {
		return protocol;
	}

	public void setProtocol(SpProtocolType protocol) {
		this.protocol = protocol;
	}

	public SpIpBindingType getType() {
		return type;
	}

	public void setType(SpIpBindingType type) {
		this.type = type;
	}

	public String toString() {
		return "elasticIpId: '" + this.elasticIpId + "', vmId: '" + this.vmId + "', vm_port: '" + this.vm_port + "', public_port: '" + this.public_port + "', publicPort: '" + this.publicPort + "', vmPort: '" + this.vmPort + "'";
	}

	public String getVmPort() {
		return vmPort;
	}

	public void setVmPort(String vmPort) {
		this.vmPort = vmPort;
	}

	public String getPublicPort() {
		return publicPort;
	}

	public void setPublicPort(String publicPort) {
		this.publicPort = publicPort;
	}
}
