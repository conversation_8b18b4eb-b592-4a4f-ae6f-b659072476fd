package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_rest.framework.bean.GenTokenParams;
import io.aicloudware.portal.api_rest.framework.bean.RestTokenBean;
import io.aicloudware.portal.api_rest.framework.entity.RestCustom;
import io.aicloudware.portal.api_rest.framework.util.JWTTokenUtil;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.entity.UpUserLdap;
import io.aicloudware.portal.api_up.entity.UpUserLogout;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.framework.action.UserAction;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.exception.SystemException;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.remote.RemoteUtil;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpServerConnection;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import io.aicloudware.portal.platform_vcd.service.ISpVmService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ldap.core.DirContextAdapter;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.ldap.DefaultSpringSecurityContextSource;
import org.springframework.security.ldap.authentication.BindAuthenticator;
import org.springframework.security.ldap.authentication.LdapAuthenticationProvider;
import org.springframework.security.ldap.authentication.ad.ActiveDirectoryLdapAuthenticationProvider;
import org.springframework.security.ldap.search.FilterBasedLdapUserSearch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.naming.NameClassPair;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpUserService extends BaseTaskService implements IUpUserService {

	private final static Logger logger = Logger.getLogger(UpUserService.class);

	@Autowired
	private IUpSystemConfigService upSystemConfigService;

	@Autowired
	private IUpOperationLogService upOperationLogService;

	@Autowired
	private IUpApplicationService upApplicationService;

	@Autowired
	private ISpVmService spVmService;

	@Autowired
	private ISpRegionService spRegionService;

	@Value("${env}")
	private String env;

	//	@Value("${check_user_device}")
	private String checkUserDevice;

	//	@Value("${user_device_uri}")
	private String userDeviceUri;

	//	@Value("${secret_key}")
	private String secretKey;

	private static DefaultSpringSecurityContextSource contextSource = null;
	private static AuthenticationProvider authenticationProvider = null;

	public static void clearAuthenticationProvider() {
		contextSource = null;
		authenticationProvider = null;
	}

	@Override
//	@Transactional(propagation = Propagation.NEVER, readOnly = true)
	public Map<String, Object> login(UpUserBean bean, Boolean isAdminLogin) {
		AssertUtil.check(Utility.isNotEmpty(bean.getName()), "用户名不能为空");
		AssertUtil.check(Utility.isNotEmpty(bean.getPassword()), "密码不能为空");
		UpUserSearchBean searchBean = new UpUserSearchBean();
		UpUser user = new UpUser();
		if (env.equals("dev")) {
			user.setName("pso");
			List<UpUser> pso = dao.query(searchBean, user);
			if(pso == null || pso.size() == 0 || pso.get(0) == null) {
				user.setName("pso");
				user.setUsername("pso");
				user.setSystemAdmin(true);
				user.setIsArrearage(false);
				SpOrg org = new SpOrg();
				org.setUsername("pso");
				List<SpOrg> orgs = this.dao.query(new SpOrgSearchBean(), org);
				AssertUtil.check(orgs != null && orgs.size() > 0, "未找到所属用户组，请联系管理员！");
				user.setOrg(orgs.get(0));
				this.dao.insert(user);
			}else {
				user = pso.get(0);
			}
		}else {
			user.setName(bean.getName());
			List<UpUser> userList = dao.query(searchBean, user);
//			AssertUtil.check(userList.size() <= 1, "用户名重复");
			AssertUtil.check(userList.size() != 0, "用户名或密码不正确");

			user = userList.get(0);
			AssertUtil.check(RecordStatus.isNormal(user.getStatus()), "用户状态不正确");
			if(isAdminLogin){
				AssertUtil.check(user.getTenantAdmin(), "用户无访问权限");
			}
			Integer failureNumber = user.getFailureNumber();
			Long failureTime = user.getFailureTime();

			AssertUtil.check(failureTime == null || failureTime < System.currentTimeMillis(), "密码连续3次输入失败，账号冻结30分钟");

			if(passwordAuthenticate(bean, user)) {
				failureNumber = null;
				failureTime = null;
			}else if(failureNumber == null || failureTime ==null) {
				failureNumber = 1;
				failureTime = System.currentTimeMillis();
			}else if((failureTime + 1 * 60 * 1000l) >= System.currentTimeMillis()){
				failureNumber ++;
				failureTime = System.currentTimeMillis();
			}else {
				failureNumber = 1;
				failureTime = System.currentTimeMillis();
			}


			if(failureNumber != null && failureNumber >= 3) {
				user.setFailureNumber(null);
				user.setFailureTime(System.currentTimeMillis() + 30 * 60 * 1000l);
				dao.update(user, "failureNumber", "failureTime");
				AssertUtil.check(false, "1分钟内密码连续3次输入失败，账号冻结30分钟");
			}else {
				user.setFailureNumber(failureNumber);
				user.setFailureTime(failureTime);
				dao.update(user, "failureNumber", "failureTime");
				if(failureNumber != null) {
					AssertUtil.check(false, "用户名或密码不正确");
				}
			}
		}

		if(user.getOrg() == null){
			List<UpOrderQuota> quotas = dao.list(UpOrderQuota.class, MapUtil.of("owner", user, "quotaStatus", UpProductSystemEnums.QuotaStatus.unfinish));
			List<SpOrg> orgs = queryDao.queryHql("from SpOrg where status = 'active' and customNo is null and name not in ('System','Public') and (isDelete is null or isDelete = false) order by name", null);

			if (quotas.size() > 0 && orgs.size() > 0) {
				SpOrg org = dao.load(SpOrg.class, orgs.get(0).getId());
				RestCustom custom = user.getCustom();
				org.setCustomNo(custom.getCustomNo());
				org.setIsDelete(false);
				org.setRuntimeStatus(custom.getRuntimeStatus());
				org.setOrgStatus(SpOrgStatus.assigned);
				this.dao.update(org, "customNo", "isDelete", "runtimeStatus", "orgStatus");
				user.setOrg(org);

				List<UpOrderQuotaDetail> quotaDetailList = new ArrayList<>();
				quotas.stream().forEach((quota) -> {
					quota.setSpOrg(org);
					quota.getQuotaDetailList().stream().forEach((detail) -> {
						detail.setSpOrg(org);
					});
					quotaDetailList.addAll(quota.getQuotaDetailList());
				});
				this.dao.update(quotas, "spOrg");
				this.dao.update(quotaDetailList, "spOrg");
			}

		}

//		dao.update(user);
		SpRegionBean region = bean.getRegion() == null ? spRegionService.CIDCRP12() : bean.getRegion();

		ThreadCache.initOperationLogLocal(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName());
		AssertUtil.check(RecordStatus.isNormal(user.getStatus()), "用户状态不正确");

		upOperationLogService.saveOperationLog(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName(), null);
		return buildTokenMap(user.getId(), region);
	}

	@Override
	public Map<String, Object> buildTokenMap(Integer userId, SpRegionBean region){
		UpUser user = dao.load(UpUser.class, userId);
//		AssertUtil.check(user.getOrg(),user.getName()+ "未绑定租户资源");
		List<SpServerConnection> serverConnectionList = dao.list(SpServerConnection.class);
		serverConnectionList = serverConnectionList.stream().filter(serverConnection -> RecordStatus.active.equals(serverConnection.getStatus())).collect(Collectors.toList());
		SpOrg org = user.getOrg();
		Map<String, Object> map = new HashMap<>();
		//map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + user.getOrg().getId() + ":" + bean.getSsoToken()));
		map.put("id",user.getId());
		map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + (user.getOrg() == null ? "" : user.getOrg().getId()) + ":" + region + ":" + System.currentTimeMillis()));
		map.put("username", user.getUsername());
		map.put("isAdmin", user.getSystemAdmin() == null ? false : user.getSystemAdmin());
		if(org != null) {
			map.put("isArchiveUser", org.getIsArchive() == null ? false : org.getIsArchive());
		}
		map.put("region", region);
		recordLogout();
		return map;
	}

	@Override
	public Map<String, Object> ssoLogin(RestTokenBean bean) {
		GenTokenParams params = null;
		try {
			logger.info("[SSOLOGIN URLEncoder]" + bean.getToken());
			params = JWTTokenUtil.getParamsByToken(URLDecoder.decode(bean.getToken(), "utf-8"));
		} catch (Exception e) {
			logger.error("[SSO LOGIN INFO] error",e);
			AssertUtil.check(false, "登录失败！");
		}
		logger.info("[SSO LOGIN INFO]"+JSONObject.fromObject(params));
		AssertUtil.check(params != null && params.getId() != null, "登录失败！");

		List<UpUser> users = dao.list(UpUser.class,"ssoUserId", params.getId());
		AssertUtil.check(users.size() != 0, "未找到用户");

		UpUser user = users.get(0);
		AssertUtil.check(RecordStatus.isNormal(user.getStatus()), "用户状态不正确");

		if(user.getOrg() == null){
			logger.info("user org is null, assign one.");
			List<UpOrderQuota> quotas = dao.list(UpOrderQuota.class, MapUtil.of("owner", user, "quotaStatus", UpProductSystemEnums.QuotaStatus.unfinish));
			AssertUtil.check(quotas.size() > 0, "未找到有效订单");
			List<SpOrg> orgs = queryDao.queryHql("from SpOrg where status = 'active' and customNo is null and name not in ('System','Public') and (isDelete is null or isDelete = false) order by name", null);
			AssertUtil.check(orgs.size() > 0, "租户资源不足，请联系管理员");
			SpOrg org = dao.load(SpOrg.class, orgs.get(0).getId());
			RestCustom custom = user.getCustom();
			org.setCustomNo(custom.getCustomNo());
			org.setIsDelete(false);
			org.setRuntimeStatus(custom.getRuntimeStatus());
			org.setOrgStatus(SpOrgStatus.assigned);
			this.dao.update(org, "customNo", "isDelete", "runtimeStatus", "orgStatus");
			user.setOrg(org);

			List<UpOrderQuotaDetail> quotaDetailList = new ArrayList<>();
			quotas.stream().forEach((quota) -> {
				quota.setSpOrg(org);
				quota.getQuotaDetailList().stream().forEach((detail) -> {
					detail.setSpOrg(org);
				});
				quotaDetailList.addAll(quota.getQuotaDetailList());
			});
			this.dao.update(quotas, "spOrg");
			this.dao.update(quotaDetailList, "spOrg");
		}

		if(user.getSuspend() == null || user.getSuspend() == true) {
			user.setSuspend(false);
		}
		dao.update(user, "suspend", "org");

		ThreadCache.initOperationLogLocal(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName());
		upOperationLogService.saveOperationLog(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName(), null);
//		List<SpServerConnection> serverConnectionList = dao.list(SpServerConnection.class);
//		serverConnectionList = serverConnectionList.stream().filter(serverConnection -> RecordStatus.active.equals(serverConnection.getStatus())).collect(Collectors.toList());
//		SpServerConnection serverConnection = ListUtil.first(serverConnectionList);
		SpRegionBean region = spRegionService.CIDCRP35();
		return buildTokenMap(user.getId(), region);
	}

	private boolean passwordAuthenticate(UpUserBean bean, UpUser user) {
		return Utility.isNotEmpty(bean.getPassword()) && bean.getPassword().equals(user.getPassword());
	}

	private boolean ldapAuthenticate(UpUserBean userBean) {
		if (authenticationProvider == null) {
			UpSystemConfigBean systemConfigBean = upSystemConfigService.get(UpSystemConfigKey.ldap_server_json);
			if (Utility.isNotEmpty(systemConfigBean.getValue())) {
				Map dataMap = RemoteUtil.gson.fromJson(systemConfigBean.getValue(), Map.class);
				String ldap_server_url = (String) dataMap.get("ldap_server_url"); // ldap://192.168.1.10:389
				if (Utility.isNotEmpty(ldap_server_url)) {
					if ("ad".equals(dataMap.get("ldap_auth_type"))) {
						String ldap_domain = (String) dataMap.get("ldap_domain"); // pso.cn
						String ldap_base_dn = (String) dataMap.get("ldap_base_dn"); // dc=pso,dc=cn
						authenticationProvider = new ActiveDirectoryLdapAuthenticationProvider(ldap_domain, ldap_server_url, ldap_base_dn);
					} else if ("ldap".equals(dataMap.get("ldap_auth_type"))) {
						String ldap_manager_dn = (String) dataMap.get("ldap_manager_dn");
						String ldap_manager_pwd = (String) dataMap.get("ldap_manager_pwd");
						contextSource = new DefaultSpringSecurityContextSource(ldap_server_url);
						contextSource.setUserDn(ldap_manager_dn);
						contextSource.setPassword(ldap_manager_pwd);
						contextSource.afterPropertiesSet();
						contextSource.getReadOnlyContext();

						BindAuthenticator bindAuthenticator = new BindAuthenticator(contextSource);
						String[] ldap_user_dn_patterns = Utility.toEmpty((String) dataMap.get("ldap_user_dn_patterns")).replace(" ", "").replace("\r\n", "\n").split("\n"); // cn={0},cn=Users,dc=pso,dc=cn
						bindAuthenticator.setUserDnPatterns(ldap_user_dn_patterns);
						String ldap_base_dn = (String) dataMap.get("ldap_base_dn"); // cn=Users,dc=pso,dc=cn
						// String ldap_search_filter = Utility.defaultIfEmpty((String)
						// dataMap.get("ldap_search_filter"), "(sAMAccountName={0})");
						String ldap_search_filter = Utility.defaultIfEmpty((String) dataMap.get("ldap_search_filter"), "(uid={0})");
						bindAuthenticator.setUserSearch(new FilterBasedLdapUserSearch(ldap_base_dn, ldap_search_filter, contextSource));
						authenticationProvider = new LdapAuthenticationProvider(bindAuthenticator);
					}
				}
			}
		}
		if (Utility.isEmpty(userBean.getPassword())) {
			return false;
		}
		if (authenticationProvider != null) {
			try {
				Authentication authentication = new UsernamePasswordAuthenticationToken(userBean.getName(), EncryptUtil.decryptWithRSA(userBean.getPassword()));
				authentication = authenticationProvider.authenticate(authentication);
				return authentication.isAuthenticated();
			} catch (Exception e) {
				logger.error("ldapAuthenticate error. (" + userBean.getName() + ")", e);
			}
		}
		return false;
	}

	private boolean ssoAuthenticate(UpUserBean userBean) {
		return false;
	}

	@Override
	public void notifySyncLdapUser() {
		upApplicationService.createSimpleOperateTask(UpTaskType.ldap_user_sync, UpTaskType.ldap_user_sync.getTitle(), null);
	}

	@Override
	protected void doTaskRunning(UpTask task) {
		super.doTaskRunning(task);
		if (UpTaskType.ldap_user_sync.equals(task.getType())) {
			syncLdapUser();
			task.setTaskStatus(UpTaskStatus.finish);
			task.setStatusMessage(null);
		}
	}

	private void syncLdapUser() {
		if (authenticationProvider == null) {
			ldapAuthenticate(new UpUserBean());
		}
		if (contextSource != null) {
			UpSystemConfigBean systemConfigBean = upSystemConfigService.get(UpSystemConfigKey.ldap_server_json);
			Map dataMap = RemoteUtil.gson.fromJson(systemConfigBean.getValue(), Map.class);
			String ldap_base_dn = (String) dataMap.get("ldap_base_dn"); // cn=Users,dc=pso,dc=cn

			String ldap_name = Utility.defaultIfEmpty((String) dataMap.get("ldap_name"), "uid");
			String ldap_display_name = Utility.defaultIfEmpty((String) dataMap.get("ldap_display_name"), "displayName");
			String ldap_mail = Utility.defaultIfEmpty((String) dataMap.get("ldap_mail"), "mail");
			String ldap_mobile = Utility.defaultIfEmpty((String) dataMap.get("ldap_mobile"), "mobile");
			String ldap_telephone = Utility.defaultIfEmpty((String) dataMap.get("ldap_telephone"), "telephoneNumber");
			String[] ldap_user_ou = Utility.defaultIfEmpty((String) dataMap.get("ldap_user_ou"), "ou").replace(" ", "").split(",");

			deepFind(contextSource.getReadOnlyContext(), ldap_base_dn, "", ldap_name, ldap_display_name, ldap_mail, ldap_mobile, ldap_telephone, ldap_user_ou);
		}
	}

	private void deepFind(final DirContext dirContext, String fullDn, String userOu, final String ldap_name, final String ldap_display_name, final String ldap_mail, final String ldap_mobile,
						  final String ldap_telephone, final String[] ldap_user_ou) {
		try {
			NamingEnumeration<NameClassPair> list = dirContext.list(fullDn);
			while (list.hasMore()) {
				NameClassPair pair = list.next();
				fullDn = pair.getNameInNamespace();
				DirContextAdapter dirContextAdapter = null;
				try {
					dirContextAdapter = (DirContextAdapter) dirContext.lookup(fullDn);
				} catch (NamingException e) {
					logger.error("LDAP find error : " + fullDn, e);
					continue;
				}
				String name = dirContextAdapter.getStringAttribute(ldap_name);
				if (Utility.isNotEmpty(name)) {
					UpUserLdap userLdap = ListUtil.first(dao.list(UpUserLdap.class, "name", name));
					if (userLdap == null) {
						userLdap = new UpUserLdap();
					}
					userLdap.setName(name);
					userLdap.setDisplayName(dirContextAdapter.getStringAttribute(ldap_display_name));
					userLdap.setEmail(dirContextAdapter.getStringAttribute(ldap_mail));
					userLdap.setMobile(dirContextAdapter.getStringAttribute(ldap_mobile));
					userLdap.setTelephone(dirContextAdapter.getStringAttribute(ldap_telephone));
					userLdap.setUserOu(userOu);
					userLdap.setUserDn(fullDn);
					if (userLdap.getId() == null) {
						dao.insert(userLdap);
					} else {
						dao.update(userLdap);
						if (userLdap.getUser() != null) {
							UpUser user = userLdap.getUser();
							user.setName(name);
							user.setDisplayName(Utility.defaultIfEmpty(userLdap.getDisplayName(), user.getDisplayName()));
							user.setDisplayName(IDisplayName.appendDisplayNameSuffix(user.getDisplayName()));
							user.setEmail(Utility.defaultIfEmpty(userLdap.getEmail(), user.getEmail()));
							user.setMobile(Utility.defaultIfEmpty(userLdap.getMobile(), user.getMobile()));
							user.setTelephone(Utility.defaultIfEmpty(userLdap.getTelephone(), user.getTelephone()));
							dao.update(user);
						}
					}
				} else {
					if (Utility.isNotEmpty(ldap_user_ou)) {
						StringBuilder sb = new StringBuilder();
						for (int i = 0; i < ldap_user_ou.length; i++) {
							if (i != 0) {
								sb.append(", ");
							}
							sb.append(dirContextAdapter.getStringAttribute(ldap_user_ou[i]));
						}
						userOu = sb.toString();
					}
					deepFind(dirContext, fullDn, userOu, ldap_name, ldap_display_name, ldap_mail, ldap_mobile, ldap_telephone, ldap_user_ou);
				}
			}
		} catch (NamingException e) {
			throw new SystemException(fullDn, e);
		}
	}

	@Override
	public void bindUserOrg() {
		List<UpUser> users = this.queryDao.queryHql("from UpUser where (systemAdmin = false or systemAdmin = null ) and org = null and status = '" + RecordStatus.active + "' and (suspend <> true or suspend = null)", null);
		if (users == null || users.size() == 0) {
			return;
		}

		List<SpOrg> orgs = this.queryDao.queryHql(
				"from SpOrg where LOWER(name) <> 'system' and LOWER(name) <> 'public' and (orgStatus <> 'assigned' or orgStatus = null) and status = '" + RecordStatus.active + "' order by id", null);

		if (orgs == null || orgs.size() == 0) {
			return;
		}

		String enddate = new SimpleDateFormat("yyyyMMdd").format(DateUtils.addDays(new Date(),-1));
		RemoteHost host = new RemoteHost(userDeviceUri);
		Header header = new BasicHeader("secretKey",secretKey);
		List<Header> headerList = new ArrayList<>();
		headerList.add(header);
		host.setHeaderList(headerList);
		UserAction action = new UserAction(host);
		for (UpUser user : users) {
			if (user.getSystemAdmin() != null && user.getSystemAdmin()) {
				continue;
			}
			if("yes".equals(checkUserDevice)) {
				if(!checkUserDevice(action,user,enddate)) {
					continue;
				}
			}
			SpOrg dbOrg = null;
			for (SpOrg org : orgs) {
				if (org.getName().toLowerCase().equals("system") || org.getName().toLowerCase().equals("public")) {
					continue;
				}

				if (SpOrgStatus.assigned.equals(org.getOrgStatus())) {
					continue;
				}
				try {
					org = this.updateSpOrg(org);
					org.setOrgStatus(SpOrgStatus.assigned);
					dbOrg = org;
					break;
				} catch (Exception e) {

				}
			}
			if (dbOrg == null) {
				break;
			}
			user.setOrg(dbOrg);
			this.dao.update(user, "org");
		}
	}

	@Override
	public Integer bindUserOrg(UpUser user) {
		AssertUtil.check(user != null && user.getId() != null , "用户数据异常");
		user = dao.load(UpUser.class, user.getId());
		if(user.getOrg()!=null) {
			return user.getOrg().getId();
		}
		List<SpOrg> orgs = this.queryDao.queryHql(
				"from SpOrg where LOWER(name) <> 'system' and LOWER(name) <> 'public' and (orgStatus <> 'assigned' or orgStatus = null) and status = '" + RecordStatus.active + "' order by id", null);

		if (orgs == null || orgs.size() == 0) {
			return null;
		}

		String enddate = new SimpleDateFormat("yyyyMMdd").format(DateUtils.addDays(new Date(),-1));
		RemoteHost host = new RemoteHost(userDeviceUri);
		Header header = new BasicHeader("secretKey",secretKey);
		List<Header> headerList = new ArrayList<>();
		headerList.add(header);
		host.setHeaderList(headerList);
		UserAction action = new UserAction(host);
		if (user.getSystemAdmin() != null && user.getSystemAdmin()) {
			return null;
		}
		if("yes".equals(checkUserDevice)) {
			if(!checkUserDevice(action,user,enddate)) {
				return null;
			}
		}
		SpOrg dbOrg = null;
		for (SpOrg org : orgs) {
			if (org.getName().toLowerCase().equals("system") || org.getName().toLowerCase().equals("public")) {
				continue;
			}

			if (SpOrgStatus.assigned.equals(org.getOrgStatus())) {
				continue;
			}
			try {
				org = this.updateSpOrg(org);
				org.setOrgStatus(SpOrgStatus.assigned);
				dbOrg = org;
				break;
			} catch (Exception e) {

			}
		}
		if (dbOrg == null) {
			return null;
		}
		user.setOrg(dbOrg);
		this.dao.update(user, "org");
		return dbOrg.getId();
	}

	private boolean checkUserDevice(UserAction action, UpUser user, String enddate) {
		logger.info("[USER_DEVICE_PARAMS] UCID:" + user.getUcId());
		if(user.getUcId()==null) {
			return false;
		}
		String callBackString = action.getUserDevice(user.getUcId(), enddate);
//		String callBackString = "{\"code\":\"0000\",\"msg\":\"成功\",\"data\":{\"data\":[{\"user_id\":\"123\",\"dev_cnt\":\"0\",\"day_id\":\"aaa\"}]}}";
		logger.info("[USER_DEVICE_CALLBACK]: " + callBackString);
		JSONObject callBackJson = JSONObject.fromObject(callBackString);
		if(callBackJson.containsKey("code") && "0000".equals(callBackJson.getString("code"))) {
			if(callBackJson.containsKey("data") && callBackJson.get("data") != null) {
				JSONObject dataObject = callBackJson.getJSONObject("data");
				if(dataObject.containsKey("data") && dataObject.get("data")!=null) {
					JSONArray array = dataObject.getJSONArray("data");
					if(array.size() > 0) {
						JSONObject obj = array.getJSONObject(0);
						if(obj!= null && obj.containsKey("dev_cnt") && obj.get("dev_cnt")!=null && obj.containsKey("user_id") && obj.get("user_id")!=null) {
							if(user.getUcId().toString().equals(obj.getString("user_id"))) {
								if(obj.containsKey("dev_cnt") && obj.get("dev_cnt")!= null && obj.getInt("dev_cnt")>0){
									return true;
								}
							}
						}
					}
				}
			}
		}
		return false;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
    public SpOrg updateSpOrg(SpOrg org) {
		String hql = "update SpOrg set orgStatus = '" + SpOrgStatus.assigned + "', updateTm = '" + new Date() + "' where (orgStatus <> 'assigned' or orgStatus = null) and id = " + org.getId();
		int i = this.queryDao.doExecuteHql(hql, null);
		if (i > 0) {
			return org;
		}
		throw new RuntimeException();
	}

	@Override
	public Integer getUserOrg(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		SpOrg org = user.getOrg();
		if (org != null) {
			return org.getId();
		}
		return null;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void powerOffVm(SpVm entity) {
		spVmService.powerOff(entity);
	}

	@Override
	public String getUserType() {
		UpUser user = this.dao.load(UpUser.class, ThreadCache.getUserId());
		return user.getType();
	}

	@Override
	public UpUserBean[] query(UpUserSearchBean searchBean, UpUser entity) {
		List<UpUser> users = this.dao.query(searchBean, entity);
		return BeanCopyUtil.copy2BeanList(users, UpUserBean.class);
	}

	@Override
	public void recordLogout() {
		UpUser user = ThreadCache.getUser();
		if(user == null) {
			return;
		}
		UpUserLogout entity = new UpUserLogout();
		entity.setName(user.getRegion().toString());
		entity.setUser(user);
		entity.setInvalidTimeMillis(user.getTimeMillis());
		dao.insert(entity);
	}

	@Override
	public boolean checkToken() {
		UpUser user = ThreadCache.getUser();
		if(user == null) {
			return false;
		}
		return dao.list(UpUserLogout.class, MapUtil.of("user", user, "invalidTimeMillis", user.getTimeMillis())).size() == 0;
	}

	@Override
	public UpUserBean addUser(UpUserBean bean) {
		UpUser admin = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(admin.getTenantAdmin(), "当前用户无操作权限");
		AssertUtil.check(bean, "新增用户信息错误");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getName()), "账号不能为空");

		if(dao.list(UpUser.class, "name", bean.getName()).size() > 0) {
			AssertUtil.check(false, "账号已存在");
		}

		bean.setUsername(bean.getUsername());
		bean.setDisplayName(StringUtils.isEmpty(bean.getDisplayName()) ? bean.getName() : bean.getDisplayName());

		UpUser entity = BeanCopyUtil.copy(bean, UpUser.class);
		entity.setSystemAdmin(false);
		entity.setTenantAdmin(false);

		if(bean.getOrgAdmin()){
			entity.setTenantAdmin(true);
		}
		entity.setPassword(EncryptUtil.encryptWithRSA("1qaz@WSX"));
		entity.setIsDelete(false);
		this.dao.insert(entity);

		bean.setId(entity.getId());
		return bean;
//		if(bean.getOrgId()!=null) {
//			SpOrg org = this.dao.load(SpOrg.class, bean.getOrgId());
//			if(org.getOrgStatus() == null || org.getOrgStatus().equals(SpOrgStatus.unassigned)) {
//				org.setOrgStatus(SpOrgStatus.assigned);
//				this.dao.update(org, "orgStatus");
//			}
//		}
	}

	@Override
	public void addAndUpdate(UpUserBean bean) {
		UpUser admin = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(admin.getTenantAdmin(), "当前用户无操作权限");
		AssertUtil.check(bean, "新增用户信息错误");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getName()), "账号不能为空");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getPassword()), "密码不能为空");
		AssertUtil.check(bean.getTenantAdmin() != null, "请选择用户角色");
		EncryptUtil.convertPassword(Arrays.asList(bean), true);

		List<UpUser> dbUserList = dao.list(UpUser.class, "name", bean.getName());
		if(bean.getId() == null){
			AssertUtil.check(dbUserList.size() == 0, "账号已存在");
			UpUser entity = BeanCopyUtil.copy(bean, UpUser.class);
			entity.setSystemAdmin(bean.getTenantAdmin());
			entity.setUsername(bean.getUsername());
			entity.setDisplayName(bean.getDisplayName());
			entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
			entity.setIsDelete(false);
			dao.insert(entity);
		}else{
			AssertUtil.check(dbUserList.size() == 1 && dbUserList.get(0).getId().equals(bean.getId()), "账号已存在");
			UpUser dbEntity = dbUserList.get(0);
			dbEntity.setName(bean.getName());
			dbEntity.setUsername(bean.getName());
			dbEntity.setDisplayName(bean.getDisplayName());
			dbEntity.setPassword(bean.getPassword());
			dbEntity.setTenantAdmin(bean.getTenantAdmin());
			dbEntity.setSystemAdmin(bean.getTenantAdmin());
			dbEntity.setIsDelete(bean.getIsDelete());
			dao.update(dbEntity);
		}
	}

	@Override
	public void delete(Integer id) {
		dao.delete(UpUser.class, id);
	}
}
