package io.aicloudware.portal.framework.action;

import io.aicloudware.portal.framework.remote.RemoteHost;

public class ConnectSSOAction extends RemoteUpModuleAction{

	public ConnectSSOAction(RemoteHost remoteHost) {
		super(remoteHost, "");
	}

	public String getToken(String appid,String appsecret,String version) {
		return getRemoteHost().stringGet("/passport/" + version + "/token/index?appid=" + appid +"&appsecret=" + appsecret);
	}
	
	public String getUserByOpenId(String openid,String version) {
		return getRemoteHost().stringGet("/passport/" + version + "/user/getuserbyopenid?openid=" + openid);
	}
    
}
