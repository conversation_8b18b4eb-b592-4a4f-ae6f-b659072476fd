package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpOperationLogService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserSearchBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.RSAUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpServerConnection;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class RestUserService extends BaseService implements IRestUserService {

	@Autowired
	private IUpOperationLogService upOperationLogService;

	@Autowired
	private ISpRegionService spRegionService;

	@Override
	@Transactional(propagation = Propagation.NEVER, readOnly = true)
	public Map<String, Object> login(UpUserBean bean) {
		AssertUtil.check(Utility.isNotEmpty(bean.getName()), "用户名不能为空");
		UpUserSearchBean searchBean = new UpUserSearchBean();
		UpUser user = new UpUser();

		user.setName(bean.getName());
		List<UpUser> userList = dao.query(searchBean, user);
//			AssertUtil.check(userList.size() <= 1, "用户名重复");
		AssertUtil.check(userList.size() != 0, "用户名不正确");

		user = userList.get(0);
		AssertUtil.check(RecordStatus.isNormal(user.getStatus()), "用户状态不正确");

//		dao.update(user);
		SpRegionBean region = bean.getRegion() == null ? spRegionService.CIDCRP35() : bean.getRegion();
		
		ThreadCache.initOperationLogLocal(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName());
		AssertUtil.check(RecordStatus.isNormal(user.getStatus()), "用户状态不正确");

		upOperationLogService.saveOperationLog(UpOperationType.user_login.getTitle(), user, UpOperationType.user_login, UpUser.class, user.getId(), user.getName(), null);
		return buildTokenMap(user.getId(), region);
	}

	private Map<String, Object> buildTokenMap(Integer userId, SpRegionBean region){
		UpUser user = dao.load(UpUser.class, userId);
//		AssertUtil.check(user.getOrg(),user.getName()+ "未绑定租户资源");
		List<SpServerConnection> serverConnectionList = dao.list(SpServerConnection.class);
		serverConnectionList = serverConnectionList.stream().filter(serverConnection -> RecordStatus.active.equals(serverConnection.getStatus())).collect(Collectors.toList());
		SpOrg org = user.getOrg();
		Map<String, Object> map = new HashMap<>();
		//map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + user.getOrg().getId() + ":" + bean.getSsoToken()));
		map.put("token", RSAUtil.encrypt(user.getId() + ":" + user.getSystemAdmin() + ":" + user.getTenantAdmin() + ":" + (user.getOrg() == null ? "" : user.getOrg().getId()) + ":" + region + ":" + System.currentTimeMillis()));
		map.put("username", user.getUsername());
		map.put("isAdmin", user.getSystemAdmin() == null ? false : user.getSystemAdmin());
		if(org != null) {
			map.put("isArchiveUser", org.getIsArchive() == null ? false : org.getIsArchive());
		}
		map.put("region", region);
		return map;
	}

}
