package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机费用统计结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpVmCostResultBean extends ResultListBean<UpVmCostBean> {

    @ApiModelProperty(value = "年度（查询条件下拉框）")
    private Integer[] fiscalYearList;

    @ApiModelProperty(value = "部门（查询条件下拉框）")
    private UpGroupBean[] groupList;

    @ApiModelProperty(value = "合计费用（汇总信息）")
    private UpVmCostBean totalCost;


    public UpVmCostBean getTotalCost() {
        return totalCost;
    }

    public UpGroupBean[] getGroupList() {
        return groupList;
    }

    public void setGroupList(UpGroupBean[] groupList) {
        this.groupList = groupList;
    }

    public Integer[] getFiscalYearList() {
        return fiscalYearList;
    }

    public void setFiscalYearList(Integer[] fiscalYearList) {
        this.fiscalYearList = fiscalYearList;
    }

    public void setTotalCost(UpVmCostBean totalCost) {
        this.totalCost = totalCost;
    }

}
