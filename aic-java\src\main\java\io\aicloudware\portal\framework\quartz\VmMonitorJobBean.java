package io.aicloudware.portal.framework.quartz;


import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.platform_vcd.service.ISpVappService;

@Component
public final class VmMonitorJobBean extends QuartzJobBean {
    protected final Logger logger = Logger.getLogger(getClass());

    @Value("${monitor_job}")
	private String quartz;
    
    @Autowired
    protected ISpVappService vappService;

    @Autowired
	private IUpSystemConfigService configService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    	UpSystemConfigBean bean = configService.get(UpSystemConfigKey.operation_control);
    	if(bean!=null && StringUtils.isNotEmpty(bean.getValue()) && "yes".equals(bean.getValue())) {
    		return;
    	}
    	if(!"yes".equals(quartz)) {
    		return;
    	}
        List<JobExecutionContext> jobs;
        try {
            jobs = jobExecutionContext.getScheduler().getCurrentlyExecutingJobs();
            for (JobExecutionContext job : jobs) {
                if (job.getTrigger().equals(jobExecutionContext.getTrigger())
                        && !job.getFireInstanceId().equals(jobExecutionContext.getFireInstanceId())) {
//                    logger.debug("There's another instance running, so leaving " + this);
                    return;
                }
            }
        } catch (SchedulerException e) {
            logger.trace("HourlyJobBean.executeInternal() error", e);
            return;
        }
        vappService.monitor();
        
    }
}
