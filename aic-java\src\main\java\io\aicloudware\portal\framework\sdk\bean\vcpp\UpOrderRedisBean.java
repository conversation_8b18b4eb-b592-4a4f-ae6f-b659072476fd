package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "redis")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderRedisBean.class})
public class UpOrderRedisBean extends SpRecordBean {

	@ApiModelProperty(value = "付费方式")
	private PaymentType paymentType;
	
	@ApiModelProperty(value = "类型")
	private RedisType redisType;
	
	@ApiModelProperty(value = "配置ID")
	private Integer redisConfigId;
	
	@ApiModelProperty(value = "vpcId")
    private Integer vpcId;
	
	@ApiModelProperty(value = "子网ID")
	private Integer ovdcNetworkId;
	
	@ApiModelProperty(value = "Redis数据库连接密码")
	private String password;

	@ApiModelProperty(value = "所有人ID")
    private Integer ownerId;
	
	@ApiModelProperty(value = "订单ID")
    private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "主机名")
	private String hostname;
	
	@ApiModelProperty(value = "分片")
	private Integer sharding;
	
	@ApiModelProperty(value = "数量")
	private Integer amount;

	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public RedisType getRedisType() {
		return redisType;
	}

	public void setRedisType(RedisType redisType) {
		this.redisType = redisType;
	}

//	public RedisStandard getRedisStandard() {
//		return redisStandard;
//	}
//
//	public void setRedisStandard(RedisStandard redisStandard) {
//		this.redisStandard = redisStandard;
//	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getOvdcNetworkId() {
		return ovdcNetworkId;
	}

	public void setOvdcNetworkId(Integer ovdcNetworkId) {
		this.ovdcNetworkId = ovdcNetworkId;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public String getHostname() {
		return hostname;
	}

	public void setHostname(String hostname) {
		this.hostname = hostname;
	}

	public Integer getRedisConfigId() {
		return redisConfigId;
	}

	public void setRedisConfigId(Integer redisConfigId) {
		this.redisConfigId = redisConfigId;
	}

	public Integer getSharding() {
		return sharding;
	}

	public void setSharding(Integer sharding) {
		this.sharding = sharding;
	}

}
