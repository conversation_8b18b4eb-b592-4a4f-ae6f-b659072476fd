package io.aicloudware.portal.api_vcpp.controller.product;

import io.aicloudware.portal.api_vcpp.entity.UpFormDefinition;
import io.aicloudware.portal.api_vcpp.service.product.IUpFormDefinitionService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/form")
public class UpFormDefinitionController extends BaseEntityController<UpFormDefinition, UpFormDefinitionBean, UpFormDefinitionResultBean> {

    @Autowired
    private IUpFormDefinitionService updFormDefinitionService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询表单定义列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回表单定义列表", response = UpFormDefinitionResultBean.class) })
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpFormDefinitionSearchBean searchBean) {
        try {
            // 使用Service层的query方法，支持分页查询
            UpFormDefinitionBean[] entityList = updFormDefinitionService.query(searchBean);

            UpFormDefinitionResultBean result = new UpFormDefinitionResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "新增表单定义")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = String.class) })
    @ResponseBody
    public ResponseBean add(@ApiParam(value = "表单定义参数") @RequestBody UpFormDefinitionBean bean) {
        updFormDefinitionService.add(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(notes = "/update", httpMethod = "PUT", value = "更新表单定义")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = String.class) })
    @ResponseBody
    public ResponseBean update(@ApiParam(value = "表单定义参数") @RequestBody UpFormDefinitionBean bean) {
        updFormDefinitionService.update(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete", httpMethod = "DELETE", value = "删除表单定义")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = String.class) })
    @ResponseBody
    public ResponseBean delete(@ApiParam(value = "表单定义ID") @PathVariable Integer id) {
        updFormDefinitionService.delete(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(notes = "/list", httpMethod = "POST", value = "查询表单定义列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回表单定义列表", response = UpFormDefinitionResultBean.class) })
    @ResponseBody
    public ResponseBean list(@ApiParam(value = "查询参数") @RequestBody UpFormDefinitionBean params) {
        UpFormDefinitionBean[] dataList = updFormDefinitionService.list(params);
        UpFormDefinitionResultBean result = new UpFormDefinitionResultBean();
        result.setDataList(dataList);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/get", httpMethod = "GET", value = "根据ID获取表单定义")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回表单定义", response = UpFormDefinitionBean.class) })
    @ResponseBody
    public ResponseBean getById(@ApiParam(value = "表单定义ID") @PathVariable Integer id) {
        UpFormDefinitionBean bean = updFormDefinitionService.getById(id);
        return ResponseBean.success(bean);
    }
}