package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "主机资源使用情况")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, VcHostUsageBean.class})
public class VcHostUsageBean extends RecordBean {

    @ApiModelProperty(value = "日期")
    private Integer date;

    @ApiModelProperty(value = "主机ID")
    private Integer hostId;

    @ApiModelProperty(value = "主机名称")
    private String hostName;

    @ApiModelProperty(value = "CPU总量")
    private Long cpuTotalHz;

    @ApiModelProperty(value = "CPU使用")
    private Long cpuUsageHz;

    @ApiModelProperty(value = "内存总量")
    private Long memoryTotalB;

    @ApiModelProperty(value = "内存使用")
    private Long memoryUsageB;

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public Integer getHostId() {
        return hostId;
    }

    public void setHostId(Integer hostId) {
        this.hostId = hostId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public Long getCpuTotalHz() {
        return cpuTotalHz;
    }

    public void setCpuTotalHz(Long cpuTotalHz) {
        this.cpuTotalHz = cpuTotalHz;
    }

    public Long getCpuUsageHz() {
        return cpuUsageHz;
    }

    public void setCpuUsageHz(Long cpuUsageHz) {
        this.cpuUsageHz = cpuUsageHz;
    }

    public Long getMemoryTotalB() {
        return memoryTotalB;
    }

    public void setMemoryTotalB(Long memoryTotalB) {
        this.memoryTotalB = memoryTotalB;
    }

    public Long getMemoryUsageB() {
        return memoryUsageB;
    }

    public void setMemoryUsageB(Long memoryUsageB) {
        this.memoryUsageB = memoryUsageB;
    }
}
