package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpBlueprintMachineType;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "蓝图计算机")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVappTemplateMachineBean.class})
public class SpVappTemplateMachineBean extends SpRecordBean {

    @ApiModelProperty(value = "蓝图机器类型")
    private SpBlueprintMachineType type;

    /* 蓝图_虚机ID(vRA蓝图里虚机分配的唯一ID) */
    @ApiModelProperty(value = "蓝图计算机ID")
    private String instanceId;

    /* 描述 */
    @ApiModelProperty(value = "蓝图描述信息")
    private String description;

    /* 预留策略 */
    @ApiModelProperty(value = "预留策略ID")
    private Integer reservationPolicyId;

    @ApiModelProperty(value = "预留策略名称")
    private String reservationPolicyName;

    @ApiModelProperty(value = "预留策略显示名称")
    private String reservationPolicyDisplayName;

    @ApiModelProperty(value = "计算机名前缀")
    private String prefixName;

    @ApiModelProperty(value = "最小实例数")
    private Integer minInstanceNum;

    @ApiModelProperty(value = "最大实例数")
    private Integer maxInstanceNum;

    @ApiModelProperty(value = "clone源")
    private String cloneFrom;

    @ApiModelProperty(value = "自定义规范")
    private String customizeSpecification;

    @ApiModelProperty(value = "CPU 最小值")
    private Integer minCpuNum;

    @ApiModelProperty(value = "CPU 最大值")
    private Integer maxCpuNum;

    @ApiModelProperty(value = "内存(GB) 最小值")
    private Integer minMemoryGB;

    @ApiModelProperty(value = "内存(GB) 最大值")
    private Integer maxMemoryGB;

    @ApiModelProperty(value = "存储(GB) 最小值")
    private Integer minDiskGB;

    @ApiModelProperty(value = "存储(GB) 最大值")
    private Integer maxDiskGB;

    @ApiModelProperty(value = "磁盘列表")
    private SpVappTemplateMachineDiskBean[] diskList;

    @ApiModelProperty(value = "蓝图机器关系ID")
    private Integer relationId;

    @ApiModelProperty(value = "属性组列表")
    private SpPropertyGroupBean[] propertyGroupList;

    public SpBlueprintMachineType getType() {
        return type;
    }

    public void setType(SpBlueprintMachineType type) {
        this.type = type;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getReservationPolicyId() {
        return reservationPolicyId;
    }

    public void setReservationPolicyId(Integer reservationPolicyId) {
        this.reservationPolicyId = reservationPolicyId;
    }

    public String getReservationPolicyName() {
        return reservationPolicyName;
    }

    public void setReservationPolicyName(String reservationPolicyName) {
        this.reservationPolicyName = reservationPolicyName;
    }

    public String getReservationPolicyDisplayName() {
        return reservationPolicyDisplayName;
    }

    public void setReservationPolicyDisplayName(String reservationPolicyDisplayName) {
        this.reservationPolicyDisplayName = reservationPolicyDisplayName;
    }

    public String getPrefixName() {
        return prefixName;
    }

    public void setPrefixName(String prefixName) {
        this.prefixName = prefixName;
    }

    public Integer getMinInstanceNum() {
        return minInstanceNum;
    }

    public void setMinInstanceNum(Integer minInstanceNum) {
        this.minInstanceNum = minInstanceNum;
    }

    public Integer getMaxInstanceNum() {
        return maxInstanceNum;
    }

    public void setMaxInstanceNum(Integer maxInstanceNum) {
        this.maxInstanceNum = maxInstanceNum;
    }

    public String getCloneFrom() {
        return cloneFrom;
    }

    public void setCloneFrom(String cloneFrom) {
        this.cloneFrom = cloneFrom;
    }

    public String getCustomizeSpecification() {
        return customizeSpecification;
    }

    public void setCustomizeSpecification(String customizeSpecification) {
        this.customizeSpecification = customizeSpecification;
    }

    public Integer getMinCpuNum() {
        return minCpuNum;
    }

    public void setMinCpuNum(Integer minCpuNum) {
        this.minCpuNum = minCpuNum;
    }

    public Integer getMaxCpuNum() {
        return maxCpuNum;
    }

    public void setMaxCpuNum(Integer maxCpuNum) {
        this.maxCpuNum = maxCpuNum;
    }

    public Integer getMinMemoryGB() {
        return minMemoryGB;
    }

    public void setMinMemoryGB(Integer minMemoryGB) {
        this.minMemoryGB = minMemoryGB;
    }

    public Integer getMaxMemoryGB() {
        return maxMemoryGB;
    }

    public void setMaxMemoryGB(Integer maxMemoryGB) {
        this.maxMemoryGB = maxMemoryGB;
    }

    public Integer getMinDiskGB() {
        return minDiskGB;
    }

    public void setMinDiskGB(Integer minDiskGB) {
        this.minDiskGB = minDiskGB;
    }

    public Integer getMaxDiskGB() {
        return maxDiskGB;
    }

    public void setMaxDiskGB(Integer maxDiskGB) {
        this.maxDiskGB = maxDiskGB;
    }

    public SpVappTemplateMachineDiskBean[] getDiskList() {
        return diskList;
    }

    public void setDiskList(SpVappTemplateMachineDiskBean[] diskList) {
        this.diskList = diskList;
    }

    public Integer getRelationId() {
        return relationId;
    }

    public void setRelationId(Integer relationId) {
        this.relationId = relationId;
    }

    public SpPropertyGroupBean[] getPropertyGroupList() {
        return propertyGroupList;
    }

    public void setPropertyGroupList(SpPropertyGroupBean[] propertyGroupList) {
        this.propertyGroupList = propertyGroupList;
    }

}
