package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡虚拟机服务器统计")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerVirtualServerStatisticsBean.class})
public class SpLoadBalancerVirtualServerStatisticsBean extends SpRecordBean {

    @ApiModelProperty(value = "负载均衡虚拟服务器ID")
    private Integer loadBalancerVirtualServerId;
    
    @ApiModelProperty(value = "vs_status")
    private String vsStatus;
    
    @ApiModelProperty(value = "time_stamp")
    private Long timeStamp;
    
    @ApiModelProperty(value = "byte_in")
    private Long byteIn;
    
    @ApiModelProperty(value = "byte_out")
    private Long byteOut;
    
    @ApiModelProperty(value = "http_req_total")
    private Long httpReqTotal;
    
    @ApiModelProperty(value = "total_sessions")
    private Long totalSessions;

	public Integer getLoadBalancerVirtualServerId() {
		return loadBalancerVirtualServerId;
	}

	public void setLoadBalancerVirtualServerId(Integer loadBalancerVirtualServerId) {
		this.loadBalancerVirtualServerId = loadBalancerVirtualServerId;
	}

	public String getVsStatus() {
		return vsStatus;
	}

	public void setVsStatus(String vsStatus) {
		this.vsStatus = vsStatus;
	}

	public Long getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(Long timeStamp) {
		this.timeStamp = timeStamp;
	}

	public Long getByteIn() {
		return byteIn;
	}

	public void setByteIn(Long byteIn) {
		this.byteIn = byteIn;
	}

	public Long getByteOut() {
		return byteOut;
	}

	public void setByteOut(Long byteOut) {
		this.byteOut = byteOut;
	}

	public Long getHttpReqTotal() {
		return httpReqTotal;
	}

	public void setHttpReqTotal(Long httpReqTotal) {
		this.httpReqTotal = httpReqTotal;
	}

	public Long getTotalSessions() {
		return totalSessions;
	}

	public void setTotalSessions(Long totalSessions) {
		this.totalSessions = totalSessions;
	}
	
    

}
