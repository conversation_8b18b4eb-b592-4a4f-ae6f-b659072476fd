package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderRedisService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CatalogType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisNodeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.MapUtil;

import io.swagger.annotations.ApiParam;

/**
 * redis
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/redis")
public class UpOrderRedisController extends BaseController {

	@Autowired
	private IUpOrderRedisService redisService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpProductService productService;

	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        Map<String, String> redisType = new LinkedHashMap<String, String>();
        
        for (RedisType type : RedisType.values()) {
        	redisType.put(type.toString(), type.getTitle());
        }
        datas.put("redisType", redisType);
        
        Map<RedisType, Object> redisNodeType = new LinkedHashMap<>();
        
//        redisNodeType.put(RedisType.standard, MapUtil.of(RedisNodeType.single, RedisNodeType.single.getTitle(), RedisNodeType.master_replica, RedisNodeType.master_replica.getTitle()));
        redisNodeType.put(RedisType.cluster, MapUtil.of(RedisNodeType.master_replica, RedisNodeType.master_replica.getTitle()));
        datas.put("redisNodeType", redisNodeType);
        

        // 镜像
        datas.put("images", orderService.getTemplatesByCatalog(CatalogType.redis));
        
        // 网络
        datas.put("vpc", orderService.getVPC(id));
        
        // redis产品配置
        datas.put("serverConfigs", productService.queryVmSet(ProductVmSetType.redis, ThreadCache.getRegion()).values());
        
		return ResponseBean.success(datas);
	}

	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean quotaInit(@ApiParam(value = "对象ID") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        Map<String, String> redisType = new LinkedHashMap<String, String>();
        redisType.put(RedisType.cluster.toString(), RedisType.cluster.getTitle());
        datas.put("redisType", redisType);
        
        Map<RedisType, Object> redisNodeType = new LinkedHashMap<>();
        redisNodeType.put(RedisType.cluster, MapUtil.of(RedisNodeType.master_replica, RedisNodeType.master_replica.getTitle()));
        datas.put("redisNodeType", redisNodeType);
        // 镜像
        datas.put("images", orderService.getTemplatesByCatalog(CatalogType.redis));
        // 网络
        datas.put("vpc", orderService.getVPC(user.getId()));
        // redis产品配置
        datas.put("serverConfigs",productService.queryVmSet(ProductVmSetType.redis,code).values());
        
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.CLOUD_REDIS, description = "管理员新增订单")
	public ResponseBean save(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(redisService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.CLOUD_REDIS, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(redisService.save(bean, ThreadCache.getUser()));
	}
	
	@RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.CLOUD_REDIS, description = "更新订单")
	public ResponseBean update(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(redisService.update(bean, ThreadCache.getUser()));
	}
}