package io.aicloudware.portal.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpdateTarget;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.GroupSequence;
import javax.validation.constraints.NotNull;

@ApiModel(value = "批量操作显示名称")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, DisplayNameOperateBean.class})
public class DisplayNameOperateBean extends BaseBean {

    @ApiModelProperty(value = "修改目标")
    private UpdateTarget target;

    @ApiModelProperty(value = "对象ID")
    @NotNull(message = "{validation.required}", groups = V02.class)
    private Integer id;

    @ApiModelProperty(value = "显示名称")
    @NotEmpty(message = "{validation.required}", groups = V03.class)
    private String displayName;

    public UpdateTarget getTarget() {
        return target;
    }

    public void setTarget(UpdateTarget target) {
        this.target = target;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}
