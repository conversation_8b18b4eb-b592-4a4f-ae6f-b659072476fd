package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ObjectStorageType;
import io.aicloudware.portal.platform_vcd.entity.SpObjectStorageBucket;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_object_storage_bucket")
@Access(AccessType.FIELD)
public class UpOrderObjectStorageBucket extends UpOrderProduct<UpOrderObjectStorageBucketBean> implements IOrderEntity {


	@JoinColumn(name = "quota_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuota quota;

	@JoinColumn(name = "quota_detail_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuotaDetail quotaDetail;

	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private ObjectStorageType type;

	@JoinColumn(name = "owner_id", nullable = false)
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser owner;

	@JoinColumn(name = "order_id", nullable = false)
	@ManyToOne(fetch = FetchType.LAZY)
	private UpOrder order;

	@JoinColumn(name = "org_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;

	@Column(name = "size_g")
	private Long sizeG;

	@Column(name = "disk_config_id")
	private Integer diskConfigId;

	@JoinColumn(name = "object_storage_bucket_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpObjectStorageBucket spObjectStorageBucket;

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public Long getSizeG() {
		return sizeG;
	}

	public void setSizeG(Long sizeG) {
		this.sizeG = sizeG;
	}

	public Integer getDiskConfigId() {
		return diskConfigId;
	}

	public void setDiskConfigId(Integer diskConfigId) {
		this.diskConfigId = diskConfigId;
	}

	public UpOrderQuota getQuota() {
		return quota;
	}

	public void setQuota(UpOrderQuota quota) {
		this.quota = quota;
	}

	public UpOrderQuotaDetail getQuotaDetail() {
		return quotaDetail;
	}

	public void setQuotaDetail(UpOrderQuotaDetail quotaDetail) {
		this.quotaDetail = quotaDetail;
	}

	public ObjectStorageType getType() {
		return type;
	}

	public void setType(ObjectStorageType type) {
		this.type = type;
	}

	public SpObjectStorageBucket getSpObjectStorageBucket() {
		return spObjectStorageBucket;
	}

	public void setSpObjectStorageBucket(SpObjectStorageBucket spObjectStorageBucket) {
		this.spObjectStorageBucket = spObjectStorageBucket;
	}

}
