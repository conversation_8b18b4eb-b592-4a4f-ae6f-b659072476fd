package io.aicloudware.portal.api_vcpp.service.quota;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Transactional
public class UpOrderQuotaService extends BaseService implements IUpOrderQuotaService {

	@Override
	public List<UpOrderQuotaDetail> queryQuotaDetail(SpOrg spOrg, ProductType type, QuotaCatalog catalog) {
		return dao.list(UpOrderQuotaDetail.class, MapUtil.of("type", type, "spOrg", spOrg, "catalog", catalog, "region", ThreadCache.getRegion()));
	}

	@Override
	public UpOrderQuotaDetail getQuotaDetail(SpOrg spOrg, SpRegionEntity region, String doorOrderItemId) {
		AssertUtil.check(doorOrderItemId != null, "doorOrderItemId不能为空");
		List<UpOrderQuotaDetail> quotaDetailList = dao.list(UpOrderQuotaDetail.class, MapUtil.of("spOrg", spOrg, "region", region, "subCode", doorOrderItemId, "quotaDetailStatus", QuotaDetailStatus.start));
		AssertUtil.check(!quotaDetailList.isEmpty(), "未找到订单信息");
		return quotaDetailList.get(0);
	}

	@Override
	public UpOrderQuotaBean[] queryQuota(String type, QuotaCatalog catalog) {
		
		UpOrderQuotaSearchBean searchBean = new UpOrderQuotaSearchBean();
		searchBean.setPageSize(Integer.MAX_VALUE);
		
		UpOrderQuota params = new UpOrderQuota();
		params.setSpOrg(new SpOrg(ThreadCache.getOrgId()));
		params.setOwner(ThreadCache.getUser());
		params.setType(ProductType.valueOf(type));
		params.setQuotaStatus(QuotaStatus.unfinish);
		params.setRegion(ThreadCache.getRegion());
		params.setCatalog(catalog);
		List<UpOrderQuota> list = ListUtil.toList(dao.query(searchBean, params), (l, entity) -> {
			for(UpOrderQuotaDetail detail : entity.getQuotaDetailList()) {
				if(detail.getQuotaDetailStatus() == QuotaDetailStatus.start) {
					l.add(entity);
					break;
				}
			}
		});
		
		UpOrderQuotaBean[] quotaBeans = BeanCopyUtil.copy2BeanList(list,UpOrderQuotaBean.class);
		for(UpOrderQuotaBean bean : quotaBeans) {
			List<UpOrderQuotaDetailBean> details = new ArrayList<>();
			for(UpOrderQuotaDetailBean detail : bean.getQuotaDetailList()) {
				if(detail.getQuotaDetailStatus() == QuotaDetailStatus.start) {
					details.add(detail);
				}
			}
			bean.setQuotaDetailList(details.toArray(new UpOrderQuotaDetailBean[details.size()]));
		}
		
		return quotaBeans;
	}
	
	@Override
	public void finishQuotaDetail(Integer quotaDetailId) {
		if(quotaDetailId == null) {
			return;
		}
		UpOrderQuotaDetail detail = this.dao.load(UpOrderQuotaDetail.class, quotaDetailId);
		AssertUtil.check(detail, "未找到订单项");
//		AssertUtil.check(detail.getQuotaDetailStatus().equals(QuotaDetailStatus.deploying), "订单项状态异常");
		detail.setQuotaDetailStatus(QuotaDetailStatus.finish);
		UpOrderQuota quota = this.dao.load(UpOrderQuota.class, detail.getQuota().getId());
		boolean isQuotaFinish = true;
		for(int i = 0 ; i < quota.getQuotaDetailList().size() ; i ++) {
			UpOrderQuotaDetail item = quota.getQuotaDetailList().get(i);
			if(item.getQuotaDetailStatus() != QuotaDetailStatus.finish && !item.getId().equals(detail.getId())) {
				isQuotaFinish = false;
				break;
			}
		}
		if(isQuotaFinish) {
			quota.setQuotaStatus(QuotaStatus.finish);
			this.dao.update(quota);
		}
		this.dao.update(detail);
	}

	@Override
	public void deployQuotaDetail(Integer quotaDetailId) {
		if(quotaDetailId == null) {
			return;
		}
		UpOrderQuotaDetail detail = this.dao.load(UpOrderQuotaDetail.class, quotaDetailId);
		AssertUtil.check(detail, "未找到订单项");
//		AssertUtil.check(detail.getQuotaDetailStatus() == QuotaDetailStatus.start, "订单项状态异常");
		detail.setQuotaDetailStatus(QuotaDetailStatus.deploying);
		this.dao.update(detail);
	}

	@Override
	public void deployQuotaDetail(UpOrderQuotaDetail detail) {
		AssertUtil.check(detail, "未找到订单项");
//		AssertUtil.check(detail.getQuotaDetailStatus() == QuotaDetailStatus.start, "订单项状态异常");
		detail.setQuotaDetailStatus(QuotaDetailStatus.deploying);
		this.dao.update(detail);
	}
	
	@Override
	public void openQuotaDetail(Integer quotaDetailId) {
		if(quotaDetailId == null) {
			return;
		}
		UpOrderQuotaDetail detail = this.dao.load(UpOrderQuotaDetail.class, quotaDetailId);
		AssertUtil.check(detail, "未找到订单项");
//		AssertUtil.check(detail.getQuotaDetailStatus() == QuotaDetailStatus.finish, "订单项状态异常");
		detail.setQuotaDetailStatus(QuotaDetailStatus.start);
		this.dao.update(detail);
		UpOrderQuota quota = this.dao.load(UpOrderQuota.class, detail.getQuota().getId());
		quota.setQuotaStatus(QuotaStatus.unfinish);
		this.dao.update(quota);
	}

	@Override
	public UpOrderQuotaBean add(UpOrderQuotaDetailBean bean) {
		AssertUtil.check(bean, "输入信息异常");
		AssertUtil.check(bean.getSpOrgId(), "缺少spOrgId");
		AssertUtil.check(bean.getRegion(), "缺少region");
		AssertUtil.check(bean.getType(), "缺少type");
		AssertUtil.check(bean.getProductCode(), "缺少productCode");

		UpOrderQuotaDetail detail = BeanCopyUtil.copy(bean, UpOrderQuotaDetail.class);
		detail.setCatalog(detail.getCatalog() == null ? QuotaCatalog.NEW : detail.getCatalog());
		detail.setName(bean.getSpOrgId() + "~" + bean.getType() + "~" + bean.getProductCode());
		detail.setQuotaDetailStatus(QuotaDetailStatus.start);
		detail.setSubCode(bean.getType() + "-" + bean.getProductCode() + "-" + System.currentTimeMillis());

		SpOrg org = dao.load(SpOrg.class, detail.getSpOrg().getId());
		UpUser user = dao.list(UpUser.class, MapUtil.of("org", org, "type", "sso")).get(0);

		UpOrderQuota quota = new UpOrderQuota(detail.getRegion(), detail.getCatalog(), detail.getSubCode(), detail.getName(), user, org, detail.getType(), org.getCustomNo());
		quota.setQuotaDetailList(new ArrayList<>(Arrays.asList(detail)));
		dao.insert(quota);
		return BeanCopyUtil.copy(quota, UpOrderQuotaBean.class);
	}

	@Override
	public void deleteQuotaDetail(Integer quotaDetailId) {
		if(quotaDetailId == null) {
			return;
		}
		UpOrderQuotaDetail detail = this.dao.load(UpOrderQuotaDetail.class, quotaDetailId);
		AssertUtil.check(detail, "未找到订单项");
//		AssertUtil.check(detail.getQuotaDetailStatus() == QuotaDetailStatus.start, "订单项状态异常");
		UpOrderQuota quota = this.dao.load(UpOrderQuota.class, detail.getQuota().getId());
		boolean isQuotaFinish = true;
		for(int i = 0 ; i < quota.getQuotaDetailList().size() ; i ++) {
			UpOrderQuotaDetail item = quota.getQuotaDetailList().get(i);
			if(item.getQuotaDetailStatus() != QuotaDetailStatus.finish && !item.getId().equals(detail.getId())) {
				isQuotaFinish = false;
				break;
			}
		}
		if(isQuotaFinish) {
			quota.setQuotaStatus(QuotaStatus.finish);
			this.dao.update(quota);
		}
//		detail.setQuotaDetailStatus(QuotaDetailStatus.finish);
		this.dao.delete(UpOrderQuotaDetail.class, quotaDetailId);
	}


}