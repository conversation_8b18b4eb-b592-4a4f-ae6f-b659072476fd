package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.framework.sdk.contants.UpUploadFileType;
import io.aicloudware.portal.framework.utility.Utility;

import java.util.Locale;
import java.util.TimeZone;

public class SystemConfigUtil {
    private static boolean debugMode = false;
    private static Locale locale = Locale.getDefault();
    private static TimeZone timeZone = TimeZone.getDefault();
    private static String rootPath = null;

    public static void setRootPath(String rootPath) {
        SystemConfigUtil.rootPath = rootPath;
    }

    public static boolean isWindows() {
        return Utility.toEmpty(System.getProperty("os.name")).toLowerCase().contains("windows");
    }

    public static Locale getLocale() {
        return locale;
    }

    public static void setLocale(Locale locale) {
        SystemConfigUtil.locale = locale;
    }

    public static TimeZone getTimeZone() {
        return timeZone;
    }

    public static void setTimeZone(TimeZone timeZone) {
        SystemConfigUtil.timeZone = timeZone;
    }

    public static String getRootPath() {
        return rootPath;
    }

    public static String getConfigPath() {
        return getRootPath() + "config/";
    }

    public static String getDataPath() {
        return getRootPath() + "data/";
    }

    public static String getFtpLocalPath() {
        return getDataPath() + "ftp_local/";
    }

    public static String getExportFilePath() {
        return getRootPath() + "export_file/";
    }

    public static String getUploadFilePath(UpUploadFileType fileType) {
        return getRootPath() + "upload_file/" + fileType.name() + "/";
    }
}
