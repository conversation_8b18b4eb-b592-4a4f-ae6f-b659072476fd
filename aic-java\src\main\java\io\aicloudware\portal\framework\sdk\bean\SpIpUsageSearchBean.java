package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "IP地址使用情况查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpIpUsageSearchBean extends SearchBean<SpIpUsageBean> {

    @ApiModelProperty(value = "IP地址列表")
    private String[] ipAddressList;

    public String[] getIpAddressList() {
        return ipAddressList;
    }

    public void setIpAddressList(String[] ipAddressList) {
        this.ipAddressList = ipAddressList;
    }
}
