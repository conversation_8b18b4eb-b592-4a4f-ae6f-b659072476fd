package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.UpPermissionType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "权限")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpPermissionBean.class})
public class UpPermissionBean extends RecordBean {

    @ApiModelProperty(value = "所有者用户ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者用户名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "虚机可用数量")
    private SpService service;

    private String serviceText;

    @ApiModelProperty(value = "vm_used_num")
    private UpPermissionType permissionType;

    private String permissionTypeText;

    @ApiModelProperty(value = "sp_org_id")
    private Integer orgId;

    @ApiModelProperty(value = "sp_org_name")
    private String orgName;

    @ApiModelProperty(value = "区域")
    private SpRegionBean region;

    private Boolean isUserTemplate;

    private String url;

    private Boolean enabledStatus;

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public UpPermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(UpPermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public SpRegionBean getRegion() {
        return region;
    }

    public void setRegion(SpRegionBean region) {
        this.region = region;
    }

    public Boolean getUserTemplate() {
        return isUserTemplate;
    }

    public void setUserTemplate(Boolean userTemplate) {
        isUserTemplate = userTemplate;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getEnabledStatus() {
        return enabledStatus;
    }

    public void setEnabledStatus(Boolean enabledStatus) {
        this.enabledStatus = enabledStatus;
    }

    public String getServiceText() {
        return serviceText;
    }

    public void setServiceText(String serviceText) {
        this.serviceText = serviceText;
    }

    public String getPermissionTypeText() {
        return permissionTypeText;
    }

    public void setPermissionTypeText(String permissionTypeText) {
        this.permissionTypeText = permissionTypeText;
    }
}
