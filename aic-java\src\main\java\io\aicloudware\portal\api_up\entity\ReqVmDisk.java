package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskBean;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "req_vm_disk")
@Access(AccessType.FIELD)
public class ReqVmDisk extends CmVmDisk<ReqVm> implements IRequestEntity<SpVmDiskBean> {

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @Column(name = "orig_id")
    private Integer origId;

    @Column(name = "operate_type")
    @Enumerated(EnumType.STRING)
    private UpOperateType operateType;

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public Integer getOrigId() {
        return origId;
    }

    public void setOrigId(Integer origId) {
        this.origId = origId;
    }

    public UpOperateType getOperateType() {
        return operateType;
    }

    public void setOperateType(UpOperateType operateType) {
        this.operateType = operateType;
    }
}
