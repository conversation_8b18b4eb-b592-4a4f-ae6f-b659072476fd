# 部门Controller模块设计文档

## 概述

本设计文档描述了UpDepartmentController的实现方案，该控制器将为部门管理提供完整的REST API接口。设计基于现有的Spring MVC架构，遵循项目的编码规范和架构模式，参考UpUserController的实现风格。

## 架构

### 技术栈
- Spring MVC框架
- Hibernate ORM
- Swagger API文档
- Jackson JSON序列化
- 基于BaseUpController的继承架构

### 分层架构
```
Controller层 (UpDepartmentController)
    ↓
Service层 (IUpDepartmentService/UpDepartmentService)
    ↓
Entity层 (UpDepartment, UpDepartmentUserRelation)
    ↓
数据库层 (up_department, up_department_user_relation)
```

## 组件和接口

### 1. UpDepartmentController

**继承关系：**
```java
public class UpDepartmentController extends BaseUpController<UpDepartment, UpDepartmentBean, UpDepartmentResultBean>
```

**主要职责：**
- 处理HTTP请求和响应
- 参数验证和错误处理
- 调用Service层业务逻辑
- 返回统一格式的ResponseBean

**核心接口设计：**

#### 基础CRUD接口
- `POST /department/addOrUpdate` - 创建或更新部门
- `POST /department/query` - 查询部门列表
- `GET /department/load/{id}` - 获取部门详情
- `DELETE /department/delete/{id}` - 删除部门

#### 部门树结构接口
- `GET /department/tree` - 获取完整部门树
- `GET /department/tree/{parentId}` - 获取指定父部门的子部门树

#### 部门用户关系接口
- `POST /department/addUsers` - 添加用户到部门
- `POST /department/removeUsers` - 从部门移除用户
- `GET /department/users/{departmentId}` - 获取部门用户列表
- `GET /department/userDepartments/{userId}` - 获取用户所属部门
- `POST /department/setManager` - 设置部门管理员

### 2. 数据传输对象 (DTOs)

#### UpDepartmentResultBean
```java
public class UpDepartmentResultBean extends ResultListBean<UpDepartmentBean> {
    // 继承分页信息和数据列表
}
```

#### UpDepartmentSearchBean
```java
public class UpDepartmentSearchBean extends SearchBean<UpDepartmentBean> {
    // 继承查询条件和分页参数
}
```

#### 部门用户操作Bean
```java
public class UpDepartmentUserOperateBean {
    private Integer departmentId;
    private Integer[] userIds;
    private String relationType;
    private Boolean isManager;
}
```

### 3. Service层增强

需要完善UpDepartmentService中缺失的方法实现：

```java
// 用户部门关系管理
void addUsersToDepart(Integer departmentId, List<Integer> userIds, String relationType);
void removeUsersFromDepartment(Integer departmentId, List<Integer> userIds);
UpDepartmentUserRelationBean[] getDepartmentUsers(Integer departmentId);
UpDepartmentBean[] getUserDepartments(Integer userId);
void setDepartmentManager(Integer departmentId, Integer userId, Boolean isManager);
```

## 数据模型

### 核心实体关系
```
UpDepartment (部门)
├── parentDepartment: UpDepartment (父部门，自关联)
├── childDepartments: List<UpDepartment> (子部门列表)
└── departmentUserRelations: List<UpDepartmentUserRelation> (用户关系)

UpDepartmentUserRelation (部门用户关系)
├── department: UpDepartment (所属部门)
├── user: UpUser (关联用户)
├── relationType: UpDepartmentUserRelationType (关系类型)
└── isManager: Boolean (是否管理员)
```

### 数据流转
```
HTTP Request → Controller → Bean Validation → Service → Entity → Database
Database → Entity → Service → Bean Conversion → Controller → HTTP Response
```

## 错误处理

### 异常处理策略
1. **参数验证错误** - 返回400 Bad Request
2. **资源不存在** - 返回404 Not Found
3. **业务逻辑错误** - 返回400 Bad Request with error message
4. **权限不足** - 返回403 Forbidden
5. **系统错误** - 返回500 Internal Server Error

### 错误响应格式
```json
{
    "success": false,
    "code": "ERROR_CODE",
    "message": "错误描述",
    "data": null
}
```

### 业务规则验证
- 删除部门前检查是否有子部门
- 删除部门前检查是否有关联用户
- 创建子部门时验证父部门存在性
- 用户部门关系的唯一性检查

## 测试策略

### 单元测试
- Controller层：测试HTTP请求处理和响应格式
- Service层：测试业务逻辑和数据操作
- 异常处理：测试各种错误场景

### 集成测试
- 数据库操作：测试CRUD操作的完整性
- 事务处理：测试数据一致性
- API端到端：测试完整的请求响应流程

### 测试数据准备
- 创建测试部门层级结构
- 准备测试用户数据
- 模拟各种业务场景

## 安全考虑

### 权限控制
- 基于ThreadCache的用户身份验证
- 部门操作权限检查
- 数据访问范围限制（基于region和org）

### 数据安全
- 输入参数验证和清理
- SQL注入防护（通过Hibernate）
- 敏感信息过滤

### 操作审计
- 记录部门创建、更新、删除操作
- 记录用户部门关系变更
- 集成现有的UpOperationLogService

## 性能优化

### 查询优化
- 部门树查询使用懒加载
- 批量操作用户部门关系
- 合理使用数据库索引

### 缓存策略
- 部门树结构缓存（如需要）
- 用户部门关系缓存（如需要）

### 分页处理
- 大量数据查询使用分页
- 合理设置默认页面大小

## API文档规范

### Swagger注解
- @Api：控制器级别描述
- @ApiOperation：方法级别描述
- @ApiParam：参数描述
- @ApiResponse：响应描述
- @ApiModel：数据模型描述

### 文档示例
```java
@ApiOperation(
    notes = "/department/addOrUpdate", 
    httpMethod = "POST", 
    value = "创建或更新部门"
)
@ApiResponses(value = {
    @ApiResponse(code = 200, message = "操作成功", response = UpDepartmentBean.class),
    @ApiResponse(code = 400, message = "参数错误"),
    @ApiResponse(code = 500, message = "系统错误")
})
```

## 实现细节

### 控制器方法命名规范
- `addOrUpdate()` - 创建或更新
- `query()` - 查询列表
- `load()` - 获取详情
- `delete()` - 删除
- `getDepartmentTree()` - 获取部门树
- `addUsersToDepart()` - 添加用户到部门
- `removeUsersFromDepartment()` - 移除用户
- `setDepartmentManager()` - 设置管理员

### 响应格式统一
所有接口都返回ResponseBean格式：
```java
return ResponseBean.success(data);  // 成功
return ResponseBean.error(message); // 失败
```

### 事务管理
- Service层方法使用@Transactional注解
- 复杂操作确保数据一致性
- 异常时自动回滚

## 部署和配置

### 依赖注入
```java
@Autowired
private IUpDepartmentService upDepartmentService;

@Autowired
private IUpOperationLogService upOperationLogService;
```

### URL映射
```java
@RequestMapping("/department")
@Api(value = "/department", description = "部门管理", position = 520)
```

### 跨域配置
继承现有的跨域配置，无需额外设置。