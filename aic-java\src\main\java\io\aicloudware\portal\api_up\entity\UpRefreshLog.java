package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.criterion.DetachedCriteria;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Date;
import java.util.Set;

@Entity
@Table(name = "up_refresh_log")
@Access(AccessType.FIELD)
public class UpRefreshLog extends BaseUpEntity<UpTaskBean> implements IEnvironmentEntity<UpTaskBean> {

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @JoinColumn(name = "order_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UpOrder order;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private UpOrderSystemEnums.RefreshType type;

    @Column(name = "task_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpTaskStatus taskStatus;

    @Column(name = "status_message", length = ApiConstants.STRING_MAX_LENGTH)
    private String statusMessage;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "expect_tm", nullable = false)
    private Date expectTime;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpTaskBean> searchBean, Set<String> aliasSet) {
        UpTaskSearchBean bean = (UpTaskSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getTaskStatusList())) {
            DaoUtil.addInValues(criteria, "taskStatus", Arrays.asList(bean.getTaskStatusList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    public void setType(UpOrderSystemEnums.RefreshType type) {
        this.type = type;
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public UpOrderSystemEnums.RefreshType getType() {
        return type;
    }

    public UpTaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(UpTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public Date getExpectTime() {
        return expectTime;
    }

    public void setExpectTime(Date expectTime) {
        this.expectTime = expectTime;
    }
}
