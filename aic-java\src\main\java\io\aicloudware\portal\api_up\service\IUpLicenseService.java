package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpLicenseBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public interface IUpLicenseService {

    public UpLicenseBean createLicense(UpLicenseBean bean);

    public void saveLicense(String licenseCode);

    public void validateLicense();

    public UpLicenseBean getLicenseBean();
}
