package io.aicloudware.portal.framework.dao;

import io.aicloudware.portal.framework.entity.IEntity;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface IQueryDao extends IDao {

    public <T> List<T> queryHql(String hql, Map<String, Object> paramMap);

    public int doExecuteHql(String hql, Map<String, Object> paramMap);

    public <T> List<T> querySql(String sql, Map<String, Object> paramMap);

    public <T extends IEntity> List<T> querySql(String sql, Map<String, Object> paramMap, Class<T> returnType);

    public int doExecuteSql(String sql, Map<String, Object> paramMap);

    public void execute(Work work);
}
