package io.aicloudware.portal.framework.remote;


import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

public class NsxStringResponseHandler extends BaseResponseHandler<String> {
    private static final Logger logger = Logger.getLogger(NsxStringResponseHandler.class);

    private final String charset;

    public NsxStringResponseHandler(String charset) {
        this.charset = charset;
    }
    
    @Override
    public String handleResponse(HttpResponse response) throws IOException {
        final StatusLine statusLine = response.getStatusLine();
        String message = statusLine.getStatusCode() + " : " + statusLine.getReasonPhrase();
        HttpEntity entity = response.getEntity();
        if (statusLine.getStatusCode() >= 400) {
        	InputStreamReader isr = new  InputStreamReader(response.getEntity().getContent());
    		Map<String, Object> responseMap = RemoteUtil.gson.fromJson(isr, Map.class);
    		throw new RuntimeException((String)responseMap.get("error_message"));
        } else if (statusLine.getStatusCode() >= 300) {
            logger.warn(message + " : " + getLocation());
        } else {
            logger.trace(message);
        }
        if (entity == null) {
            return null;
        }
        return handleEntity(entity);
    }
    

    @Override
    protected String handleEntity(HttpEntity entity) {
        if (Utility.isNotEmpty(getLocation())) {
            return getLocation();
        }
        try {
            return EntityUtils.toString(entity, charset);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
