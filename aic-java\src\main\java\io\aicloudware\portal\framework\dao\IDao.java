package io.aicloudware.portal.framework.dao;

import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface IDao {

    public <E extends IEntity> E load(Class<E> clazz, Integer id);

    public <E extends IEntity> E load(Class<E> clazz, Integer id, LazyLoad<E> lazyLoad);
    
    public <E extends IEntity> E loadFullStatus(Class<E> clazz, Integer id);

    public <E extends IEntity> List<E> listIncludeDeleted(Class<E> clazz);

    public <E extends IEntity> List<E> list(Class<E> clazz);

    public <E extends IEntity> List<E> list(Class<E> clazz, String property, Object value);

    public <E extends IEntity> List<E> list(Class<E> clazz, Map<String, Object> paramMap);
    
    public <E extends IEntity> Map<Integer, E> map(Class<E> clazz, Collection<Integer> idList);

    public <E extends IEntity<B>, B extends RecordBean> List<E> query(SearchBean<B> searchBean, E entity);

    public <E extends IEntity<B>, B extends RecordBean> List<E> query(SearchBean<B> searchBean, E entity, LazyLoad<E> lazyLoad);

    public <E extends IEntity> E insert(E entity);

    public <E extends IEntity> Collection<E> insert(Collection<E> entityList);

    public <E extends IEntity> E update(E entity, String... names);

    public <E extends IEntity> Collection<E> update(Collection<E> entityList, String... names);

    public <E extends IEntity> boolean delete(Class<E> clazz, Integer id);

    public <E extends IEntity> boolean delete(Class<E> clazz, Collection<Integer> idList);

    public <E extends IEntity> boolean destroy(Class<E> clazz, Integer id);

    public <E extends IEntity> boolean destroy(Class<E> clazz, Collection<Integer> idList);

    public <E extends IEntity> void evict(Collection<E> entityList);

    public void flush();

    public void clear();

    public <E extends IEntity> void validateDuplicateName(Class<E> clazz, Collection<String> nameList);

}
