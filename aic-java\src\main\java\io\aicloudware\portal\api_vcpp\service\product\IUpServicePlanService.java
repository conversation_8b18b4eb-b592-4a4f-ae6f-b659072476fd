package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.framework.sdk.bean.UpServicePlanBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;

public interface IUpServicePlanService {

	void add(UpServicePlanBean bean);

	void update(UpServicePlanBean bean);

	void delete(Integer id);

	UpServicePlanBean[] listByType(UpServicePlanType type);

	UpServicePlanBean[] list(UpServicePlanBean params);
}
