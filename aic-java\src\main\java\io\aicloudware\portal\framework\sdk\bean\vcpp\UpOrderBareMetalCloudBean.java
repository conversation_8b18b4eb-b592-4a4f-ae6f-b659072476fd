package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.BareMetalCloudType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "裸金属")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderBareMetalCloudBean.class})
public class UpOrderBareMetalCloudBean extends SpRecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "类型")
	private BareMetalCloudType type;
	
	@ApiModelProperty(value = "产品编码")
	private String productCode;
	
	@ApiModelProperty(value = "CPU主频")
	private String cpuClockSpeedGHz;
	
	@ApiModelProperty(value = "CPU核数")
	private Integer cpuNum;
	
	@ApiModelProperty(value = "缓存")
	private Integer cpuCacheM;
	
	@ApiModelProperty(value = "内存")
	private Integer memoryGB;
	
	@ApiModelProperty(value = "存储数")
	private Integer diskNum;
	
	@ApiModelProperty(value = "存储大小")
	private String disk;
	
	@ApiModelProperty(value = "存储类型")
	private String diskType;
	
	@ApiModelProperty(value = "多模光口数")
	private Integer multiModeFiberNum;
	
	@ApiModelProperty(value = "镜像名")
	private String templateName;
	
	@ApiModelProperty(value = "镜像名")
	private String templateId;
	
	@ApiModelProperty(value = "主机名")
	private String hostName;
	
	@ApiModelProperty(value = "密码")
	private String password;
	
	@ApiModelProperty(value = "所有人")
	private Integer ownerId;

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public String getCpuClockSpeedGHz() {
		return cpuClockSpeedGHz;
	}

	public void setCpuClockSpeedGHz(String cpuClockSpeedGHz) {
		this.cpuClockSpeedGHz = cpuClockSpeedGHz;
	}

	public Integer getCpuNum() {
		return cpuNum;
	}

	public void setCpuNum(Integer cpuNum) {
		this.cpuNum = cpuNum;
	}

	public Integer getCpuCacheM() {
		return cpuCacheM;
	}

	public void setCpuCacheM(Integer cpuCacheM) {
		this.cpuCacheM = cpuCacheM;
	}

	public Integer getMemoryGB() {
		return memoryGB;
	}

	public void setMemoryGB(Integer memoryGB) {
		this.memoryGB = memoryGB;
	}

	public Integer getDiskNum() {
		return diskNum;
	}

	public void setDiskNum(Integer diskNum) {
		this.diskNum = diskNum;
	}

	public String getDisk() {
		return disk;
	}

	public void setDisk(String disk) {
		this.disk = disk;
	}

	public String getDiskType() {
		return diskType;
	}

	public void setDiskType(String diskType) {
		this.diskType = diskType;
	}

	public Integer getMultiModeFiberNum() {
		return multiModeFiberNum;
	}

	public void setMultiModeFiberNum(Integer multiModeFiberNum) {
		this.multiModeFiberNum = multiModeFiberNum;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public BareMetalCloudType getType() {
		return type;
	}

	public void setType(BareMetalCloudType type) {
		this.type = type;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}
	
}
