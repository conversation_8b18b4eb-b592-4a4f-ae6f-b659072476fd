package io.aicloudware.portal.api_vcpp.controller.product;

import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/product")
public class UpProductController extends BaseController {
    @Autowired
    private IUpProductService upProductService;

    @RequestMapping(value = "/sync", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean sync() {
        upProductService.sync(ThreadCache.getOrgId(), ThreadCache.getRegion());
        return ResponseBean.success("");
    }
}
