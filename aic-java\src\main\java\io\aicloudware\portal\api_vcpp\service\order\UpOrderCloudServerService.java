package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpAppSystem;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpQuotaService;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.finance.IUpFinanceRechargeService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.*;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
@Transactional
public class UpOrderCloudServerService extends BaseService implements IUpOrderCloudServerService {

	@Autowired
	private IUpFinanceRechargeService financeRechargeService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@Autowired
	private IUpOrderQuotaService quotaService;

	@Autowired
	private IUpQuotaService upQuotaService;

	@Autowired
	private ISpRegionService spRegionService;
	
	@Override
	public List<Map<String,Object>> getKeyPairs(Integer id) {
		UpUser user = this.dao.load(UpUser.class, id);
//		SpSecureKeySearchBean search = new SpSecureKeySearchBean();
//		search.setPageSize(Integer.MAX_VALUE);
//		SpSecureKey params = new SpSecureKey();
//		params.setSpOrg(ThreadCache.getUser().getOrg());
//		
		List<Map<String,Object>> keyPairs = new ArrayList<>();
//		List<SpSecureKey> entitys = this.dao.query(search, params);
		List<SpSecureKey> entitys = this.dao.list(SpSecureKey.class,"spOrg",user.getOrg());
		for(SpSecureKey entity: entitys) {
			Map<String,Object> item = new HashMap<>();
			item.put("id", entity.getId());
			item.put("name", entity.getName());
			keyPairs.add(item);
		}
		return keyPairs;
	}
	
	@Override
	public List<Map<String,Object>> getKeyPairs() {
		
//		SpSecureKeySearchBean search = new SpSecureKeySearchBean();
//		search.setPageSize(Integer.MAX_VALUE);
//		SpSecureKey params = new SpSecureKey();
//		params.setSpOrg(ThreadCache.getUser().getOrg());
//		
		List<Map<String,Object>> keyPairs = new ArrayList<>();
//		List<SpSecureKey> entitys = this.dao.query(search, params);
		List<SpSecureKey> entitys = this.dao.list(SpSecureKey.class,"spOrg",ThreadCache.getUser().getOrg());
		for(SpSecureKey entity: entitys) {
			Map<String,Object> item = new HashMap<>();
			item.put("id", entity.getId());
			item.put("name", entity.getName());
			keyPairs.add(item);
		}
		return keyPairs;
	}

	@Override
	public Integer save(UpOrderCloudServerBean bean, UpUser applyUser) {
		
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");

		List<SpVm> vmList = dao.list(SpVm.class, MapUtil.of("spOrg", user.getOrg(), "region", ThreadCache.getRegion(), "status", RecordStatus.active, "appSystem.id", bean.getAppSystemId()));
		Integer vmCount = 0;
		Integer cpuCount = 0;
		Integer memoryGB = 0;
		if (vmList != null) {
			for (SpVm vm : vmList) {
				if (vm.getSpOrg().getId().equals(user.getOrg().getId())) {
					vmCount++;
					cpuCount += vm.getCpuNum();
					memoryGB += vm.getMemoryGB();
				}
			}
		}

		List<Object[]> runningOrders = queryDao.querySql(
				"select count(1) as vmcount, sum(ocs.cpu) as cpucount, sum(ocs.memory) as memorycount " +
				" from up_order o, up_order_cloud_server ocs where o.status='active' and o.app_system_id=:appSystemId " +
						"and type=:type and order_status in ('submit', 'approving','pending_approve','pending_deploy','deploying') " +
						"and ocs.order_id=o.id ", MapUtil.of("appSystemId", bean.getAppSystemId(), "type", OrderType.new_cloud_server.name()));
		if (runningOrders != null && runningOrders.size() > 0) {
			Object[] running = runningOrders.get(0);
			vmCount += ((BigInteger) running[0]).intValue();
			if (running[1] != null) {
				cpuCount += ((BigInteger) running[1]).intValue();
			}
			if (running[2] != null) {
				memoryGB += ((BigInteger) running[2]).intValue();
			}
		}

		int quantity = bean.getAmount();

		upQuotaService.checkQuota(SpService.ecs, SpResourceType.instances, bean.getAppSystemId(), ThreadCache.getOrgId(), quantity, vmCount);
		upQuotaService.checkQuota(SpService.ecs, SpResourceType.cores, bean.getAppSystemId(), ThreadCache.getOrgId(), bean.getCpu() * quantity, cpuCount);
		upQuotaService.checkQuota(SpService.ecs, SpResourceType.ram, bean.getAppSystemId(), ThreadCache.getOrgId(), bean.getMemory() * quantity, memoryGB);

		AssertUtil.check(bean.getServerType(), "请选择云主机类型！");
		AssertUtil.check(bean.getImageId(), "请选择镜像！");
		AssertUtil.check(bean.getVpcId(), "请选择专有网络！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
		AssertUtil.check(bean.getNetworkId(), "请选择子网！");
		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");
		
		AssertUtil.check(bean.getKeyType(), "请选择密钥方式！");
		if (bean.getKeyType().equals(KeyType.password)) {
			AssertUtil.check(bean.getPassword(), "请输入密码！");
			bean.setAccount(user.getName());
			AssertUtil.check(Pattern.compile("(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,20}").matcher(bean.getPassword()).find(),"密码不符合规则");
		} else if (bean.getKeyType().equals(KeyType.keypair)) {
			AssertUtil.check(bean.getKeyId(), "请选择密钥对！");
		}
		AssertUtil.check(bean.getName(), "请输入实例名！");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getHostname()), "请输入主机名！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getHostname()), "主机名只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getHostname()), "主机名必须包含字母！");
		AssertUtil.check(bean.getHostname().length() <= 15, "主机名长度不能超过15个字符！");
		AssertUtil.check((bean.getCloudDiskList() != null && bean.getCloudDiskList().length >= 1), "请输入磁盘大小！");
		AssertUtil.check(bean.getAmount() != null && bean.getAmount() >= 1, "请输入购买数量！");
		AssertUtil.check(bean.getAmount() <= 20, "云服务器一次购买数量不能超过20台！");
		Boolean hasDisk = false;
		for (UpOrderCloudDiskBean disk : bean.getCloudDiskList()) {
			if (disk.getType().equals(SpVmDiskType.system) && disk.getDiskGB() != null && disk.getDiskGB() != 0) {
				hasDisk = true;
			}
		}
		AssertUtil.check(hasDisk, "缺失系统盘！");
		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_cloud_server, user.getId()) == 0, "您有未完成的云服务器申请！");
		
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_cloud_server);
		order.setName("[" + OrderType.new_cloud_server + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setAppSystem(new UpAppSystem(bean.getAppSystemId()));
		order.setApplyUser(applyUser);
		order.setPaymentType(bean.getPaymentType());
		order.setSpOrg(user.getOrg());
		order.setNumber(bean.getAmount());
		
		List<UpOrderCloudServer> entitys = new ArrayList<>();
		List<SpVapp> vappEntitys = new ArrayList<>();
		
		UpProductVmSet vmSet = null;
		UpProductDiskSet diskSet = null;
		UpProductBandwidthSet bandwidthSet = null;
		for (int i = 0; i < bean.getAmount();) {

			String name = bean.getName() + "-" + String.format("%03d", ++i);
			String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));

			UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
			entity.setRegion(order.getRegion());
			entity.setTaskSequence(i*100);
			entity.setOwner(user);
			entity.setName(name + "-" + random);
			entity.setAppSystem(new UpAppSystem(bean.getAppSystemId()));
			entity.setOrder(order);
			entity.setSpOrg(user.getOrg());
			entity.setSecurityGroup(new SpSecurityGroup(bean.getSecurityGroupId()));
			updatePrebuildImage(entity);

			if (vmSet == null) {
				vmSet = this.dao.load(UpProductVmSet.class, entity.getServerConfigId());
				AssertUtil.check(vmSet != null && vmSet.getEnabled() && vmSet.getServerType().equals(bean.getServerType()) && vmSet.getType().equals(ProductVmSetType.vm), "虚拟机配置信息异常！");
				order.setVmSet(vmSet);
				order.setCpuNum(vmSet.getCpuUnit());
				order.setMemoryNum(vmSet.getMemoryUnit());
				order.setCpuPrice(vmSet.getCpuPrice());
				order.setMemoryPrice(vmSet.getMemoryPrice());
			}
			entity.setCpu(vmSet.getCpuUnit());
			entity.setMemory(vmSet.getMemoryUnit());


			entity.setServerType(bean.getServerType());
			
			// 处理磁盘
			List<UpOrderCloudDisk> disks = new ArrayList<>();
			int j = 0;
			for (UpOrderCloudDiskBean diskBean : bean.getCloudDiskList()) {
				if (diskBean.getDiskGB() == null || diskBean.getDiskGB() == 0) {
					continue;
				}
				UpOrderCloudDisk disk = BeanCopyUtil.copy(diskBean, UpOrderCloudDisk.class);
				disk.setTaskSequence(i*100+j*10);
				disk.setChargeType(CloudStorageChargeType.hour);
				disk.setOrderCloudServer(entity);
				
				if(diskBean.getType().equals(SpVmDiskType.system)) {
					disk.setType(SpVmDiskType.system);
					disk.setDiskType(DiskType.hdd);
				}else {
					disk.setType(SpVmDiskType.mount);
//					AssertUtil.check(disk.getDiskType(), "请选择云盘类型！");
				}
//				disk.setType(diskBean.getType() == null ? SpVmDiskType.mount : diskBean.getType());
				disk.setName(name + "-" + random + "-" + j);
				disk.setDiskNumber(j);
				disk.setOwner(user);
				disk.setPaymentType(bean.getPaymentType());
				disk.setOrder(order);
				disk.setSpOrg(user.getOrg());
				disk.setRegion(order.getRegion());
				disks.add(disk);

				if (disk.getType().equals(SpVmDiskType.mount)) {
					if (diskSet == null) {
						List<UpProductDiskSet> diskSetList = dao.list(UpProductDiskSet.class, MapUtil.of("region", order.getRegion(), "name", "SAS", "status", RecordStatus.active));
						AssertUtil.check(diskSetList != null && !diskSetList.isEmpty(), "高IO云盘配置信息异常！");
						diskSet = diskSetList.get(0);
						AssertUtil.check(diskSet != null && diskSet.getEnabled(), "云盘配置信息异常！");
//						AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= disk.getDiskGB(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
//						AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= disk.getDiskGB(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
						order.setDiskSet(diskSet);
						order.setDiskNum(disk.getDiskGB());
//						order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(disk.getDiskGB())));
					}
//					order.setDiskNum((order.getDiskNum() == null ? 0 : order.getDiskNum()) + disk.getDiskGB());
				}else if(disk.getType().equals(SpVmDiskType.system)) {
					order.setSystemDiskNum(disk.getDiskGB());
				}
				j++;
			}

			entity.setCloudDiskList(disks);
			// 处理弹性公网IP同绑
			int k = 1;
			if (bean.getIsElasticIp() != null && bean.getIsElasticIp() && bean.getElasticIpList()[0] != null) {
				UpOrderElasticIp elasticIpEntity = BeanCopyUtil.copy(bean.getElasticIpList()[0], UpOrderElasticIp.class);
				elasticIpEntity.setTaskSequence(i*100+j*10+k);
				elasticIpEntity.setName(name + "-" + random);
				elasticIpEntity.setChargePeriod(ChargePeriod.hour);
				elasticIpEntity.setOrderCloudServer(entity);
				elasticIpEntity.setOwner(user);
				elasticIpEntity.setResourceType(ResourceType.cloudServer);
				elasticIpEntity.setPaymentType(bean.getPaymentType());
				elasticIpEntity.setOrder(order);
				elasticIpEntity.setSpOrg(user.getOrg());
				elasticIpEntity.setRegion(order.getRegion());
				List<UpOrderElasticIp> list = new ArrayList<>();
				list.add(elasticIpEntity);
				entity.setElasticIpList(list);
				if (bandwidthSet == null) {
					bandwidthSet = this.dao.load(UpProductBandwidthSet.class, elasticIpEntity.getBandwidthConfigId());
					AssertUtil.check(bandwidthSet != null && bandwidthSet.getEnabled(), "带宽配置信息异常！");
					order.setBandwidthSet(bandwidthSet);
					order.setBandwidthNum(elasticIpEntity.getBandwidth());
					order.setBandwidthPrice(bandwidthSet.getPrice().multiply(BigDecimal.valueOf(elasticIpEntity.getBandwidth())));
				}
//				order.setBandwidthNum((order.getBandwidthNum() == null ? 0 : order.getBandwidthNum()) + elasticIpEntity.getBandwidth());
			} else {
				bean.setElasticIpList(null);
			}
			SpVapp vapp = this.addVapp(entity);
			vapp.setOrder(order);
			vappEntitys.add(vapp);
			entity.setVm(vapp.getVmList().get(0));
			entitys.add(entity);
		}

//		order.setCpuPrice(vmSet.getCpuPrice().multiply(BigDecimal.valueOf(order.getCpuNum() / vmSet.getCpuUnit())));
//		order.setMemoryPrice(vmSet.getMemoryPrice().multiply(BigDecimal.valueOf(order.getMemoryNum() / vmSet.getMemoryUnit())));
		this.dao.insert(order);
		this.dao.insert(vappEntitys);
		this.dao.insert(entitys);
		for(SpVapp vapp : vappEntitys) {
			for(UpOrderCloudServer orderServer : entitys) {
				if(vapp.getVmList().get(0).getId().equals(orderServer.getVm().getId())) {
					vapp.setOrderCloudServer(orderServer);
					break;
				}
			}
		}
		this.dao.update(vappEntitys, "orderCloudServer");
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        task.setRegion(ThreadCache.getRegion());
        dao.insert(task);
//        orderMQService.createOrderMQ(order, entitys);
        
        quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}

	private void updatePrebuildImage(UpOrderCloudServer entity) {
//		if (SpRegion.isEdgeSite(entity.getRegion())) {
		if (spRegionService.isEdgeSite(entity.getRegion())) {
			logger.info("region:"+entity.getRegion()+" updatePrebuildImage from:"+entity.getImageId());
			SpVappTemplate template = this.dao.load(SpVappTemplate.class, entity.getImageId());
			String templateName = template.getName();
			//String catalogName = template.getCatalog().getName();
			//if ("iaas".equals(catalogName) || "redis".equals(catalogName) || "mysql".equals(catalogName) || "mysql_pxc".equals(catalogName)) {
				//String prebuildCatalogName = catalogName + "-" + entity.getRegion().name();
				String prebuildCatalogName = entity.getRegion().name();
				logger.info("prebuild catalog name:"+prebuildCatalogName);
//				List<Integer> templateIds = queryDao.querySql("select tplt.id from sp_vapp_template tplt, sp_catalog cat where tplt.catalog_id=cat.id " +
//								" and tplt.status='active' and cat.name=:catName and tplt.name=:tpltName",
//						MapUtil.of("catName", prebuildCatalogName, "tpltName", templateName));
			List<Integer> templateIds = queryDao.querySql("select tplt.id from sp_vapp_template tplt where  " +
							"  tplt.status='active' and tplt.name=:tpltName",
					MapUtil.of( "tpltName", templateName));

				if (templateIds != null && templateIds.size()>0) {
					logger.info("found prebuild template id:"+templateIds.get(0));
					entity.setImageId(templateIds.get(0));
				}
//				List<SpCatalog> prebuildCatalogs = dao.list(SpCatalog.class, MapUtil.of("name", prebuildCatalogName, "status", RecordStatus.active));
//				if (prebuildCatalogs == null || prebuildCatalogs.size() == 0) {
//					return ;
//				}
//				SpCatalog prebuildCatalog = prebuildCatalogs.get(0);
//				logger.info("prebuild catalog:"+prebuildCatalog);
//				List<SpVappTemplate> prebuildTemplates = dao.list(SpVappTemplate.class, MapUtil.of("name", templateName, "status", RecordStatus.active, "catalog", prebuildCatalog));
//				if(prebuildTemplates != null && prebuildTemplates.size()>0) {
//					SpVappTemplate prebuildTemplate = prebuildTemplates.get(0);
//					logger.info("prebuildTemplate:"+prebuildTemplate.getId());
//					entity.setImageId(prebuildTemplate.getId());
//				}
			//}
		}
	}

	@Override
	public Integer update(UpOrderCloudServerBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项！");
		UpOrderQuotaDetail quotaDetail = dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());
		AssertUtil.check(quotaDetail.getQuota().getOwner().getId().equals(ThreadCache.getUserId()) && quotaDetail.getSpOrg().getId().equals(ThreadCache.getOrgId()),"订单项无操作权限");
		UpProductVmSet vmSet = null;
		if(quotaDetail.getIsCustom() == null || !quotaDetail.getIsCustom()){
			AssertUtil.check(bean.getServerConfigId(), "请选择配置！");
			vmSet = this.dao.load(UpProductVmSet.class, bean.getServerConfigId());
			AssertUtil.check(vmSet != null && vmSet.getEnabled() && vmSet.getProductCode().equals(quotaDetail.getProductCode()), "产品编码异常！");
		}
		AssertUtil.check(bean.getVmId(), "请选择变更虚机！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.cloud_server_update, user.getId()) == 0, "您有未完成的云服务器变更单！");
		SpVm vm = dao.load(SpVm.class, bean.getVmId());
		AssertUtil.check(quotaDetail.getId().equals(vm.getUpdateOrderQuotaDetail().getId()),"云服务器变更操作异常！");
		AssertUtil.check(vm.getSpOrg().getId().equals(ThreadCache.getOrgId()),"云服务器无操作权限！");
		
		UpOrder order = new UpOrder();
		order.setRegion(vm.getRegion());
		order.setType(OrderType.cloud_server_update);
		order.setName("[" + OrderType.cloud_server_update + "]" + vm.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		order.setQuota(quotaDetail.getQuota());
		order.setQuotaDetail(quotaDetail);
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			order.setCpuNum(quotaDetail.getCpu());
			order.setMemoryNum(quotaDetail.getMemoryG());
		}else{
			order.setVmSet(vmSet);
			order.setCpuNum(vmSet.getCpuUnit());
			order.setMemoryNum(vmSet.getMemoryUnit());
			order.setCpuPrice(vmSet.getCpuPrice());
			order.setMemoryPrice(vmSet.getMemoryPrice());
		}

		UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
		entity.setTaskSequence(100);
		entity.setOwner(user);
		entity.setName("update-" + vm.getName() + "-" + quotaDetail.getSubCode());
		entity.setOrder(order);
		entity.setSpOrg(user.getOrg());
		entity.setCpu(order.getCpuNum());
		entity.setMemory(order.getMemoryNum());
		entity.setServerType(bean.getServerType());
		entity.setVm(vm);
		entity.setUpdateVapp(vm.getSpVapp());
		entity.setRegion(order.getRegion());

		this.dao.insert(order);
		this.dao.insert(entity);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        task.setRegion(order.getRegion());
        dao.insert(task);
        quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}

	private SpVapp addVapp(UpOrderCloudServer cloudServer) {
	        SpVapp spVapp = new SpVapp();
	        spVapp.setName(cloudServer.getName());
	        //spVapp.setOrderCloudServer(cloudServer);
	        spVapp.setOwner(cloudServer.getOwner());
	        spVapp.setSpOrg(cloudServer.getSpOrg());
	        spVapp.setDeployStatus(SpDeployStatus.INIT);
	        SpVappTemplate template = this.dao.load(SpVappTemplate.class, cloudServer.getImageId());
	        spVapp.setVappTemplate(template);
	
	        List<SpVm> vmList = new ArrayList<SpVm>();
	        SpVm spVm = new SpVm();
	        spVm.setName(cloudServer.getName());
	        spVm.setSpOrg(cloudServer.getSpOrg());
	        spVm.setCpuNum(cloudServer.getCpu());
	        spVm.setMemoryGB(cloudServer.getMemory());
	        spVm.setVappTemplate(template);
	//        SpVappTemplateMachine templateMachine = this.dao.load(SpVappTemplateMachine.class,
	//                cloudServer.getTemplateMachine().getId());
	//        spVm.setTemplateMachine(templateMachine);
	        spVm.setPowerStatus(SpVmPowerStatus.power_on);
	        spVm.setOwner(cloudServer.getOwner());
	        spVm.setOrder(cloudServer.getOrder());
	        spVm.setDeployStatus(SpDeployStatus.INIT);
	        //spVm.setVmType(VcdOperationCommon.getVmType(cloudServer.getOrder()));
	        spVm.setVmType(OrderUtil.getVmType(cloudServer.getOrder()!=null?cloudServer.getOrder().getType():null));
	        spVm.setHostName(cloudServer.getHostname());
	        spVm.setRegion(ThreadCache.getRegion());
	        List<SpVmDisk> spvmDiskList = new ArrayList<SpVmDisk>();
	        int number = 0;
	        int diskGB = 0;
	
	        for (UpOrderCloudDisk disk : cloudServer.getCloudDiskList()) {
	            SpVmDisk spvmDisk = new SpVmDisk();
	            spvmDisk.setSpOrg(disk.getSpOrg());
	            spvmDisk.setName(disk.getName());
	            spvmDisk.setDiskGB(disk.getDiskGB());
	            spvmDisk.setDiskNumber(number++);
	            spvmDisk.setDiskLabel(disk.getName());
	            spvmDisk.setDiskPath("");
	            spvmDisk.setType(disk.getType());
	            spvmDisk.setOrder(disk.getOrder());
	            spvmDisk.setRegion(ThreadCache.getRegion());
	            //String spUuid = "urn:vcloud:disk:" + MD5Util.format(MD5Util.encode(disk.getName()));
	            //String spUuid = spVm.getSpUuid() + VcdCloudService.SEPERATOR + spvmDisk.getDiskNumber();
	            //spvmDisk.setSpUuid(spUuid);
	            //spvmDisk.setThinProvisioned(thinProvisioned);
	            spvmDiskList.add(spvmDisk);
	            diskGB += spvmDisk.getDiskGB();
	        }
	        spVm.setDiskList(spvmDiskList);
	        spVm.setDiskGB(diskGB);
	        
	        vmList.add(spVm);
	        spVapp.setVmType(spVm.getVmType());
	        spVapp.setVmList(vmList);
	        spVapp.setRegion(ThreadCache.getRegion());
	        return spVapp;
	    }

}
