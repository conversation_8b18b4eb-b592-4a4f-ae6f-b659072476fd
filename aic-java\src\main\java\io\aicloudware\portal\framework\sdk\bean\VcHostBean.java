package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vCenter主机")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcHostBean extends VcRecordBean {

    @ApiModelProperty(value = "数据中心ID")
    private Integer dataCenterId;

    @ApiModelProperty(value = "数据中心名称")
    private String dataCenterName;

    @ApiModelProperty(value = "集群ID")
    private Integer clusterId;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "CPU（核）")
    private Integer cpuNum;

    @ApiModelProperty(value = "CPU主频")
    private Long cpuCoreHz;

    @ApiModelProperty(value = "CPU总量")
    private Long cpuTotalHz;

    @ApiModelProperty(value = "CPU使用")
    private Long cpuUsageHz;

    @ApiModelProperty(value = "CPU空闲")
    private Long cpuFreeHz;

    @ApiModelProperty(value = "内存总量")
    private Long memoryTotalB;

    @ApiModelProperty(value = "内存使用")
    private Long memoryUsageB;

    @ApiModelProperty(value = "内存剩余")
    private Long memoryFreeB;

    @ApiModelProperty(value = "物理存储列表")
    private VcDataStoreBean[] dataStoreList;

    public Integer getDataCenterId() {
        return dataCenterId;
    }

    public void setDataCenterId(Integer dataCenterId) {
        this.dataCenterId = dataCenterId;
    }

    public String getDataCenterName() {
        return dataCenterName;
    }

    public void setDataCenterName(String dataCenterName) {
        this.dataCenterName = dataCenterName;
    }

    public Integer getClusterId() {
        return clusterId;
    }

    public void setClusterId(Integer clusterId) {
        this.clusterId = clusterId;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Long getCpuCoreHz() {
        return cpuCoreHz;
    }

    public void setCpuCoreHz(Long cpuCoreHz) {
        this.cpuCoreHz = cpuCoreHz;
    }

    public Long getCpuTotalHz() {
        return cpuTotalHz;
    }

    public void setCpuTotalHz(Long cpuTotalHz) {
        this.cpuTotalHz = cpuTotalHz;
    }

    public Long getCpuUsageHz() {
        return cpuUsageHz;
    }

    public void setCpuUsageHz(Long cpuUsageHz) {
        this.cpuUsageHz = cpuUsageHz;
    }

    public Long getCpuFreeHz() {
        return cpuFreeHz;
    }

    public void setCpuFreeHz(Long cpuFreeHz) {
        this.cpuFreeHz = cpuFreeHz;
    }

    public Long getMemoryTotalB() {
        return memoryTotalB;
    }

    public void setMemoryTotalB(Long memoryTotalB) {
        this.memoryTotalB = memoryTotalB;
    }

    public Long getMemoryUsageB() {
        return memoryUsageB;
    }

    public void setMemoryUsageB(Long memoryUsageB) {
        this.memoryUsageB = memoryUsageB;
    }

    public Long getMemoryFreeB() {
        return memoryFreeB;
    }

    public void setMemoryFreeB(Long memoryFreeB) {
        this.memoryFreeB = memoryFreeB;
    }

    public VcDataStoreBean[] getDataStoreList() {
        return dataStoreList;
    }

    public void setDataStoreList(VcDataStoreBean[] dataStoreList) {
        this.dataStoreList = dataStoreList;
    }
}
