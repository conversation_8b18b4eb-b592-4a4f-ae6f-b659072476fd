package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "虚机资源使用情况")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpVmUsageBean.class})
public class UpVmUsageBean extends RecordBean {

    @ApiModelProperty(value = "业务租户ID", position = 40)
    private Integer upTenantId;

    @ApiModelProperty(value = "业务租户名称", position = 50)
    private String upTenantName;

    @ApiModelProperty(value = "部门ID")
    private Integer groupId;

    @ApiModelProperty(value = "部门名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String groupName;

    @ApiModelProperty(value = "应用系统ID")
    private Integer appSystemId;

    @ApiModelProperty(value = "应用系统名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String appSystemName;

    @ApiModelProperty(value = "应用系统显示名称")
    private String appSystemDisplayName;

    @ApiModelProperty(value = "所有者ID")
    private Integer ownerId;

    @ApiModelProperty(value = "所有者名称")
    private String ownerName;

    @ApiModelProperty(value = "所有者显示名称")
    private String ownerDisplayName;

    @ApiModelProperty(value = "日期")
    private Integer date;

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "CPU已使用MHz")
    private Integer cpuUsageMHz = 0;
    @ApiModelProperty(value = "CPU未使用MHz")
    private Integer cpuFreeMHz = 0;

    @ApiModelProperty(value = "内存已使用GB")
    private Integer memoryUsageM = 0;
    @ApiModelProperty(value = "内存已使用GB")
    private Integer memoryFreeM = 0;

    @ApiModelProperty(value = "磁盘已使用GB")
    private Integer diskUsageG = 0;
    @ApiModelProperty(value = "磁盘未使用GB")
    private Integer diskFreeG = 0;

    public Integer getUpTenantId() {
        return upTenantId;
    }

    public void setUpTenantId(Integer upTenantId) {
        this.upTenantId = upTenantId;
    }

    public String getUpTenantName() {
        return upTenantName;
    }

    public void setUpTenantName(String upTenantName) {
        this.upTenantName = upTenantName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getAppSystemId() {
        return appSystemId;
    }

    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }

    public String getAppSystemName() {
        return appSystemName;
    }

    public void setAppSystemName(String appSystemName) {
        this.appSystemName = appSystemName;
    }

    public String getAppSystemDisplayName() {
        return appSystemDisplayName;
    }

    public void setAppSystemDisplayName(String appSystemDisplayName) {
        this.appSystemDisplayName = appSystemDisplayName;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public Integer getCpuUsageMHz() {
        return cpuUsageMHz;
    }

    public void setCpuUsageMHz(Integer cpuUsageMHz) {
        this.cpuUsageMHz = cpuUsageMHz;
    }

    public Integer getCpuFreeMHz() {
        return cpuFreeMHz;
    }

    public void setCpuFreeMHz(Integer cpuFreeMHz) {
        this.cpuFreeMHz = cpuFreeMHz;
    }

    public Integer getMemoryUsageM() {
        return memoryUsageM;
    }

    public void setMemoryUsageM(Integer memoryUsageM) {
        this.memoryUsageM = memoryUsageM;
    }

    public Integer getMemoryFreeM() {
        return memoryFreeM;
    }

    public void setMemoryFreeM(Integer memoryFreeM) {
        this.memoryFreeM = memoryFreeM;
    }

    public Integer getDiskUsageG() {
        return diskUsageG;
    }

    public void setDiskUsageG(Integer diskUsageG) {
        this.diskUsageG = diskUsageG;
    }

    public Integer getDiskFreeG() {
        return diskFreeG;
    }

    public void setDiskFreeG(Integer diskFreeG) {
        this.diskFreeG = diskFreeG;
    }

    public String getOwnerDisplayName() {
        return ownerDisplayName;
    }

    public void setOwnerDisplayName(String ownerDisplayName) {
        this.ownerDisplayName = ownerDisplayName;
    }

}
