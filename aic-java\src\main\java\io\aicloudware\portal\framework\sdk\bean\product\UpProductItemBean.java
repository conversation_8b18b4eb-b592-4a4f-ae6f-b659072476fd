package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.ProductEnums.ProductItemType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "产品配件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductItemBean extends RecordBean {

	@ApiModelProperty(value = "配件类型")
	private ProductItemType type;

	@ApiModelProperty(value = "子分类")
	private String subType;
	
	@ApiModelProperty(value = "单位数")
	private Integer unit;

	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	@ApiModelProperty(value = "最大值")
	private Integer maxValue;
	
	@ApiModelProperty(value = "最小值")
	private Integer minValue;
	
	@ApiModelProperty(value = "步长")
	private Integer step;
	
	@ApiModelProperty(value = "序列")
	private Integer seq;

	@ApiModelProperty(value = "是否激活")
	private Boolean enabled;
	
	@ApiModelProperty(value = "是否级联更新产品")
	private Boolean isConnectProduct;

	public ProductItemType getType() {
		return type;
	}

	public void setType(ProductItemType type) {
		this.type = type;
	}

	public String getSubType() {
		return subType;
	}

	public void setSubType(String subType) {
		this.subType = subType;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Boolean getIsConnectProduct() {
		return isConnectProduct;
	}

	public void setIsConnectProduct(Boolean isConnectProduct) {
		this.isConnectProduct = isConnectProduct;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}
	
}
