package io.aicloudware.portal.api_rest.framework.enums;

public enum RestMessageType {
	message("反馈消息","/api/rest/cloudVmware/v1/action/cloudnocFeedBack"),
	montor("告警","/api/rest/cloudVmware/v1/action/sendPmAlarm"),
	websiteApprove("备案","/api/rest/cloudVmware/v1/action/queryRecord"),
	;

    private final String title;
    private final String uri;

    private RestMessageType(String title, String uri) {
        this.title = title;
        this.uri = uri;
    }

    public String getTitle() {
        return title;
    }

	public String getUri() {
		return uri;
	}
}
