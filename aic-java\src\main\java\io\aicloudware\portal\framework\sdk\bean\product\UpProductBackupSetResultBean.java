package io.aicloudware.portal.framework.sdk.bean.product;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.ResultListBean;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "备份套餐设置结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductBackupSetResultBean extends ResultListBean<UpProductBackupSetBean>  {

}
