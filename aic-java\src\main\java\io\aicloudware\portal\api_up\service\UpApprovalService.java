package io.aicloudware.portal.api_up.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.aicloudware.portal.api_up.entity.UpApplication;
import io.aicloudware.portal.api_up.entity.UpApprovalHistory;
import io.aicloudware.portal.api_up.entity.UpApprovalProcess;
import io.aicloudware.portal.api_up.entity.UpApprovalProcessNode;
import io.aicloudware.portal.api_up.entity.UpApprovalScene;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.common.SdkConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistorySearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserListBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpApprovalStatus;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.Utility;

@Service
public class UpApprovalService extends BaseService implements IUpApprovalService {

    @Autowired
    private IUpApplicationService upApplicationService;

    @Autowired
    private IUpRelationService upRelationService;

    @Autowired
    private IUpOperationLogService upOperationLogService;

    @Override
    public UpUserListBean toApprove(UpApprovalHistoryBean bean) {

        // 查询场景所在的节点
        UpApprovalProcessNode currentNode = dao.load(UpApprovalProcessNode.class, bean.getNodeId());
        // 查询场景节点所在的审批流程
        UpApprovalProcess approvalProcess = dao.load(UpApprovalProcess.class, bean.getProcessId());
        // 查询申请单
        UpApplication application = dao.load(UpApplication.class, bean.getApplicationId());

        List<UpUserBean> approverList = new ArrayList<>();

        // 在流程的所有节点中找到下一节点
        UpApprovalProcessNode nextNode = getNextNode(approvalProcess, currentNode);

        // 存在下一节点
        if (null != nextNode) {
            approverList = getApprovers(nextNode, application);
        }

        UpUserListBean upUserListBean = new UpUserListBean();
        upUserListBean.setDataList(approverList.toArray(new UpUserBean[approverList.size()]));

        return upUserListBean;
    }

    @Override
    public List<UpUserBean> getApprovers(UpApprovalProcessNode node, UpApplication application) {
        if (node == null) {
            return Collections.emptyList();
        }
        if (node.getUser() != null) {
            UpUser approver = dao.load(UpUser.class, node.getUser().getId());
            return Collections.singletonList(BeanCopyUtil.copy2Bean(approver, UpUserBean.class));
        }
        AssertUtil.check(node.getRole() != null, "流程节点的审批人、部门、角色不能同时为空。" +
                "（流程名称：" + node.getApprovalProcess().getName() + "，节点名称：" + node.getName() + "）");
        Set<Integer> userIdSet = null;
        if (node.getRole() != null) {
            List<UpRelationBean> roleUserList = upRelationService.getRoleRelationList(node.getRole().getId(), UpRelationType.user_role);
            if (userIdSet == null) {
                userIdSet = new TreeSet<>(ListUtil.toList(roleUserList, UpRelationBean::getUserId));
            } else {
                userIdSet.retainAll(ListUtil.toList(roleUserList, UpRelationBean::getUserId));
            }
        }
        Collection<UpUser> userCollection = dao.map(UpUser.class, userIdSet).values();
        AssertUtil.check(Utility.isNotEmpty(userCollection), "流程节点的找不到对应的审批人。" +
                "（流程名称：" + node.getApprovalProcess().getName() + "，节点名称：" + node.getName() + "）");
        return Arrays.asList(BeanCopyUtil.copy2BeanList(userCollection, UpUserBean.class));
    }

    @Override
    public void doApprove(UpApprovalHistoryBean bean) {
//        // 注意：当前审批单只能单个提交处理，若批量处理审批单则此处要实时查询审批单的状态
//        AssertUtil.check(UpApprovalStatus.pending_approve.equals(bean.getApprovalStatus()),
//                "审批单状态不是 pending_approve，审批单名称：" + bean.getName());
//
//        // 查询场景所在的节点
//        UpApprovalProcessNode currentNode = dao.load(UpApprovalProcessNode.class, bean.getNodeId());
//        // 查询场景节点所在的审批流程
//        UpApprovalProcess approvalProcess = dao.load(UpApprovalProcess.class, bean.getProcessId());
//        // 在流程的所有节点中找到下一节点
//        UpApprovalProcessNode nextNode = getNextNode(approvalProcess, currentNode);
//
//        // 同意当前节点审批单
//        updateCurrentNodeApproval(bean, Boolean.TRUE);
//
//        // 最后一个审批节点
//        if (null == nextNode) {
//            UpApplication application = dao.load(UpApplication.class, bean.getApplicationId());
//            // 更新申请单
//            updateApplicationStatus(bean.getApplicationId(), UpApplicationStatus.pending_deploy);
//            // 记录日志
//            upOperationLogService.saveOperationLog(UpOperationType.application_approve.getTitle(), ThreadCache.getUser(), UpOperationType.application_approve, UpApprovalHistory.class, bean.getId(), bean.getName(), new UpApplication(bean.getApplicationId()));
//            upApplicationService.handleApplicationProcess(application);
//        }
//        // 流程未完
//        else {
//            // 发待办审批单
//            sendNextNodeApproval(bean, nextNode);
//            // 更新申请单
//            updateApplicationStatus(bean.getApplicationId(), UpApplicationStatus.approving);
//            // 记录日志
//            upOperationLogService.saveOperationLog(UpOperationType.application_approve.getTitle(), ThreadCache.getUser(), UpOperationType.application_approve, UpApprovalHistory.class, bean.getId(), bean.getName(), new UpApplication(bean.getApplicationId()));
//        }
    }

    @Override
    public void doReject(UpApprovalHistoryBean bean) {
//        // 拒绝当前节点审批单
//        updateCurrentNodeApproval(bean, Boolean.FALSE);
//        // 更新申请单
//        updateApplicationStatus(bean.getApplicationId(), UpApplicationStatus.approve_reject);
//        // 记录日志
//        upOperationLogService.saveOperationLog(UpOperationType.application_reject.getTitle(), ThreadCache.getUser(), UpOperationType.application_reject, UpApprovalHistory.class, bean.getId(), bean.getName(), new UpApplication(bean.getApplicationId()));
    }

    private void updateCurrentNodeApproval(UpApprovalHistoryBean bean, Boolean isApprove) {
        UpApprovalHistorySearchBean searchBean = new UpApprovalHistorySearchBean();
        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);

        UpApprovalHistory entity = new UpApprovalHistory();

        UpApplication application = dao.load(UpApplication.class, bean.getApplicationId());
        UpApprovalProcessNode node = new UpApprovalProcessNode();
        node.setId(bean.getNodeId());
        entity.setApplication(application);
        entity.setNode(node);
        entity.setApprovalStatus(UpApprovalStatus.pending_approve);

        List<UpApprovalHistory> ahList = dao.query(searchBean, entity);

        for (UpApprovalHistory approvalHistory : ahList) {
            if (Utility.equals(ThreadCache.getUserId(), approvalHistory.getPendingApprover().getId())) {
                approvalHistory.setApprovalStatus(
                        Boolean.TRUE.equals(isApprove) ? UpApprovalStatus.self_approve : UpApprovalStatus.self_reject);
            } else {
                approvalHistory.setApprovalStatus(
                        Boolean.TRUE.equals(isApprove) ? UpApprovalStatus.other_approve : UpApprovalStatus.other_reject);
            }
            approvalHistory.setActualApprover(ThreadCache.getUser());
            approvalHistory.setOpinion(bean.getOpinion());
        }

        dao.update(ahList, "approvalStatus", "actualApprover", "opinion");
    }

    private void updateApplicationStatus(Integer id, UpApplicationStatus status) {
        UpApplication application = new UpApplication();
        application.setId(id);
        application.setApplicationStatus(status);
        dao.update(application, "applicationStatus");
    }

    private void sendNextNodeApproval(UpApprovalHistoryBean bean, UpApprovalProcessNode nextNode) {

        AssertUtil.check(bean.getNextNodeApprovers(), "未选择下一节点的审批人，审批单名称：" + bean.getName());

        for (Integer userId : bean.getNextNodeApprovers()) {
            UpApprovalHistory approvalHistory = new UpApprovalHistory();

            UpApplication application = dao.load(UpApplication.class, bean.getApplicationId());
            approvalHistory.setName(bean.getName());
            approvalHistory.setApplication(application);
            approvalHistory.setProcessId(bean.getProcessId());
            approvalHistory.setNode(nextNode);
            approvalHistory.setPreApprover(ThreadCache.getUser());
            UpUser pendingApprover = new UpUser();
            pendingApprover.setId(userId);
            approvalHistory.setPendingApprover(pendingApprover);
            approvalHistory.setApprovalStatus(UpApprovalStatus.pending_approve);

            dao.insert(approvalHistory);

            UpTask task = new UpTask();
            task.setType(UpTaskType.send_mail_pending_approve);
            task.setTargetId(approvalHistory.getId());
            task.setName(bean.getName());
            task.setTaskStatus(UpTaskStatus.start);
            dao.insert(task);
        }
    }

    private UpApprovalProcessNode getNextNode(UpApprovalProcess approvalProcess, UpApprovalProcessNode currentNode) {

        UpApprovalProcessNode nextNode = null;

        if (null != approvalProcess.getApprovalProcessNodeList()) {
            for (UpApprovalProcessNode node : approvalProcess.getApprovalProcessNodeList()) {
                if (node.getSeq().compareTo(currentNode.getSeq()) > 0) {
                    if (null == nextNode) {
                        nextNode = node;
                    } else if (node.getSeq().compareTo(nextNode.getSeq()) < 0) {
                        nextNode = node;
                    }
                }
            }
        }

        return nextNode;
    }

    @Override
    public UpApprovalHistoryResultBean getApplicationDetail(Integer applicationId) {
        UpApprovalHistorySearchBean ahSearchBean = new UpApprovalHistorySearchBean();
        UpApprovalStatus[] approvalStatusList = {UpApprovalStatus.pending_approve, UpApprovalStatus.self_approve, UpApprovalStatus.self_reject};
        ahSearchBean.setApprovalStatusList(approvalStatusList);
        ahSearchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);

        UpApprovalHistory ahEntity = new UpApprovalHistory();
        UpApplication application = dao.load(UpApplication.class, applicationId);
        ahEntity.setApplication(application);

        UpApprovalHistoryResultBean resultBean = new UpApprovalHistoryResultBean();
        List<UpApprovalHistory> ahEntityList = dao.query(ahSearchBean, ahEntity);
        resultBean.setDataList(BeanCopyUtil.copy2BeanList(ahEntityList, UpApprovalHistoryBean.class));
        return resultBean;
    }

    @Override
    public void checkIsUsed(UpApprovalProcessResultBean rsltBean) {
        List<Integer> processIdList = new ArrayList<Integer>();
        List<Integer> ahProcessIdList = new ArrayList<Integer>();

        if (null != rsltBean.getDataList()) {

            for (UpApprovalProcessBean processBean : rsltBean.getDataList()) {
                processIdList.add(processBean.getId());
            }

            UpApprovalHistorySearchBean ahSearchBean = new UpApprovalHistorySearchBean();
            ahSearchBean.setProcessIdList(processIdList.toArray(new Integer[processIdList.size()]));
            ahSearchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);

            UpApprovalHistory ahEntity = new UpApprovalHistory();
            ahEntity.setApprovalStatus(UpApprovalStatus.pending_approve);

            List<UpApprovalHistory> ahList = dao.query(ahSearchBean, ahEntity);
            for (UpApprovalHistory upApprovalHistory : ahList) {
                ahProcessIdList.add(upApprovalHistory.getProcessId());
            }

            for (UpApprovalProcessBean processBean : rsltBean.getDataList()) {
                if (ahProcessIdList.contains(processBean.getId())) {
                    processBean.setIsUsed(Boolean.TRUE);
                } else {
                    processBean.setIsUsed(Boolean.FALSE);
                }
            }
        }
    }

    public void checkIsUsed(UpApprovalProcessBean apBean) {
        List<Integer> processIdList = new ArrayList<Integer>();
        processIdList.add(apBean.getId());

        UpApprovalHistorySearchBean ahSearchBean = new UpApprovalHistorySearchBean();
        ahSearchBean.setProcessIdList(processIdList.toArray(new Integer[processIdList.size()]));
        ahSearchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);

        UpApprovalHistory ahEntity = new UpApprovalHistory();
        ahEntity.setApprovalStatus(UpApprovalStatus.pending_approve);

        List<UpApprovalHistory> ahList = dao.query(ahSearchBean, ahEntity);

        if (null != ahList && ahList.size() > 0) {
            apBean.setIsUsed(Boolean.TRUE);
        } else {
            apBean.setIsUsed(Boolean.FALSE);
        }
    }

    @Override
    public void initDefaultDataForTenant() {
        UpApprovalProcess apEntity = new UpApprovalProcess();
        apEntity.setName("无审批流程");
        dao.insert(apEntity);

        UpApprovalScene asEntity = new UpApprovalScene();
        asEntity.setName("无审批场景");
        asEntity.setApprovalProcess(apEntity);
        asEntity.setPriority(1);
        dao.insert(asEntity);
    }

}
