package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlanItem;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlanRegionRelation;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanBean;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanItemBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpServerConnectionType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemSubType.SSD;
import static io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemType.*;

@Service
@Transactional

public class UpServicePlanService extends BaseService implements IUpServicePlanService {

    private static final SimpleDateFormat DateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void add(UpServicePlanBean bean) {
        UpServicePlan entity = BeanCopyUtil.copy(bean, UpServicePlan.class);
        UUID uuid = UUID.randomUUID();

        if(entity.getServicePlanItems() != null && entity.getServicePlanItems().size() > 0){
            entity.getServicePlanItems().forEach(item -> item.setName(item.getServicePlanItemType().name()));
        }

        if(entity.getServicePlanRegionRelations() != null && entity.getServicePlanRegionRelations().size() > 0){
            entity.getServicePlanRegionRelations().forEach(item -> item.setName(entity.getName() + "-" + item.getRegion().getId()));
        }
        entity.setServicePlanCode(uuid.toString());
        dao.insert(entity);
    }

    @Override
    public void update(UpServicePlanBean bean) {
        // 加载实体并校验是否存在
        UpServicePlan entity = dao.load(UpServicePlan.class, bean.getId());
        if (entity == null) {
            throw new IllegalArgumentException("服务计划不存在，ID: " + bean.getId());
        }

        UpServicePlanItemBean[] itemBeans = bean.getServicePlanItems();
        if (itemBeans == null) {
            itemBeans = new UpServicePlanItemBean[0]; // 避免空指针
        }

        List<UpOrder> orders = dao.list(UpOrder.class, "servicePlan.id", entity.getId());

        if (orders.isEmpty()) {
            // 服务未使用过，直接更新服务
            updateExistingServicePlan(bean, entity, itemBeans);
        } else {
            // 服务已使用过，归档旧服务，创建新服务
            archiveAndCreateNewServicePlan(bean, entity, itemBeans);
        }
    }

    private void updateExistingServicePlan(UpServicePlanBean bean, UpServicePlan entity, UpServicePlanItemBean[] itemBeans) {
        List<UpServicePlanItem> itemList = entity.getServicePlanItems();
        List<Integer> itemIdList = itemList.stream().map(UpServicePlanItem::getId).collect(Collectors.toList());
        dao.destroy(UpServicePlanItem.class, itemIdList);

        // 更新基础信息
        entity.setPrice(bean.getPrice());
        entity.setServicePlanType(bean.getServicePlanType());
        entity.setAutoEffectiveDate(bean.getAutoEffectiveDate());
        entity.setAutoExpiryDate(bean.getAutoExpiryDate());
        entity.setRemark(bean.getRemark());
        entity.setName(bean.getName());

        // 删除区域关系
        if (Utility.isNotEmpty(entity.getServicePlanRegionRelations())) {
            dao.destroy(UpServicePlanRegionRelation.class, entity.getServicePlanRegionRelations().stream()
                    .map(UpServicePlanRegionRelation::getId)
                    .collect(Collectors.toList()));
        }

        // 插入新的区域关系
        if (Utility.isNotEmpty(bean.getServicePlanRegionRelations())) {
            List<UpServicePlanRegionRelation> relations = Arrays.stream(bean.getServicePlanRegionRelations())
                    .map(b -> BeanCopyUtil.copy(b, UpServicePlanRegionRelation.class))
                    .collect(Collectors.toList());
            dao.insert(relations);
            entity.setServicePlanRegionRelations(relations);
        }

        dao.update(entity);

        // 重建配件项
        List<UpServicePlanItem> itemEntities = Arrays.stream(itemBeans)
                .map(itemBean -> {
                    UpServicePlanItem itemEntity = BeanCopyUtil.copy(itemBean, UpServicePlanItem.class);
                    itemEntity.setServicePlan(entity);
                    itemEntity.setId(null);
                    return itemEntity;
                })
                .collect(Collectors.toList());

        dao.insert(itemEntities);
    }

    private void archiveAndCreateNewServicePlan (UpServicePlanBean bean, UpServicePlan entity, UpServicePlanItemBean[] itemBeans) {
        entity.setStatus(RecordStatus.inactive);
        dao.update(entity);

        UpServicePlan newEntity = BeanCopyUtil.copy(bean, UpServicePlan.class);
        newEntity.setSourceServicePlan(entity);
        newEntity.setServicePlanCode(entity.getServicePlanCode());
//        newEntity.setServiceDiscount(entity.getServiceDiscount());
        dao.insert(newEntity);

        List<UpServicePlanItem> itemEntities = Arrays.stream(itemBeans)
                .map(itemBean -> {
                    UpServicePlanItem itemEntity = BeanCopyUtil.copy(itemBean, UpServicePlanItem.class);
                    itemEntity.setServicePlan(newEntity);
                    itemEntity.setId(null);
                    return itemEntity;
                })
                .collect(Collectors.toList());

        dao.insert(itemEntities);
    }

    @Override
    public void delete(Integer id) {
        UpServicePlan entity = dao.load(UpServicePlan.class, id);
        entity.setStatus(RecordStatus.inactive);
        dao.update(entity);
    }

    @Override
    public UpServicePlanBean[] listByType(UpServicePlanType type) {
        List<UpServicePlan> list = dao.list(UpServicePlan.class, MapUtil.of("servicePlanType", type, "status", RecordStatus.active));
        return list.stream().map(entity -> {
            UpServicePlanBean bean = BeanCopyUtil.copy(entity, UpServicePlanBean.class);
            for(UpServicePlanItemBean itemBean: bean.getServicePlanItems()){
                if(itemBean.getServicePlanItemType() != null){
                    itemBean.setServicePlanItemTypeTxt(itemBean.getServicePlanItemType().getTitle());
                }
                if(itemBean.getServicePlanItemSubType() != null){
                    itemBean.setServicePlanItemSubTypeTxt(itemBean.getServicePlanItemSubType().getTitle());
                }
            }
            return bean;
        }).toArray(UpServicePlanBean[]::new);
    }

    @Override
    public UpServicePlanBean[] list(UpServicePlanBean params) {

//        Map<String, Object> sqlParams = new HashMap<>();
//        sqlParams.put("status", RecordStatus.active);
//
//        if(params != null){
//            if(params.getServicePlanType() != null){
//                sqlParams.put("servicePlanType", params.getServicePlanType());
//            }
//            if(params.getServerConnectionId() != null){
//                sqlParams.put("serverConnection.id", params.getServerConnectionId());
//            }
//            if(params.getServerConnectionType() != null){
//                sqlParams.put("serverConnectionType", params.getServerConnectionType());
//            }
//        }
//
//        List<UpServicePlan> list = dao.list(UpServicePlan.class, "status", RecordStatus.active);
//        return new UpServicePlanBean[0];
        UpServicePlanItemBean item1 = new UpServicePlanItemBean();
        item1.setServicePlanItemType(CPU);
        item1.setAmount(8);

        UpServicePlanItemBean item2 = new UpServicePlanItemBean();
        item2.setServicePlanItemType(MEMORY_GB);
        item2.setAmount(16);

        UpServicePlanItemBean item3 = new UpServicePlanItemBean();
        item3.setServicePlanItemType(DISK_GB);
        item3.setServicePlanItemSubType(SSD);
        item3.setAmount(40);

        UpServicePlanBean bean = new UpServicePlanBean();
        bean.setName("Aliyun-8c16g-1111");
        bean.setServicePlanType(UpServicePlanType.ecs);
        bean.setServicePlanCode(UUID.randomUUID().toString());
        bean.setCloudConnectionUuid(UUID.randomUUID().toString());
        bean.setCloudConnectionType(SpServerConnectionType.aliyun);
        bean.setCloudConnectionName("AliyunDMZ");
        bean.setPrice(new BigDecimal(48));
        try {
            bean.setAutoEffectiveDate(DateTimeFormat.parse("2025-11-01 00:00:00"));
            bean.setAutoExpiryDate(DateTimeFormat.parse("2025-11-11 23:59:59"));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        bean.setRemark("阿里云双11活动产品");
        bean.setServicePlanItems(new UpServicePlanItemBean[]{item1, item2, item3});
        return new UpServicePlanBean[]{bean};
    }
}
