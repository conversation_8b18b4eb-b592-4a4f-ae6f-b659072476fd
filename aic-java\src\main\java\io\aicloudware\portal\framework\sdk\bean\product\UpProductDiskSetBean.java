package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "磁盘产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductDiskSetBean extends RecordBean {

	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "内存配件ID")
    private Integer diskProductItemId;

	@ApiModelProperty(value = "单位数")
	private Integer unit;
	
	@ApiModelProperty(value = "价格")
	private BigDecimal price;
	
	@ApiModelProperty(value = "最大值")
	private Integer maxValue;
	
	@ApiModelProperty(value = "最小值")
	private Integer minValue;
	
	@ApiModelProperty(value = "步长")
	private Integer step;
	
	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;
	
	@ApiModelProperty(value = "云盘类型")
	private DiskType diskType;
	
	@ApiModelProperty(value = "云盘类型")
	private ProductDiskSetType type;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getDiskProductItemId() {
		return diskProductItemId;
	}

	public void setDiskProductItemId(Integer diskProductItemId) {
		this.diskProductItemId = diskProductItemId;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}

	public DiskType getDiskType() {
		return diskType;
	}

	public void setDiskType(DiskType diskType) {
		this.diskType = diskType;
	}

	public ProductDiskSetType getType() {
		return type;
	}

	public void setType(ProductDiskSetType type) {
		this.type = type;
	}
	
}
