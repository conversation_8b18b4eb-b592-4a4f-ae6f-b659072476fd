package io.aicloudware.portal.framework.action;

import java.util.Map;

import io.aicloudware.portal.framework.remote.RemoteHost;

public class FinanceAction extends RemoteUpModuleAction {

	public FinanceAction(RemoteHost remoteHost) {
		super(remoteHost, "");
	}

	public String getOrder(Integer user_id, Map<String, String> params) {
		return getRemoteHost().stringJsonPost("/fee/account/recharge?user_id=" + user_id, params);
	}

	public String getOrderUri(Integer user_id, String pay_order_id) {
		return getRemoteHost().stringGet("/fee/account/recharge/qrcode/" + pay_order_id + "?user_id=" + user_id);
	}

	public String getOrderStatus(Integer user_id, String pay_order_id) {
		return getRemoteHost().stringGet("/fee/account/recharge/" + pay_order_id + "?user_id=" + user_id);
	}

	public String getBalance(Integer user_id) {
		return getRemoteHost().stringGet("/fee/account/balance?user_id=" + user_id);
	}

	public String getBills(Integer user_id, Integer page,Integer per_page) {
		String url = "/fee/account/consumption/bills/iaas?user_id=" + user_id;
		if(page!=null) {
			url += "&page=" + page;
		}
		if(per_page!=null) {
			url += "&per_page" + per_page;
		}
		
		return getRemoteHost().stringGet(url);
	}

}
