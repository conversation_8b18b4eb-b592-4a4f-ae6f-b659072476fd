package io.aicloudware.portal.framework.controller;


import io.aicloudware.portal.framework.dao.IDao;
import io.aicloudware.portal.framework.entity.ISpResourceEntity;
import io.aicloudware.portal.framework.service.IInvokeService;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller
public abstract class BaseSpInvokeController<E extends ISpResourceEntity<B>, B extends RecordBean, RL extends ResultListBean<B>>
        extends BaseUpController<E, B, RL> {

    @Autowired
    private IDao dao;

    protected abstract IInvokeService<E, ? extends SearchBean<B>, B> getInvokeService();

    @Override
    protected B[] doInsert(List<E> entityList) {
        for (E entity : entityList) {
            getInvokeService().doInvokeAdd(entity);
        }
        return super.doInsert(entityList);
    }

    @Override
    protected B[] doUpdate(List<B> beanList) {
        for (B bean : beanList) {
            getInvokeService().doInvokeUpdate(bean);
        }
        return super.doUpdate(beanList);
    }

    @Override
    protected Boolean doDelete(List<Integer> idList) {
        for (Integer id : idList) {
            getInvokeService().doInvokeDelete(id);
        }
        return super.doDelete(idList);
    }
}
