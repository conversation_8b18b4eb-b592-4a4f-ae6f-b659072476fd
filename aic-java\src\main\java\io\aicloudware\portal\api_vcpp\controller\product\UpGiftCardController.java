package io.aicloudware.portal.api_vcpp.controller.product;

import io.aicloudware.portal.api_vcpp.entity.UpCoupon;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.api_vcpp.service.product.IUpServicePlanService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/giftCard")
public class UpGiftCardController extends BaseEntityController<UpCoupon, UpCouponBean, UpCouponResultBean> {

    @Autowired
    private IUpServicePlanService upServicePlanService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpCouponSearchBean searchBean) {
        UpCoupon entity = BeanCopyUtil.copy(searchBean.getBean(), UpCoupon.class);
        if(StringUtils.isNotEmpty(entity.getName())) {
            UpCouponBean fuzzyBean = new UpCouponBean();
            fuzzyBean.setName(entity.getName());
//            fuzzyBean.setRegionsStr(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
        entity.setStatus(RecordStatus.active);
        try {
            UpCouponBean[] entityList = this.doQuery(searchBean, entity);

            if(Utility.isNotEmpty(entityList)){
                for(UpCouponBean bean: entityList){
//                    if(Utility.isNotEmpty(bean.getServicePlanItems())){
//
//                        for(UpServicePlanItemBean itemBean: bean.getServicePlanItems()){
//                            itemBean.setServicePlanItemTypeTxt(itemBean.getServicePlanItemType().getTitle());
//                            if(itemBean.getServicePlanItemSubType() != null){
//                                itemBean.setServicePlanItemSubTypeTxt(itemBean.getServicePlanItemSubType().getTitle());
//                            }
//                        }
//                    }
                }
            }
            UpCouponResultBean result = new UpCouponResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/listByType/{type}", method = RequestMethod.GET)
    @ApiOperation(notes = "/listByType/{type}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class) })
    @ResponseBody
    public ResponseBean listByType(@ApiParam(value = "对象ID") @PathVariable UpServicePlanType type) {
        return ResponseBean.success(upServicePlanService.listByType(type));
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class) })
    @ResponseBody
    public ResponseBean get(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        UpServicePlanBean bean = commonService.load(UpServicePlan.class, UpServicePlanBean.class, id, ThreadCache.getOrgId());
        return ResponseBean.success(bean);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "新增实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    public ResponseBean add(@ApiParam(value = "参数") @RequestBody UpServicePlanBean bean) {
        upServicePlanService.add(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(notes = "/update", httpMethod = "PUT", value = "更新实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    public ResponseBean update(@ApiParam(value = "参数") @RequestBody UpServicePlanBean bean) {
        upServicePlanService.update(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例对象")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
    @ResponseBody
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        upServicePlanService.delete(id);
        return ResponseBean.success(true);
    }
}