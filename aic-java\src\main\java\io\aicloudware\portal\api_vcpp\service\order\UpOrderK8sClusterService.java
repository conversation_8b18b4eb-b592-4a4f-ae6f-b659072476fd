package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderK8sCluster;
import io.aicloudware.portal.api_vcpp.entity.UpOrderK8sClusterNodePool;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterNodePoolBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpK8sCluster;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class UpOrderK8sClusterService extends BaseService implements IUpOrderK8sClusterService {
	
	@Autowired
	private IUpOrderService orderService;

	@Override
	public Integer save(UpOrderK8sClusterBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getFlavor(), "请选择规格！");
		AssertUtil.check(bean.getHostNetworkVpcId(), "请选择VPC！");
		AssertUtil.check(bean.getHostNetworkSubnetId(), "请选择子网！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getHostNetworkVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");

		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getHostNetworkSubnetId());
		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");

		AssertUtil.check(bean.getName(), "请输入名称！");
		SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_k8s_cluster);
		order.setName("[" + OrderType.new_k8s_cluster + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(applyUser);
		order.setApplyUser(applyUser);
		order.setSpOrg(org);

		UpOrderK8sCluster entity = BeanCopyUtil.copy(bean, UpOrderK8sCluster.class);
		entity.setRegion(order.getRegion());
		entity.setName(bean.getName());
		entity.setOrder(order);
		entity.setSpOrg(org);
		entity.setContainerNetworkMode(bean.getContainerNetworkMode());
		entity.setHostNetworkVpcId(bean.getHostNetworkVpcId());
		entity.setHostNetworkSubnetId(bean.getHostNetworkSubnetId());
		entity.setHostNetworkSecurityGroupId(bean.getHostNetworkSecurityGroupId());
		this.dao.insert(order);
		this.dao.insert(entity);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(org);
        task.setRegion(ThreadCache.getRegion());
        dao.insert(task);
		return order.getId();
	}


	@Override
	public Integer update(UpOrderK8sClusterBean bean, UpUser applyUser) {
//		SpVm vm = dao.load(SpVm.class, bean.getVmId());
//		AssertUtil.check(quotaDetail.getId().equals(vm.getUpdateOrderQuotaDetail().getId()),"云服务器变更操作异常！");
//		AssertUtil.check(vm.getSpOrg().getId().equals(ThreadCache.getOrgId()),"云服务器无操作权限！");
//
//		UpOrder order = new UpOrder();
//		order.setRegion(vm.getRegion());
//		order.setType(OrderType.cloud_server_update);
//		order.setName("[" + OrderType.cloud_server_update + "]" + vm.getName());
//		order.setOrderStatus(OrderStatus.pending_deploy);
//		order.setOwner(user);
//		order.setApplyUser(applyUser);
//		order.setSpOrg(user.getOrg());
//		order.setNumber(1);
//		order.setQuota(quotaDetail.getQuota());
//		order.setQuotaDetail(quotaDetail);
//		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
//			order.setCpuNum(quotaDetail.getCpu());
//			order.setMemoryNum(quotaDetail.getMemoryG());
//		}else{
//			order.setVmSet(vmSet);
//			order.setCpuNum(vmSet.getCpuUnit());
//			order.setMemoryNum(vmSet.getMemoryUnit());
//			order.setCpuPrice(vmSet.getCpuPrice());
//			order.setMemoryPrice(vmSet.getMemoryPrice());
//		}
//
//		UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
//		entity.setTaskSequence(100);
//		entity.setOwner(user);
//		entity.setName("update-" + vm.getName() + "-" + quotaDetail.getSubCode());
//		entity.setOrder(order);
//		entity.setSpOrg(user.getOrg());
//		entity.setCpu(order.getCpuNum());
//		entity.setMemory(order.getMemoryNum());
//		entity.setServerType(bean.getServerType());
//		entity.setVm(vm);
//		entity.setUpdateVapp(vm.getSpVapp());
//		entity.setRegion(order.getRegion());
//
//		this.dao.insert(order);
//		this.dao.insert(entity);
//
//        UpTask task = new UpTask();
//        task.setName(order.getName());
//        task.setType(UpTaskType.up_application);
//        task.setOrder(order);
//        task.setOrderType(order.getType());
//        task.setTaskStatus(UpTaskStatus.start);
//        task.setSpOrg(org);
//        task.setRegion(order.getRegion());
//        dao.insert(task);
//		return order.getId();
		return null;
	}

	@Override
	public Integer saveNodePool(UpOrderK8sClusterNodePoolBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getFlavor(), "请选择规格！");
		AssertUtil.check(bean.getClusterId(), "请指定集群！");
		SpK8sCluster spK8sCluster = this.dao.load(SpK8sCluster.class, bean.getClusterId());
		AssertUtil.check(spK8sCluster != null && RecordStatus.active.equals(spK8sCluster.getStatus()), "集群不存在！");

		AssertUtil.check(bean.getName(), "请输入名称！");
		SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_k8s_cluster_node_pool);
		order.setName("[" + OrderType.new_k8s_cluster_node_pool + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(applyUser);
		order.setApplyUser(applyUser);
		order.setSpOrg(org);

		UpOrderK8sClusterNodePool entity = BeanCopyUtil.copy(bean, UpOrderK8sClusterNodePool.class);
		entity.setRegion(order.getRegion());
		entity.setName(bean.getName());
		entity.setOrder(order);
		entity.setSpOrg(org);

		this.dao.insert(order);
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(org);
		task.setRegion(ThreadCache.getRegion());
		dao.insert(task);
		return order.getId();
	}


}
