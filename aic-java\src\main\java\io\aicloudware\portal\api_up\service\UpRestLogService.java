package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_rest.framework.bean.RestResponseBean;
import io.aicloudware.portal.api_rest.framework.bean.RestResultDataBean;
import io.aicloudware.portal.api_up.entity.UpRestLog;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.annotation.RestLogAspect;
import io.aicloudware.portal.framework.dao.ICloudDao;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Aspect
@Component
@Transactional
public class UpRestLogService extends BaseService{
	@Autowired
	private IUpOrderQuotaService quotaService;
	
	@Autowired
    protected ICloudDao cloudDao;

	@Autowired
	private ISpRegionService spRegionService;

	@AfterReturning(pointcut = "@annotation(restLogAspect)", returning = "result")
	public void cloudNetSuccess(JoinPoint point, RestLogAspect restLogAspect, Object result) {
		try {
			insertLog(point, restLogAspect, result, true, null);
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	@AfterThrowing(pointcut = "@annotation(restLogAspect)", throwing = "ex")
	public void cloudNetFailed(JoinPoint point, RestLogAspect restLogAspect, Throwable ex) {
		try {
			insertLog(point, restLogAspect, null, false, ex);
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	private void insertLog(JoinPoint point, RestLogAspect restLogAspect, Object result, Boolean process, Throwable ex) {
		Object[] items = point.getArgs();
		JSONObject json = (JSONObject)items[0];
		UpRestLog entity = new UpRestLog();
//		if(restLogAspect.type().equals("in")){
//		}
		try{
			entity.setRegion(getRegionByCode(json.getString( "cityId")));
		}catch(JSONException e){

		}

		try{
			entity.setMainAgreementId(json.getString("mainAgreementId"));
		}catch(JSONException e){

		}

		try {
			entity.setCustomNo(json.getString("customNo"));
		}catch(JSONException e){

		}
		entity.setParameters(json.toString());

		if(process){
			if(result == null){
				result = "";
			}
			if(result instanceof RestResponseBean){
				RestResponseBean responseBean = (RestResponseBean)result;
				if(responseBean.getResultData() instanceof RestResultDataBean){
					entity.setCallback("RestResponseBean:" + net.sf.json.JSONObject.fromObject(responseBean).toString());
				}else{
					entity.setCallback(responseBean.toString());
				}
			}else{
				entity.setCallback(result.toString());
			}
			entity.setRestStatus("success");
		}else{
			entity.setCallback(ex.toString());
			entity.setRestStatus("failed");
		}
		entity.setMethod(point.getSignature().getName());
		entity.setName(entity.getMethod() + "~" + entity.getRestStatus());
		cloudDao.insert(entity);
	}


	private SpRegionEntity getRegionByCode(String code) {
//		for (SpRegion region : SpRegion.values()) {
//			if (region.getCode().equals(code)) {
//				return region;
//			}
//		}
//		return null;
		return spRegionService.getRegionByCode(code);
	}
}
