package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "部署参数（蓝图机器自定义属性组）")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, ReqPropertyGroupBean.class})
public class ReqPropertyGroupBean extends RecordBean {

    @ApiModelProperty(value = "虚机申请ID")
    private Integer reqVmId;

    @ApiModelProperty(value = "虚机申请名称")
    private String reqVmName;

    @ApiModelProperty(value = "软件组件关系ID")
    private Integer blueprintRelationId;

    @ApiModelProperty(value = "软件组件关系名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String blueprintRelationName;

    @ApiModelProperty(value = "软件组件关系显示名称")
    private String blueprintRelationDisplayName;

    @ApiModelProperty(value = "属性组ID")
    private Integer propertyGroupId;

    @ApiModelProperty(value = "属性组名称")
    private String propertyGroupName;

    @ApiModelProperty(value = "属性组显示名称")
    private String propertyGroupDisplayName;

    @ApiModelProperty(value = "属性项列表")
    private ReqPropertyItemBean[] propertyItemList;

    public Integer getReqVmId() {
        return reqVmId;
    }

    public void setReqVmId(Integer reqVmId) {
        this.reqVmId = reqVmId;
    }

    public String getReqVmName() {
        return reqVmName;
    }

    public void setReqVmName(String reqVmName) {
        this.reqVmName = reqVmName;
    }

    public Integer getBlueprintRelationId() {
        return blueprintRelationId;
    }

    public void setBlueprintRelationId(Integer blueprintRelationId) {
        this.blueprintRelationId = blueprintRelationId;
    }

    public String getBlueprintRelationName() {
        return blueprintRelationName;
    }

    public void setBlueprintRelationName(String blueprintRelationName) {
        this.blueprintRelationName = blueprintRelationName;
    }

    public String getBlueprintRelationDisplayName() {
        return blueprintRelationDisplayName;
    }

    public void setBlueprintRelationDisplayName(String blueprintRelationDisplayName) {
        this.blueprintRelationDisplayName = blueprintRelationDisplayName;
    }

    public Integer getPropertyGroupId() {
        return propertyGroupId;
    }

    public void setPropertyGroupId(Integer propertyGroupId) {
        this.propertyGroupId = propertyGroupId;
    }

    public String getPropertyGroupName() {
        return propertyGroupName;
    }

    public void setPropertyGroupName(String propertyGroupName) {
        this.propertyGroupName = propertyGroupName;
    }

    public String getPropertyGroupDisplayName() {
        return propertyGroupDisplayName;
    }

    public void setPropertyGroupDisplayName(String propertyGroupDisplayName) {
        this.propertyGroupDisplayName = propertyGroupDisplayName;
    }

    public ReqPropertyItemBean[] getPropertyItemList() {
        return propertyItemList;
    }

    public void setPropertyItemList(ReqPropertyItemBean[] propertyItemList) {
        this.propertyItemList = propertyItemList;
    }
}
