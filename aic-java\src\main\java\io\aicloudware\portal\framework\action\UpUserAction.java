package io.aicloudware.portal.framework.action;

import java.util.Arrays;
import java.util.List;

import org.apache.http.message.BasicHeader;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserListBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserSearchBean;

public class UpUserAction extends RemoteUpModuleAction<UpUserBean, UpUserListBean, UpUserSearchBean, UpUserResultBean> {

    public UpUserAction(RemoteHost remoteHost) {
        super(remoteHost, "user");
    }

    @Override
    public UpUserBean add(UpUserBean bean) throws SDKException {
        return super.add(bean);
    }

    @Override
    public UpUserListBean add(UpUserListBean bean) throws SDKException {
        return super.add(bean);
    }

    @Override
    public UpUserBean update(UpUserBean bean) throws SDKException {
        return super.update(bean);
    }

    @Override
    public UpUserListBean update(UpUserListBean bean) throws SDKException {
        return super.update(bean);
    }

    @Override
    public Boolean delete(Integer id) throws SDKException {
        return super.delete(id);
    }

    @Override
    public Boolean delete(List<Integer> idList) throws SDKException {
        return super.delete(idList);
    }

    @Override
    public UpUserResultBean query(UpUserSearchBean bean) throws SDKException {
        return super.query(bean);
    }

    @Override
    public UpUserBean get(Integer id) throws SDKException {
        return super.get(id);
    }

    public UpUserBean getLoginUser() throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonGet("/" + getModule() + "/get_login_user");
            return getResultData(responseBean, getModuleType());
        });
    }

    private String login(UpUserBean bean) throws SDKException {
        return jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonPost("/" + getModule() + "/login", bean);
            return getResultData(responseBean, String.class);
        });
    }

    public void logout() throws SDKException {
        jsonRemote(() -> {
            ResponseBean responseBean = getRemoteHost().jsonGet("/" + getModule() + "/logout");
            return getResultData(responseBean, Boolean.class);
        });
    }

    public static RemoteHost login(String url, String username, String password, String loginTime, String loginToken) {
        RemoteHost remoteHost = new RemoteHost(url);
        UpUserAction userAction = new UpUserAction(remoteHost);
        UpUserBean userBean = new UpUserBean();
        userBean.setName(username);
        userBean.setPassword(password);
        //userBean.setLoginTime(loginTime);
        //userBean.setLoginToken(loginToken);
        String userToken = userAction.login(userBean);
        remoteHost.setHeaderList(Arrays.asList(new BasicHeader("Authorization", userToken)));
        return remoteHost;
    }

}
