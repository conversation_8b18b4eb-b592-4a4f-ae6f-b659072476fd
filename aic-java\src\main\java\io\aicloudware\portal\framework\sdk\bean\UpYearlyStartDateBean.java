package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "年度起始日期设置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpYearlyStartDateBean {

    @ApiModelProperty(value = "年度起始日期")
    private String yearlyStartDate;

    public String getYearlyStartDate() {
        return yearlyStartDate;
    }

    public void setYearlyStartDate(String yearlyStartDate) {
        this.yearlyStartDate = yearlyStartDate;
    }

}
