package io.aicloudware.portal.framework.sdk.bean.vcpp;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ChargePeriod;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ElasticIpChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ResourceType;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "弹性公网IP")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderElasticIpBean.class})
public class UpOrderElasticIpBean extends RecordBean {

	@ApiModelProperty(value = "协议ID")
	private Integer quotaId;
	
	@ApiModelProperty(value = "子协议ID")
	private Integer quotaDetailId;
	
	@ApiModelProperty(value = "付费方式")
	private PaymentType paymentType;
	
	@ApiModelProperty(value = "计费方式")
	private ElasticIpChargeType chargeType;
	
	@ApiModelProperty(value = "带宽")
	private Integer bandwidth;
	
	@ApiModelProperty(value = "计费周期")
	private ChargePeriod chargePeriod;
	
	@ApiModelProperty(value = "是否绑定相关资源")
	private Boolean isResource;
	
	@ApiModelProperty(value = "资源类型")
	private ResourceType resourceType;
	
	@ApiModelProperty(value = "云服务器ID")
	private Integer cloudServerId;
	
	@ApiModelProperty(value = "负载均衡ID")
	private Integer loadBalanceId;
	
	@ApiModelProperty(value = "所有人ID")
	private Integer ownerId;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "带宽峰值产品配置ID")
	private Integer bandwidthConfigId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "公网IP")
	private String elasticIP;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public ElasticIpChargeType getChargeType() {
		return chargeType;
	}

	public void setChargeType(ElasticIpChargeType chargeType) {
		this.chargeType = chargeType;
	}

	public Integer getBandwidth() {
		return bandwidth;
	}

	public void setBandwidth(Integer bandwidth) {
		this.bandwidth = bandwidth;
	}

	public ChargePeriod getChargePeriod() {
		return chargePeriod;
	}

	public void setChargePeriod(ChargePeriod chargePeriod) {
		this.chargePeriod = chargePeriod;
	}

	public Boolean getIsResource() {
		return isResource;
	}

	public void setIsResource(Boolean isResource) {
		this.isResource = isResource;
	}

	public ResourceType getResourceType() {
		return resourceType;
	}

	public void setResourceType(ResourceType resourceType) {
		this.resourceType = resourceType;
	}

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public Integer getLoadBalanceId() {
		return loadBalanceId;
	}

	public void setLoadBalanceId(Integer loadBalanceId) {
		this.loadBalanceId = loadBalanceId;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getBandwidthConfigId() {
		return bandwidthConfigId;
	}

	public void setBandwidthConfigId(Integer bandwidthConfigId) {
		this.bandwidthConfigId = bandwidthConfigId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public Integer getQuotaId() {
		return quotaId;
	}

	public void setQuotaId(Integer quotaId) {
		this.quotaId = quotaId;
	}

	public Integer getQuotaDetailId() {
		return quotaDetailId;
	}

	public void setQuotaDetailId(Integer quotaDetailId) {
		this.quotaDetailId = quotaDetailId;
	}

	public String getElasticIP() {
		return elasticIP;
	}

	public void setElasticIP(String elasticIP) {
		this.elasticIP = elasticIP;
	}
	
	
}
