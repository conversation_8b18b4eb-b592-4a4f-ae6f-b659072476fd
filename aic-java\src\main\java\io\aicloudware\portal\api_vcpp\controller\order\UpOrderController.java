package io.aicloudware.portal.api_vcpp.controller.order;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import io.aicloudware.portal.framework.sdk.bean.UpOrderResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpOrderSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 订单
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order")
public class UpOrderController extends BaseUpController<UpOrder, UpOrderBean, UpOrderResultBean> {

	@Autowired
	private IUpOrderService orderService;


	/**
	 * 配置
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/init")
	@ResponseBody
	public ResponseBean init(HttpServletRequest request) {
		Map<String, Map<String, ?>> datas = new HashMap<>();
		// 订单状态
		Map<String, String> orderStatus = new LinkedHashMap<String, String>();
		for (OrderStatus type : OrderStatus.values()) {
			if(type != OrderStatus.approving && type != OrderStatus.approve_reject && type != OrderStatus.pending_approve  && type != OrderStatus.deploy_reject &&  type != OrderStatus.pending_audit ){
				orderStatus.put(type.toString(), type.getTitle());
			}
		}
		datas.put("orderStatus", orderStatus);

		// 订单类型
		Map<String, String> orderType = new LinkedHashMap<String, String>();
		for (OrderType type : OrderType.values()) {
			orderType.put(type.toString(), type.getTitle());
		}
		datas.put("orderType", orderType);

		// 任务状态
		Map<String, String> taskStatus = new LinkedHashMap<String, String>();
		for (UpTaskStatus type : UpTaskStatus.values()) {
			taskStatus.put(type.toString(), type.getTitle());
		}
		datas.put("taskStatus", taskStatus);

		return ResponseBean.success(datas);
	}

	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/query", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean query(@RequestBody UpOrderSearchBean searchBean, HttpServletRequest request) {

		if(searchBean.getOrderName1()  == null){
			searchBean.setOrderName1("updateTm");
		}
		if(searchBean.getOrderBy1()  == null){
			searchBean.setOrderBy1(false);
		}
		return ResponseBean.success(orderService.query(searchBean));
	}
	
	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/redeploy/{id}", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean redeploy(@ApiParam(value = "对象ID") @PathVariable String id, HttpServletRequest request) {
		try {
			orderService.redepoly(Integer.valueOf(id));
    	}catch(Exception e) {
    		return ResponseBean.error(2, "系统异常", e.getMessage());
    	}
		return ResponseBean.success(true);
	}

	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update", method = RequestMethod.PUT)
	@ResponseBody
	public ResponseBean update(@RequestBody UpOrderBean bean, HttpServletRequest request) {
		orderService.update(bean);
		return ResponseBean.success(true);
	}

//	/**
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/detail", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean detail(@RequestBody Integer workSheetId, HttpServletRequest request) {
//		Integer userId = ThreadCache.getUserId();
//		return ResponseBean.success(orderService.detail(workSheetId, userId));
//	}
//
//	/**
//	 * 保存
//	 * 
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/save", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean save(@RequestBody UpWorkSheetDetailBean bean, HttpServletRequest request) {
//		Integer userId = ThreadCache.getUserId();
//		return ResponseBean.success(workSheetService.save(bean, userId));
//	}
//
//	/**
//	 * 更新
//	 * 
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/update", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean save(@RequestBody UpWorkSheetBean bean, HttpServletRequest request) {
//		Integer userId = ThreadCache.getUserId();
//		return ResponseBean.success(workSheetService.update(bean, userId));
//	}

}