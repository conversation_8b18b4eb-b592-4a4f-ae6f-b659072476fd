package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "网卡")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderCloudServerNetworkBean.class})
public class UpOrderCloudServerNetworkBean extends SpRecordBean {

	@ApiModelProperty(value = "网卡序号")
	private Integer nicNumber;
	
	@ApiModelProperty(value = "网络ID")
	private Integer networkId;
	
	@ApiModelProperty(value = "网络适配器类型")
	private UpOrderSystemEnums.NetworkAdapterType networkAdapterType;
	
	@ApiModelProperty(value = "IP分配方式")
	private UpOrderSystemEnums.IpAddressAllocationMode ipAddressAllocationMode;
	
	@ApiModelProperty(value = "IP地址")
	private String ipAddress;
	
	@ApiModelProperty(value = "是否连接")
	private Boolean connected;

	@ApiModelProperty(value = "网络类型")
	private String fenceMode;
	
	@ApiModelProperty(value = "所有人ID")
	private Integer ownerId;
	
	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;

	public Integer getNicNumber() {
		return nicNumber;
	}

	public void setNicNumber(Integer nicNumber) {
		this.nicNumber = nicNumber;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public UpOrderSystemEnums.NetworkAdapterType getNetworkAdapterType() {
		return networkAdapterType;
	}

	public void setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType networkAdapterType) {
		this.networkAdapterType = networkAdapterType;
	}

	public UpOrderSystemEnums.IpAddressAllocationMode getIpAddressAllocationMode() {
		return ipAddressAllocationMode;
	}

	public void setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode ipAddressAllocationMode) {
		this.ipAddressAllocationMode = ipAddressAllocationMode;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public Boolean getConnected() {
		return connected;
	}

	public void setConnected(Boolean connected) {
		this.connected = connected;
	}

	public String getFenceMode() {
		return fenceMode;
	}

	public void setFenceMode(String fenceMode) {
		this.fenceMode = fenceMode;
	}

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}
}
