package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "vAppBackup")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpVappBackupBean.class})
public class SpVappBackupBean extends SpRecordBean {

    @ApiModelProperty(value = "备份序号")
    private Integer sequence;

    @ApiModelProperty(value = "UUID")
    private String uuid;

    @ApiModelProperty(value = "电源状态")
    private SpVmPowerStatus powerStatus;
    
    @ApiModelProperty(value = "状态")
    private RecordStatus status;

    public SpVmPowerStatus getPowerStatus() {
        return powerStatus;
    }

    public void setPowerStatus(SpVmPowerStatus powerStatus) {
        this.powerStatus = powerStatus;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

}
