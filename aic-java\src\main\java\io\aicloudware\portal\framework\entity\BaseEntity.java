package io.aicloudware.portal.framework.entity;

import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.bean.BaseBean;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import org.hibernate.criterion.DetachedCriteria;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;
import java.util.Set;

@MappedSuperclass
public abstract class BaseEntity<B extends RecordBean> extends BaseBean implements IEntity<B> {

    @EntityProperty(isNullCopy = false)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "name", nullable = false)
    private String name;

    @EntityProperty(isNullCopy = false)
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus status;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "create_by", nullable = false)
    private Integer createBy;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "update_by", nullable = false)
    private Integer updateBy;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "create_tm", nullable = false)
    private Date createTm;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "update_tm", nullable = false)
    private Date updateTm;

    public BaseEntity() {
    }

    public BaseEntity(Integer id) {
        this.id = id;
    }

    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<B> searchBean, Set<String> aliasSet) {
        DaoUtil.addEntityQuery(criteria, this, aliasSet);
        DaoUtil.addFuzzyQuery(criteria, searchBean.getFuzzyBean(), aliasSet);
        return criteria;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    public Integer getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }

    public Date getUpdateTm() {
        return updateTm;
    }

    public void setUpdateTm(Date updateTm) {
        this.updateTm = updateTm;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }
}
