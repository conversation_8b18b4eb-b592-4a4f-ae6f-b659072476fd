package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "数据存储资源使用情况")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, VcDataStoreUsageBean.class})
public class VcDataStoreUsageBean extends RecordBean {

    @ApiModelProperty(value = "日期")
    private Integer date;

    @ApiModelProperty(value = "数据存储ID")
    private Integer dataStoreId;

    @ApiModelProperty(value = "数据存储名称")
    private String dataStoreName;

    @ApiModelProperty(value = "存储总量")
    private Long diskTotalB;

    @ApiModelProperty(value = "存储使用")
    private Long diskUsageB;

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public Integer getDataStoreId() {
        return dataStoreId;
    }

    public void setDataStoreId(Integer dataStoreId) {
        this.dataStoreId = dataStoreId;
    }

    public String getDataStoreName() {
        return dataStoreName;
    }

    public void setDataStoreName(String dataStoreName) {
        this.dataStoreName = dataStoreName;
    }

    public Long getDiskTotalB() {
        return diskTotalB;
    }

    public void setDiskTotalB(Long diskTotalB) {
        this.diskTotalB = diskTotalB;
    }

    public Long getDiskUsageB() {
        return diskUsageB;
    }

    public void setDiskUsageB(Long diskUsageB) {
        this.diskUsageB = diskUsageB;
    }
}
