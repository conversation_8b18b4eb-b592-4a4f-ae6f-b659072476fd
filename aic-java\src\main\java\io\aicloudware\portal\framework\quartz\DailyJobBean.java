package io.aicloudware.portal.framework.quartz;


import io.aicloudware.portal.api_up.service.IUpQuotaService;
import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.api_up.service.IUpUserService;
import io.aicloudware.portal.api_vcpp.service.product.IUpInstanceIndexService;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.ClassReaderUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.platform_vcd.service.ISpServerConnectionService;
import org.apache.commons.lang.StringUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.persistence.Table;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Component
public final class DailyJobBean extends QuartzJobBean {
    protected final Logger logger = Logger.getLogger(getClass());

    @Value("${daily_job}")
	private String quartz;
    
    @Autowired
    protected ISpServerConnectionService spServerConnectionService;

    @Autowired
    protected IUpQuotaService upQuotaService;

//    @Autowired
//    protected ISpDeploymentService spDeploymentService;

    @Autowired
    protected IUpUserService upUserService;

    @Autowired
    protected ICommonService commonService;

    @Autowired
	private IUpSystemConfigService configService;

    @Autowired
    private IUpInstanceIndexService upInstanceIndexService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    	UpSystemConfigBean bean = configService.get(UpSystemConfigKey.operation_control);
    	if(bean!=null && StringUtils.isNotEmpty(bean.getValue()) && "yes".equals(bean.getValue())) {
    		return;
    	}
    	if(!"yes".equals(quartz)) {
    		return;
    	}
        List<JobExecutionContext> jobs;
        try {
            jobs = jobExecutionContext.getScheduler().getCurrentlyExecutingJobs();
            for (JobExecutionContext job : jobs) {
                if (job.getTrigger().equals(jobExecutionContext.getTrigger())
                        && !job.getFireInstanceId().equals(jobExecutionContext.getFireInstanceId())) {
//                    logger.debug("There's another instance running, so leaving " + this);
                    return;
                }
            }
        } catch (SchedulerException e) {
            logger.trace("DailyJobBean.executeInternal() error", e);
            return;
        }

//        spServerConnectionService.notifyAutoScaleVra();
        spServerConnectionService.notifyRefreshAll(true, true, true);
//        upQuotaService.notifyRefreshUsage();
//        spDeploymentService.handleExpiredDeployment();
//        upStaticsRefreshService.notifyRefresh(null);
//        upUserService.notifySyncLdapUser();
        commonService.doTransactionalTask(this::cascadeHandleDirtyData);
        upInstanceIndexService.sync();
    }

    private void cascadeHandleDirtyData() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        Date deleteDate = calendar.getTime();
        final Set<Class<?>> classSet = ClassReaderUtil.getClasses(IEntity.class.getPackage().getName());
        for (Class<?> clazz : classSet) {
            Table table = clazz.getAnnotation(Table.class);
            if (table != null) {
                List<? extends IEntity> dataList = BeanFactory.getCloudDao().listIncludeDeleted((Class<? extends IEntity>) clazz);
                for (IEntity entity : dataList) {
                    if (RecordStatus.deleted.equals(entity.getStatus())) {
                        if (entity.getUpdateTm().compareTo(deleteDate) < 0) {
//                            BeanFactory.getCloudDao().destroy(entity.getClass(), entity.getId());
                        } else {
                            DaoUtil.cascadeChildEntity(entity, null, child -> BeanFactory.getCloudDao().delete(child.getClass(), child.getId()));
                        }
                    } else {
                        DaoUtil.cascadeParentEntity(entity, parent -> {
                            if (RecordStatus.deleted.equals(parent.getStatus())) {
                                BeanFactory.getCloudDao().delete(parent.getClass(), parent.getId());
                                if (!RecordStatus.deleted.equals(entity.getStatus())) {
                                    BeanFactory.getCloudDao().delete(entity.getClass(), entity.getId());
                                }
                            }
                        }, null);
                    }
                }
            }
        }
    }
}
