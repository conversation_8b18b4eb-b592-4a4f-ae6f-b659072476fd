package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "年度起始日期设置结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpYearlyStartDateSetResultBean {

    @ApiModelProperty(value = "年度起始日期")
    private String yearlyStartDate;

    @ApiModelProperty(value = "年度起始日期（可选最大日期）")
    private String maxYearlyStartDate;

    @ApiModelProperty(value = "年度起始日期（可选最小日期）")
    private String minYearlyStartDate;

    @ApiModelProperty(value = "自然年")
    private String year;

    public String getYearlyStartDate() {
        return yearlyStartDate;
    }

    public void setYearlyStartDate(String yearlyStartDate) {
        this.yearlyStartDate = yearlyStartDate;
    }

    public String getMaxYearlyStartDate() {
        return maxYearlyStartDate;
    }

    public void setMaxYearlyStartDate(String maxYearlyStartDate) {
        this.maxYearlyStartDate = maxYearlyStartDate;
    }

    public String getMinYearlyStartDate() {
        return minYearlyStartDate;
    }

    public void setMinYearlyStartDate(String minYearlyStartDate) {
        this.minYearlyStartDate = minYearlyStartDate;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }


}
