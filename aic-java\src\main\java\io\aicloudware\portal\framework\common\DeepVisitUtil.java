package io.aicloudware.portal.framework.common;

import io.aicloudware.portal.framework.executor.IExecutorA;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

public class DeepVisitUtil {

    public static void visitDeploymentVM(SpVapp deployment, IExecutorA<SpVm> executor) {
        if (deployment != null) {
            if (deployment.getVmList() != null) {
                for (SpVm vm : deployment.getVmList()) {
                    executor.doExecute(vm);
                }
            }
        }
    }
}
