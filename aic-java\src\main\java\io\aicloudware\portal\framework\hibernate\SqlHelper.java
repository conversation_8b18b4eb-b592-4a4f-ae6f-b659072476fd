package io.aicloudware.portal.framework.hibernate;

import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.Logger;
import org.slf4j.event.Level;

import java.sql.ResultSet;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class SqlHelper {
    private static final Logger logger = Logger.getLogger(SqlHelper.class);

    public static String formatSql_ParamIndex(String sql, Map<Integer, Object> paramMap) {
        int count = 0;
        while (sql.contains("?")) {
            sql = sql.replaceFirst("\\?", "_P:" + (count++) + "_");
        }
        for (int i = 0; i < count; i++) {
            sql = sql.replace("_P:" + i + "_", convert2String(paramMap.get(i + 1)));
        }
        return sql;
    }

    public static String formatSql_ParamName(String sql, Map<String, Object> paramMap) {
        for (String key : paramMap.keySet()) {
            sql = sql.replace("@" + key, convert2String(paramMap.get(key)));
        }
        return sql;
    }

    private static long global_count = 10000L;
    private static final ThreadLocal<Long> local_count = new ThreadLocal<>();

    public static void outputSql(String sql) {
        if (Logger.checkLevel(Level.TRACE)) {
            global_count++;
            if (global_count >= 100000) {
                global_count -= 90000;
            }
            local_count.set(global_count);
//            System.out.println("=== SQL ==== " + String.valueOf(local_count.get()).substring(1) + " : " + sql);
        }
    }

    public static void outputResult(Object result) {
        if (Logger.checkLevel(Level.TRACE)) {
            String value = null;
            if (result != null) {
                if (result instanceof List) {
                    List<Object> list = (List<Object>) result;
                    if (list.size() == 1) {
                        outputResult(ListUtil.first(list));
                    } else {
                        value = "size -> " + list.size();
                    }
                } else if (result instanceof IEntity) {
                    IEntity entity = (IEntity) result;
                    value = result.getClass().getSimpleName() + " -> " + entity.getId() + " : " + entity.getName();
                } else if (result.getClass().isArray()) {
                    value = Arrays.asList((Object[]) result).toString();
                } else if (result instanceof ResultSet) {
                } else {
                    value = result.toString();
                }
                if (value == null) {
                    return;
                }
            }
            if (local_count.get() == null) {
                local_count.set(global_count);
            }
//            System.out.println("== Result == " + String.valueOf(local_count.get()).substring(1) + " : " + value);
        }
    }

    public static String convert2String(Object param) {
        if (param == null) {
            return "null";
        } else if (param instanceof String) {
            return "'" + param + "'";
        } else if (param instanceof Date) {
            return "to_date('" + FormatUtil.formatDateTime((Date) param) + "','YYYY-MM-DD HH24:MI:SS')";
        } else {
            return String.valueOf(param);
        }
    }
}
