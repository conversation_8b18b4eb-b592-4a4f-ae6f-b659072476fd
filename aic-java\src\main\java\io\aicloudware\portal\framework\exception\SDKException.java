package io.aicloudware.portal.framework.exception;

import io.aicloudware.portal.framework.bean.ResponseBean;

public class SDKException extends BaseException {

    private final ResponseBean errorInfo;

    public SDKException(ResponseBean errorInfo) {
        this(errorInfo, null);
    }

    public SDKException(ResponseBean errorInfo, Throwable t) {
        super(errorInfo.getErrorMsg(), t);
        this.errorInfo = errorInfo;
    }

    public ResponseBean getErrorInfo() {
        return errorInfo;
    }
}
