package io.aicloudware.portal.framework.action;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.executor.IExecutorR;
import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.utility.Logger;

public class RemoteAction {

    public static final String UnSupportExceptionMessage = "无法执行该操作";

    protected final Logger logger = Logger.getLogger(getClass());

    private final RemoteHost remoteHost;
    private final String module;

    public RemoteAction(RemoteHost remoteHost, String module) {
        this.remoteHost = remoteHost;
        this.module = module;
    }

    public final RemoteHost getRemoteHost() {
        return remoteHost;
    }

    public String getModule() {
        return module;
    }

    public final <T> T jsonRemote(IExecutorR<T> executor) {
        try {
            return executor.doExecute();
        } catch (Throwable t) {
            SDKException sdkException = null;
            if (t instanceof SDKException) {
                sdkException = (SDKException) t;
            } else {
                sdkException = new SDKException(ResponseBean.error(11, "客户端未知异常", t.getMessage()), t);
            }
            ResponseBean bean = sdkException.getErrorInfo();
            logger.error(bean.getErrorCode() + ":" + bean.getErrorType() + ":" + bean.getErrorMsg(), t);
            throw sdkException;
        }
    }

    public static <T> T getResultData(ResponseBean responseBean, Class<T> type) {
        try {
            return responseBean.getData(type);
        } catch (Throwable t) {
            throw new SDKException(ResponseBean.error(10, "Json无法转换成Bean", type.getSimpleName() + ": " + responseBean.getJsonData()), t);
        }
    }

}
