package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductLoadBalanceSetBean;

@Entity
@Table(name = "up_product_load_balance_set")
@Access(AccessType.FIELD)
public class UpProductLoadBalanceSet extends BaseUpEntity<UpProductLoadBalanceSetBean> {

	private static final long serialVersionUID = -2839660769450711158L;

	@Column(name = "payment_type")
	private String paymentType;

	@JoinColumn(name = "load_balance_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem loadBalanceProductItem;

	@Column(name = "unit")
	private Integer unit;
	
	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "enabled")
	private Boolean enabled;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public UpProductItem getLoadBalanceProductItem() {
		return loadBalanceProductItem;
	}

	public void setLoadBalanceProductItem(UpProductItem loadBalanceProductItem) {
		this.loadBalanceProductItem = loadBalanceProductItem;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

}
