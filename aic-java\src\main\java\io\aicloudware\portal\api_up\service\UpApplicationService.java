package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.*;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.IDisplayName;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import io.aicloudware.portal.framework.bean.UpSimpleOperateBean;
import io.aicloudware.portal.framework.common.DeepVisitUtil;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.entity.IResourceEntity;
import io.aicloudware.portal.framework.executor.IExecutorA;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class UpApplicationService extends BaseService implements IUpApplicationService {

    @Autowired
    private IUpTaskService upTaskService;

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @Autowired
    private IUpApprovalService upApprovalHistoryService;

    @Autowired
    private IUpOperationLogService upOperationLogService;

    @Autowired
    private ISpRegionService spRegionService;

    @Override
    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createAddApplication(UpApplicationType type, List<R> entityList, UpApplicationBean bean, Class<E> entityClazz) {
        List<String> nameList = new ArrayList<>();
        for (R entity : entityList) {
            if (Utility.isNotEmpty(entity.getName())) {
                nameList.add(entity.getName());
            }
        }
        if (Utility.isNotEmpty(entityList)) {
            dao.validateDuplicateName(entityClazz, nameList);

            if (Utility.isEmpty(bean.getOperateInfo()) && UpApplicationType.deployment_add.equals(type) && entityList.get(0) instanceof ReqDeployment) {
                SpVappTemplate blueprint = ((ReqDeployment) entityList.get(0)).getVappTemplate();
                blueprint = dao.load(SpVappTemplate.class, blueprint.getId());
                bean.setOperateInfo(IDisplayName.removeDisplayNameSuffix(blueprint.getDisplayName()));
            }
        }

        if (bean.getApplicationStatus() == null) {
            bean.setApplicationStatus(UpApplicationStatus.pending_submit);
        }

        UpApplication application = createApplication(type, entityList, bean);
        UpOrder order = new UpOrder();
        for (R request : entityList) {
            DaoUtil.cascadeChildEntity(request, entity1 -> {
                if (entity1 instanceof IRequestEntity) {
                    ((IRequestEntity) entity1).setOrder(order);
                }
                if (entity1 instanceof ReqVm) {
                    ReqVm reqVm = (ReqVm) entity1;
                    reqVm.setOsPassword(EncryptUtil.encryptWithRSA(reqVm.getOsPassword()));
                }
            }, null);
            dao.insert(request);
        }

        //handleApplicationProcess(application);

        return BeanCopyUtil.copy2Bean(application, UpApplicationBean.class);
    }

    @Override
    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createUpdateApplication(UpApplicationType type, List<R> entityList, UpApplicationBean bean, Class<E> entityClazz) {
        Map<Integer, R> requestMap = ListUtil.map(entityList, new ListUtil.ConvertKey<R, Integer>() {
            @Override
            public Integer getKey(R value) {
                return value.getId();
            }
        });
        Map<Integer, E> entityMap = dao.map(entityClazz, requestMap.keySet());
        List<String> nameList = new ArrayList<>();
        for (Integer id : entityMap.keySet()) {
            if (Utility.isNotEmpty(requestMap.get(id).getName())
                    && Utility.notEquals(requestMap.get(id).getName(), entityMap.get(id).getName())) {
                nameList.add(requestMap.get(id).getName());
            }
        }
        if (Utility.isNotEmpty(entityList)) {
            dao.validateDuplicateName(entityClazz, nameList);
        }

        bean.setApplicationStatus(UpApplicationStatus.pending_approve);

        UpApplication application = createApplication(type, entityList, bean);
        UpOrder order = new UpOrder();
        for (R request : entityList) {
            DaoUtil.cascadeChildEntity(request, entity -> {
                if (entity instanceof IRequestEntity) {
                    ((IRequestEntity) entity).setOrder(order);
                    ((IRequestEntity) entity).setOrigId(entity.getId());
                    entity.setId(null);
                }
            }, null);
            dao.insert(request);
        }

        //handleApplicationProcess(application);

        return BeanCopyUtil.copy2Bean(application, UpApplicationBean.class);
    }

    @Override
    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createSimpleOperateApplication(Class<E> entityClazz, Class<R> requestClazz, SpSimpleOperateBean bean, IExecutorA<R> callback) {

        List<E> entityList = new ArrayList<>(dao.map(entityClazz, Arrays.asList(bean.getIdList())).values());

        bean.setApplicationStatus(UpApplicationStatus.pending_approve);

        UpApplication application = createApplication(bean.getApplicationType(), entityList, bean);
        UpOrder order = new UpOrder();
        dao.evict(entityList);
        for (E entity : entityList) {
            R requestEntity = BeanCopyUtil.copy(entity, requestClazz);
            requestEntity.setOrder(order);
            requestEntity.setOrigId(entity.getId());
            requestEntity.setId(null);
            if (callback != null) {
                callback.doExecute(requestEntity);
            }
            dao.insert(requestEntity);

            if (entity instanceof SpVapp && requestEntity instanceof ReqDeployment) {
                SpVapp spDeployment = dao.load(SpVapp.class, entity.getId());
                DeepVisitUtil.visitDeploymentVM(spDeployment, spVm -> {
                    ReqVm reqVm = BeanCopyUtil.copy(spVm, ReqVm.class);
                    reqVm.setOrder(order);
                    reqVm.setInstanceNum(1);
                    reqVm.setOrigId(spVm.getId());
                    reqVm.setId(null);
                    reqVm.setReqDeployment((ReqDeployment) requestEntity);
                    dao.insert(reqVm);
                });
            }
        }

        //handleApplicationProcess(application);

        return BeanCopyUtil.copy2Bean(application, UpApplicationBean.class);
    }

    @Override
    public UpApplicationBean createAppSystemApplication(SpSimpleOperateBean bean) {
        bean.setApplicationStatus(UpApplicationStatus.pending_approve);
        UpApplication application = createApplication(bean.getApplicationType(), Collections.singletonList((IEntity) null), bean);
        UpOrder order = new UpOrder();
        for (Integer spDeploymentId : bean.getIdList()) {
            SpVapp spDeployment = dao.load(SpVapp.class, spDeploymentId);
            ReqDeployment reqDeployment = BeanCopyUtil.copy(spDeployment, ReqDeployment.class);
            reqDeployment.setOrder(order);
            reqDeployment.setOrigId(spDeploymentId);
            reqDeployment.setId(null);
            dao.insert(reqDeployment);

            DeepVisitUtil.visitDeploymentVM(spDeployment, spVm -> {
                ReqVm reqVm = BeanCopyUtil.copy(spVm, ReqVm.class);
                reqVm.setOrder(order);
                reqVm.setInstanceNum(1);
                reqVm.setOrigId(spVm.getId());
                reqVm.setId(null);
                reqVm.setReqDeployment(reqDeployment);
                dao.insert(reqVm);
            });
        }
        //handleApplicationProcess(application);

        return BeanCopyUtil.copy2Bean(application, UpApplicationBean.class);
    }

    @Override
    public UpTaskBean createSimpleOperateTask(UpTaskType type, String name, Integer targetId) {
        UpTask task = new UpTask();
        task.setName(name);
        task.setType(type);
        task.setTargetId(targetId);
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(new SpRegionEntity(spRegionService.CIDCRP35().getId()));
        dao.insert(task);
        return BeanCopyUtil.copy2Bean(task, UpTaskBean.class);
    }

    private <E extends IEntity> UpApplication createApplication(UpApplicationType type, List<E> entityList, UpApplicationBean bean) {
        AssertUtil.check(Utility.isNotEmpty(entityList), "找不到操作对象记录");
        UpApplication application = new UpApplication();
        application.setType(type);
        application.setPage(bean.getPage());
        application.setOwner(dao.load(UpUser.class, ThreadCache.getUserId()));
        application.setName(Utility.toEmpty(bean.getName()));
        application.setOperateInfo(bean.getOperateInfo());
        application.setReason(bean.getReason());
        application.setComment(bean.getComment());
        application.setApplicationStatus(bean.getApplicationStatus());
        dao.insert(application);
        return application;
    }

    public void handleApplicationProcess(UpOrder order) {
        if (OrderStatus.pending_approve.equals(order.getOrderStatus())) {
            order.setOrderStatus(OrderStatus.pending_deploy);
            dao.update(order);
            handleApplicationProcess(order);
            upOperationLogService.saveOperationLog(UpOperationType.application_create.getTitle(), ThreadCache.getUser(), UpOperationType.application_create, UpOrder.class, order.getId(), order.getName(), order);
        } else if (OrderStatus.pending_deploy.equals(order.getOrderStatus())) {
            UpSystemConfigBean configBean = upSystemConfigService.get(UpSystemConfigKey.auto_deploy);
            if (Boolean.parseBoolean(configBean.getValue())) {
                UpSimpleOperateBean operateBean = new UpSimpleOperateBean();
                operateBean.setIdList(new Integer[]{order.getId()});
                deploy(operateBean);
            }
        } else if (OrderStatus.pending_audit.equals(order.getOrderStatus())) {
            UpSystemConfigBean configBean = upSystemConfigService.get(UpSystemConfigKey.auto_audit);
            if (Boolean.parseBoolean(configBean.getValue())) {
                UpSimpleOperateBean operateBean = new UpSimpleOperateBean();
                operateBean.setIdList(new Integer[]{order.getId()});
                audit(operateBean);
            }
        }
    }

//    private void createApprovalHistory(UpApplication application) {
//        UpApprovalHistory approvalHistory = new UpApprovalHistory();
//
//        UpApprovalSceneSearchBean asSearchBean = new UpApprovalSceneSearchBean();
//        asSearchBean.setOrderName1("priority");
//        asSearchBean.setOrderBy1(false);
//        asSearchBean.setOrderName2("id");
//        asSearchBean.setOrderBy2(false);
//        UpApprovalScene asEntity = new UpApprovalScene();
//        List<UpApprovalScene> asList = dao.query(asSearchBean, asEntity);
//
//        UpApprovalScene approvalScene = null;
//        // 获取最高优先级场景
//        if (!Utility.isEmpty(asList)) {
//            for (UpApprovalScene scene : asList) {
//                if ((null == scene.getAppSystemType())
//                        && (null == scene.getApplicationType() || application.getType().equals(scene.getApplicationType()))) {
//                    approvalScene = scene;
//                    break;
//                }
//            }
//        }
//
//        AssertUtil.check(approvalScene != null, "申请单未找到对应的场景", application);
//
//        // 获取最小节点
//        List<UpApprovalProcessNode> approvalProcessNodeList = approvalScene.getApprovalProcess().getApprovalProcessNodeList();
//        approvalProcessNodeList.sort(Comparator.comparing(UpApprovalProcessNode::getSeq));
//        UpApprovalProcessNode minNode = ListUtil.first(approvalProcessNodeList);
//
//        // 无审批流程
//        if (null == minNode) {
//            application.setApplicationStatus(UpApplicationStatus.pending_deploy);
//            dao.update(application);
//            handleApplicationProcess(application);
//        } else {
//            List<UpUserBean> approverList = upApprovalHistoryService.getApprovers(minNode, application);
//
//            for (UpUserBean userBean : approverList) {
//                approvalHistory.setName(application.getName());
//                approvalHistory.setApplication(application);
//                approvalHistory.setProcessId(approvalScene.getApprovalProcess().getId());
//                approvalHistory.setNode(minNode);
//                approvalHistory.setPendingApprover(new UpUser(userBean.getId()));
//                approvalHistory.setApprovalStatus(UpApprovalStatus.pending_approve);
//
//                dao.insert(approvalHistory);
//            }
//
//            application.setApplicationStatus(UpApplicationStatus.approving);
//            dao.update(application);
//            handleApplicationProcess(application);
//        }
//    }

    @Override
    public void checkMutexApplication(List<Integer> deploymentIdList, List<Integer> vmIdList) {
        Set<Integer> deploymentIdSet = new HashSet<>();
        Set<Integer> vmIdSet = new HashSet<>();
        if (Utility.isNotEmpty(deploymentIdList)) {
            Map<Integer, SpVapp> deploymentMap = dao.map(SpVapp.class, deploymentIdList);
            for (SpVapp deployment : deploymentMap.values()) {
                SpVapp _deployment = deployment;
                if (_deployment != null) {
                    deploymentIdSet.add(_deployment.getId());
                }
                DaoUtil.cascadeChildEntity(deployment, entity -> {
                    if (entity instanceof SpVapp) {
                        deploymentIdSet.add(entity.getId());
                    } else if (entity instanceof SpVm) {
                        vmIdSet.add(entity.getId());
                    }
                }, null);
            }
        }
        if (Utility.isNotEmpty(vmIdList)) {
            Map<Integer, SpVm> vmMap = dao.map(SpVm.class, vmIdList);
            for (SpVm vm : vmMap.values()) {
                SpVapp vapp = vm.getSpVapp();
                if (vapp != null) {
                    deploymentIdSet.add(vapp.getId());
                }
                vmIdSet.add(vm.getId());
            }
        }
        List<UpApplicationStatus> statusCloseList = Arrays.asList(
                UpApplicationStatus.close_success, UpApplicationStatus.close_error, UpApplicationStatus.close_give_up);
        if (Utility.isNotEmpty(deploymentIdSet)) {
            SpDeploymentSearchBean searchBean = new SpDeploymentSearchBean();
            searchBean.setDeploymentIdList(deploymentIdSet.toArray(new Integer[deploymentIdSet.size()]));
            List<ReqDeployment> deploymentList = dao.query(searchBean, new ReqDeployment());
            for (ReqDeployment reqDeployment : deploymentList) {
                UpOrder order = reqDeployment.getOrder();
                AssertUtil.check(statusCloseList.contains(order.getOrderStatus()),
                        "【申请单互斥】申请单:" + order.getId()
//                        "【申请单互斥】申请单:" + application.getId() + "->" + application.getName()
//                                + "; 部署:" + reqDeployment.getId() + "->" + reqDeployment.getName()
                );
            }
        }
        if (Utility.isNotEmpty(vmIdSet)) {
            SpVmSearchBean searchBean = new SpVmSearchBean();
            searchBean.setVmIdList(vmIdSet.toArray(new Integer[vmIdSet.size()]));
            List<ReqVm> vmList = dao.query(searchBean, new ReqVm());
            for (ReqVm reqVm : vmList) {
                UpOrder order = reqVm.getOrder();
                AssertUtil.check(statusCloseList.contains(order.getOrderStatus()),
                        "【申请单互斥】申请单:" + order.getId()
//                        "【申请单互斥】申请单:" + application.getId() + "->" + application.getName()
//                                + "; 虚机:" + reqVm.getId() + "->" + reqVm.getName()
                );
            }
        }
    }

    @Override
    public void resubmit(UpSimpleOperateBean bean) {
        Map<Integer, UpApplication> applicationMap = dao.map(UpApplication.class, Arrays.asList(bean.getIdList()));
        for (UpApplication application : applicationMap.values()) {
            ThreadCache.initOperationLogLocal(UpOperationType.application_create.getTitle(), ThreadCache.getUser(), UpOperationType.application_create, UpApplication.class, application.getId(), application.getName());
            application.setApplicationStatus(UpApplicationStatus.pending_approve);
            dao.update(application, "applicationStatus");
            //handleApplicationProcess(application);
        }
    }

    @Override
    public void deploy(UpSimpleOperateBean bean) {
        Map<Integer, UpOrder> orderMap = dao.map(UpOrder.class, Arrays.asList(bean.getIdList()));
        for (UpOrder order : orderMap.values()) {
            ThreadCache.initOperationLogLocal(UpOperationType.application_deploy_start.getTitle(), ThreadCache.getUser(), UpOperationType.application_deploy_start, UpOrder.class, order.getId(), order.getName());
            UpTask task = new UpTask();
            task.setName(order.getName());
            task.setType(UpTaskType.up_application);
            task.setOrder(order);
            task.setOrderType(order.getType());
            task.setTaskStatus(UpTaskStatus.start);
            dao.insert(task);
            //order.setTask(task);
            order.setOrderStatus(OrderStatus.deploying);
            //order.setDeployStartTm(new Date());
            dao.update(order);
            // 记录日志
            upOperationLogService.saveOperationLog(UpOperationType.application_deploy_start.getTitle(), ThreadCache.getUser(), UpOperationType.application_deploy_start, UpOrder.class, order.getId(), order.getName(), order);
        }
    }

    @Override
    public void redeploy(UpSimpleOperateBean bean) {
//        Map<Integer, UpApplication> applicationMap = dao.map(UpApplication.class, Arrays.asList(bean.getIdList()));
//        for (UpApplication application : applicationMap.values()) {
//            ThreadCache.initOperationLogLocal(UpOperationType.application_deploy_start.getTitle(), ThreadCache.getUser(), UpOperationType.application_deploy_start, UpApplication.class, application.getId(), application.getName());
//            upTaskService.redeploy(application.getTask().getId());
//            // 记录日志
//            upOperationLogService.saveOperationLog(UpOperationType.application_deploy_start.getTitle(), ThreadCache.getUser(), UpOperationType.application_deploy_start, UpApplication.class, application.getId(), application.getName(), application);
//        }
    }

    @Override
    public void deployReject(UpSimpleOperateBean bean) {
        Map<Integer, UpApplication> applicationMap = dao.map(UpApplication.class, Arrays.asList(bean.getIdList()));
        for (UpApplication application : applicationMap.values()) {
            application.setApplicationStatus(UpApplicationStatus.deploy_reject);
            dao.update(application, "applicationStatus");
        }
    }

    @Override
    public void audit(UpSimpleOperateBean bean) {
        Map<Integer, UpApplication> applicationMap = dao.map(UpApplication.class, Arrays.asList(bean.getIdList()));
        for (UpApplication application : applicationMap.values()) {
            application.setApplicationStatus(UpApplicationStatus.close_success);
            application.setCloseTm(new Date());
            dao.update(application, "applicationStatus", "closeTm");
            UpTask task = new UpTask();
            task.setType(UpTaskType.send_mail_deploy_success);
            task.setTargetId(application.getId());
            task.setName("send_mail_deploy_success:" + application.getName());
            task.setTaskStatus(UpTaskStatus.start);
            dao.insert(task);

            SpVmSearchBean searchBean = new SpVmSearchBean();
            searchBean.setApplicationIdList(new Integer[]{application.getId()});
            List<ReqVm> reqVmList = dao.query(searchBean, new ReqVm());
            for (ReqVm reqVm : reqVmList) {
                searchBean = new SpVmSearchBean();
                SpVm spVm = new SpVm();
                spVm.setReqVm(reqVm);
                for (SpVm vm : dao.query(searchBean, spVm)) {
                    vm.setStatus(RecordStatus.active);
                    dao.update(vm);
                }
            }
        }
    }

    @Override
    public void close(UpSimpleOperateBean bean) {
        Map<Integer, UpApplication> applicationMap = dao.map(UpApplication.class, Arrays.asList(bean.getIdList()));
        for (UpApplication application : applicationMap.values()) {
            if (UpApplicationStatus.pending_submit.equals(application.getApplicationStatus())
                    || UpApplicationStatus.approve_reject.equals(application.getApplicationStatus())
                    || UpApplicationStatus.deploy_reject.equals(application.getApplicationStatus())) {
                application.setApplicationStatus(UpApplicationStatus.close_give_up);
                dao.update(application, "applicationStatus");
            } else if (UpApplicationStatus.deploy_failed.equals(application.getApplicationStatus())) {
                application.setApplicationStatus(UpApplicationStatus.close_error);
                dao.update(application, "applicationStatus");
            }
        }
    }
}
