package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "OVDC")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpOVDCBean.class})
public class SpOVDCBean extends SpRecordBean {
    @ApiModelProperty(value = "EDGE ID")
    private String edgeId;

    @ApiModelProperty(value = "OVDC存储策略列表")
    private SpOVDCStorageProfileBean[] ovdcStorageProfileList;
	
    @ApiModelProperty(value = "OVDC存储列表")
    private SpOVDCStorageBean[] ovdcStorageList;

    @ApiModelProperty(value = "计算网络列表")
    private SpOVDCNetworkBean[] ovdcNetworkList;

    @ApiModelProperty(value = "vlan")
	private String vlan;

	public SpOVDCStorageBean[] getOvdcStorageList() {
		return ovdcStorageList;
	}

	public void setOvdcStorageList(SpOVDCStorageBean[] ovdcStorageList) {
		this.ovdcStorageList = ovdcStorageList;
	}

	public SpOVDCNetworkBean[] getOvdcNetworkList() {
		return ovdcNetworkList;
	}

	public void setOvdcNetworkList(SpOVDCNetworkBean[] ovdcNetworkList) {
		this.ovdcNetworkList = ovdcNetworkList;
	}

	public SpOVDCStorageProfileBean[] getOvdcStorageProfileList() {
		return ovdcStorageProfileList;
	}

	public void setOvdcStorageProfileList(SpOVDCStorageProfileBean[] ovdcStorageProfileList) {
		this.ovdcStorageProfileList = ovdcStorageProfileList;
	}

	public String getEdgeId() {
		return edgeId;
	}

	public void setEdgeId(String edgeId) {
		this.edgeId = edgeId;
	}

	public String getVlan() {
		return vlan;
	}

	public void setVlan(String vlan) {
		this.vlan = vlan;
	}
}
