package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "快照产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductSnapshotSetBean extends RecordBean {

	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "镜像配件ID")
    private Integer snapshotProductItemId;
	
	@ApiModelProperty(value = "单位数")
	private Integer unit;
	
	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getSnapshotProductItemId() {
		return snapshotProductItemId;
	}

	public void setSnapshotProductItemId(Integer snapshotProductItemId) {
		this.snapshotProductItemId = snapshotProductItemId;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

}
