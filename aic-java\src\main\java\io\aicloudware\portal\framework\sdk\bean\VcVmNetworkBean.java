package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vCenter虚拟机网络")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcVmNetworkBean extends VcRecordBean {

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "端口组")
    private String portGroupName;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public String getPortGroupName() {
        return portGroupName;
    }

    public void setPortGroupName(String portGroupName) {
        this.portGroupName = portGroupName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

}
