package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedFreq;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedRet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "备份产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductBackupSetBean extends RecordBean {

	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "备份配件ID")
    private Integer backupProductItemId;
	
	@ApiModelProperty(value = "单位数")
	private Integer unit;
	
	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;
	
	@ApiModelProperty(value = "产品编码")
	private String productCode;
	
	@ApiModelProperty(value = "备份频率")
	private VTBackupSchedFreq schedFreq;
	
	@ApiModelProperty(value = "保留时间")
	private VTBackupSchedRet schedRet;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getBackupProductItemId() {
		return backupProductItemId;
	}

	public void setBackupProductItemId(Integer backupProductItemId) {
		this.backupProductItemId = backupProductItemId;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public VTBackupSchedFreq getSchedFreq() {
		return schedFreq;
	}

	public void setSchedFreq(VTBackupSchedFreq schedFreq) {
		this.schedFreq = schedFreq;
	}

	public VTBackupSchedRet getSchedRet() {
		return schedRet;
	}

	public void setSchedRet(VTBackupSchedRet schedRet) {
		this.schedRet = schedRet;
	}

}
