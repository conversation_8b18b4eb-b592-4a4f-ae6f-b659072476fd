package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductSnapshotSetBean;

@Entity
@Table(name = "up_product_snapshot_set")
@Access(AccessType.FIELD)
public class UpProductSnapshotSet extends BaseUpEntity<UpProductSnapshotSetBean> {

	private static final long serialVersionUID = 3821892672220703882L;

	@Column(name = "payment_type")
	private String paymentType;

	@JoinColumn(name = "snapshot_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem snapshotProductItem;

	@Column(name = "unit")
	private Integer unit;
	
	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "enabled")
	private Boolean enabled;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public UpProductItem getSnapshotProductItem() {
		return snapshotProductItem;
	}
	
	public void setSnapshotProductItem(UpProductItem snapshotProductItem) {
		this.snapshotProductItem = snapshotProductItem;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}
}
