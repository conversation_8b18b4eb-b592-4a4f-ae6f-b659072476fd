package io.aicloudware.portal.framework.sdk.bean.finance;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpFinanceProductType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "消费账单")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpFinanceBillBean extends RecordBean{

	@ApiModelProperty(value = "FID")
	private String fid;
	
	@ApiModelProperty(value = "实例ID")
	private String instance_id;
	
	@ApiModelProperty(value = "产品名")
	private String product_name;

	@ApiModelProperty(value = "产品类型")
	private UpFinanceProductType product_type;
	
	@ApiModelProperty(name = "是否成功付款")
	private Boolean isSuccess;
	
	@ApiModelProperty(name = "账单开始时间")
	private String bill_start_time;
	
	@ApiModelProperty(name = "账单结束时间")
	private String bill_end_time;
	
	@ApiModelProperty(name = "应付金额")
	private BigDecimal bill_amount;

	public String getFid() {
		return fid;
	}

	public void setFid(String fid) {
		this.fid = fid;
	}

	public String getInstance_id() {
		return instance_id;
	}

	public void setInstance_id(String instance_id) {
		this.instance_id = instance_id;
	}

	public String getProduct_name() {
		return product_name;
	}

	public void setProduct_name(String product_name) {
		this.product_name = product_name;
	}

	public UpFinanceProductType getProduct_type() {
		return product_type;
	}

	public void setProduct_type(UpFinanceProductType product_type) {
		this.product_type = product_type;
	}

	public Boolean getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(Boolean isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getBill_start_time() {
		return bill_start_time;
	}

	public void setBill_start_time(String bill_start_time) {
		this.bill_start_time = bill_start_time;
	}

	public String getBill_end_time() {
		return bill_end_time;
	}

	public void setBill_end_time(String bill_end_time) {
		this.bill_end_time = bill_end_time;
	}

	public BigDecimal getBill_amount() {
		return bill_amount;
	}

	public void setBill_amount(BigDecimal bill_amount) {
		this.bill_amount = bill_amount;
	}

}
