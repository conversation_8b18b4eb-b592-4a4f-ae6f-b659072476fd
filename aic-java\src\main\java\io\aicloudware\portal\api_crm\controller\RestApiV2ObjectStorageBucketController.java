package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestObjectStorageBucketService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.bean.SpObjectStorageBucketResultBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3BucketBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3ObjectBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.platform_vcd.entity.SpObjectStorageBucket;
import io.swagger.annotations.*;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping("/api/v2/oss")
@Api(value = "/api/v2/oss", description = "对象存储", position = 81)

public class RestApiV2ObjectStorageBucketController extends BaseEntityController<SpObjectStorageBucket, SpObjectStorageBucketBean, SpObjectStorageBucketResultBean> {
	
	@Autowired
	private IRestObjectStorageBucketService restObjectStorageBucketService;

//	@RequestMapping(value = "/quota/list", method = RequestMethod.POST)
//	@ApiOperation(notes = "/quota/list", httpMethod = "POST", value = "新增配额")
//	@ApiResponses(value = { @ApiResponse(code = 200, message = "新增配额", response = SpObjectStorageBucketResultBean.class) })
//	@ResponseBody
//	public ResponseBean list(HttpServletRequest request) {
//		return ResponseBean.success(restObjectStorageBucketService.quotalist());
//	}
//
//	@RequestMapping(value = "/quota/add", method = RequestMethod.POST)
//	@ApiOperation(notes = "/quota/add", httpMethod = "POST", value = "新增配额")
//	@ApiResponses(value = { @ApiResponse(code = 200, message = "新增配额", response = SpObjectStorageBucketResultBean.class) })
//	@ResponseBody
//	public ResponseBean quotaAdd(@ApiParam(value = "实例对象") @Valid @RequestBody UpOrderQuotaDetailBean bean, HttpServletRequest request) {
//		return ResponseBean.success(restObjectStorageBucketService.quotaAdd(bean));
//	}
//
//	@RequestMapping(value = "/quota/delete/{id}", method = RequestMethod.DELETE)
//	@ApiOperation(notes = "/quota/delete/{id}", httpMethod = "DELETE", value = "删除配额")
//	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpObjectStorageBucketResultBean.class) })
//	@ResponseBody
//	public ResponseBean quotaDelete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//		restObjectStorageBucketService.quotaDelete(id);
//		return ResponseBean.success(true);
//	}

	@RequestMapping(value = "/config", method = RequestMethod.GET)
	@ApiOperation(notes = "/config", httpMethod = "GET", value = "查询实例对象列表")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpObjectStorageBucketResultBean.class) })
	@ResponseBody
	public ResponseBean config() {
		return ResponseBean.success(restObjectStorageBucketService.initS3OrgCephUser());
	}
	
    @RequestMapping(value = "/bucket/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/bucket/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpObjectStorageBucketResultBean.class) })
    @ResponseBody
    public ResponseBean listBuckets() {
        return ResponseBean.success(restObjectStorageBucketService.listBuckets());
    }

    @RequestMapping(value = "/bucket/detail/{name}", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/detail/{name}", httpMethod = "POST", value = "获取详情的跳转路径")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpObjectStorageBucketBean.class) })
	@ResponseBody
	public ResponseBean detail(@ApiParam(value = "对象ID") @PathVariable String name) {
//		String str = "[{\"size\":36,\"name\":\"UpOrderService.class\",\"id\":\"UpOrderService.class\",\"lastModified\":\"2021-03-25 14:01:13\",\"type\":\"file\",\"parentId\":\"\"},{\"size\":7,\"name\":\"UpUserController.class\",\"id\":\"UpUserController.class\",\"lastModified\":\"2021-03-25 10:13:17\",\"type\":\"file\",\"parentId\":\"\"},{\"size\":0,\"name\":\"test\",\"id\":\"test/\",\"lastModified\":\"2021-03-25 13:49:09\",\"type\":\"folder\",\"parentId\":\"\"},{\"size\":36,\"name\":\"UpOrderService.class\",\"id\":\"test/UpOrderService.class\",\"lastModified\":\"2021-03-25 13:50:47\",\"type\":\"file\",\"parentId\":\"test/\"},{\"size\":0,\"name\":\"folder1\",\"id\":\"test/folder1/\",\"lastModified\":\"2021-03-25 13:51:49\",\"type\":\"folder\",\"parentId\":\"test/\"},{\"size\":12,\"name\":\"UpOrderCloudServerController.class\",\"id\":\"test/folder1/UpOrderCloudServerController.class\",\"lastModified\":\"2021-03-25 13:51:57\",\"type\":\"file\",\"parentId\":\"test/folder1/\"}]";
//		return ResponseBean.success(JSONArray.fromObject(str));
		return ResponseBean.success(restObjectStorageBucketService.bucketDetails(name));
	}
    
    @RequestMapping(value = "/bucket/info/{name}", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/info/{name}", httpMethod = "POST", value = "获取详情的跳转路径")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpObjectStorageBucketBean.class) })
	@ResponseBody
	public ResponseBean info(@ApiParam(value = "对象ID") @PathVariable String name) {
		return ResponseBean.success(restObjectStorageBucketService.bucketInfo(name));
	}
	
	@RequestMapping(value = "/bucket/add", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/add", httpMethod = "POST", value = "创建桶")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "创建桶", response = String.class) })
	@ResponseBody
	@AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "创建桶")
    public ResponseBean addBucket(@RequestBody UpS3BucketBean bean, HttpServletRequest request) {
        restObjectStorageBucketService.addBucket(bean.getName());
        return ResponseBean.success(true);
    }

	@RequestMapping(value = "/bucket/delete/{id}", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/delete/{id}", httpMethod = "POST", value = "删除桶")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
	@ResponseBody
	@AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "删除桶")
	public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable String id) {
		AssertUtil.check(restObjectStorageBucketService.bucketDetails(id).size() == 0 , "请先清空桶内的所有内容");
		restObjectStorageBucketService.deleteBucket(id);
		return ResponseBean.success(true);
	}

	@RequestMapping(value = "/bucket/addFile", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/addFile", httpMethod = "POST", value = "文件上传")
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "文件上传")
    public ResponseBean upLoadFile(@RequestParam String bucketId, @RequestParam String id, @RequestParam(value = "file" , required = true) MultipartFile file, HttpServletRequest request) throws IOException {
		boolean isMultipart = ServletFileUpload.isMultipartContent(request);
        // 文件为空，上传失败
        if (file == null || file.isEmpty()||!isMultipart) return ResponseBean.error(2, "Failed", "文件上传失败！");
        UpS3ObjectBean bean = new UpS3ObjectBean();
        bean.setId(id);
        bean.setBucketId(bucketId);
        restObjectStorageBucketService.uploadBucketFile(bean, file);
        return ResponseBean.success(true);
    }
	
	@RequestMapping(value = "/bucket/addFolder", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/addFolder", httpMethod = "POST", value = "新建文件夹")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = Boolean.class) })
	@ResponseBody
	@AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "新建文件夹")
	public ResponseBean folder(@RequestBody UpS3ObjectBean bean) {
		restObjectStorageBucketService.createBucketFolder(bean);
		return ResponseBean.success(true);
	}
	
	@RequestMapping(value = "/bucket/deleteObject", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/deleteObject", httpMethod = "POST", value = "删除对象")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
	@ResponseBody
	@AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "删除对象")
	public ResponseBean deleteObject(@RequestBody UpS3ObjectBean bean) {
		restObjectStorageBucketService.deleteObject(bean);
		return ResponseBean.success(true);
	}
	
	@RequestMapping(value = "/bucket/downloadObject", method = RequestMethod.POST)
	@ApiOperation(notes = "/bucket/downloadObject", httpMethod = "POST", value = "下载")
	@ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = String.class) })
	@ResponseBody
	@AuditLogSpEntity(type = UpProductSystemEnums.ProductType.OBJECT_STORAGE, description = "下载")
	public void downloadObject(@RequestBody UpS3ObjectBean bean, HttpServletRequest request, HttpServletResponse response) {
		restObjectStorageBucketService.downloadObject(bean, request, response);
	}
	
}