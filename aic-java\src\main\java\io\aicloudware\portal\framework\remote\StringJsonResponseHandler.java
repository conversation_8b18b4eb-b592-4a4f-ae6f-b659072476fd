package io.aicloudware.portal.framework.remote;

import io.aicloudware.portal.framework.utility.FileUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;

public class StringJsonResponseHandler extends BaseResponseHandler<String> {
    private static final StringResponseHandler handler = new StringResponseHandler(FileUtil.UTF8);

    public StringJsonResponseHandler() {
    }

    @Override
    public String handleResponse(HttpResponse response) {
    	String callbackStr;
        final StatusLine statusLine = response.getStatusLine();
        HttpEntity entity = response.getEntity();
        if (statusLine.getStatusCode() >= 400) {
        	callbackStr = handler.handleEntity(entity);
        } else if (statusLine.getStatusCode() >= 300) {
        	callbackStr = statusLine.getStatusCode() + ": 服务端地址已跳转" + response.getFirstHeader("Location").getValue();
        } else if (entity != null) {
        	callbackStr = handleEntity(entity);
        } else {
        	callbackStr = statusLine.getStatusCode() + ": 返回的数据对象为空, ResponseBean is null.";
        }
        return callbackStr;
    }

    @Override
    protected String handleEntity(HttpEntity entity) {
        return handler.handleEntity(entity);
    }
}
