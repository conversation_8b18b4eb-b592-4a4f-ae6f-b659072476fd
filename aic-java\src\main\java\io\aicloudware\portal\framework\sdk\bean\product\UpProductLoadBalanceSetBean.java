package io.aicloudware.portal.framework.sdk.bean.product;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡产品套餐")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpProductLoadBalanceSetBean extends RecordBean {

	@ApiModelProperty(value = "付费方式")
	private String paymentType;
	
	@ApiModelProperty(value = "负载均衡配件ID")
    private Integer loadBalanceProductItemId;

	@ApiModelProperty(value = "单位数")
	private Integer unit;

	@ApiModelProperty(value = "价格")
	private BigDecimal price;
	
	@ApiModelProperty(value = "激活状态")
	private Boolean enabled;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getLoadBalanceProductItemId() {
		return loadBalanceProductItemId;
	}

	public void setLoadBalanceProductItemId(Integer loadBalanceProductItemId) {
		this.loadBalanceProductItemId = loadBalanceProductItemId;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	
	
}
