package io.aicloudware.portal.api_up.controller;


import io.aicloudware.portal.api_up.entity.UpVmCost;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/cost")
@Api(value = "/cost", description = "资源计费", position = 510)
public class UpCostController extends BaseUpController<UpVmCost, UpVmCostBean, UpVmCostResultBean> {

//    @Autowired
//    private IUpCostService upCostService;
//
//    @RequestMapping(value = "/group_statistic", method = RequestMethod.POST)
//    @ApiOperation(notes = "/group_statistic", httpMethod = "POST", value = "部门费用统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmCostResultBean.class)})
//    @ResponseBody
//    public ResponseBean groupStatistic(@ApiParam(value = "查询条件") @RequestBody UpVmCostSearchBean searchBean) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//        UpVmCostResultBean rslt = upCostService.groupStatistic(searchBean);
//        return ResponseBean.success(rslt);
//    }
//
//    @RequestMapping(value = "/group_statistic/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/group_statistic/export", httpMethod = "POST", value = "部门费用导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean groupStatisticExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpVmCostSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//
//        String fileName = "";
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = groupStatistic(searchBean);
//        UpVmCostResultBean rslt = responseBean.getData(UpVmCostResultBean.class);
//        UpVmCostBean[] dataList = rslt.getDataList();
//        UpVmCostBean totalCost = rslt.getTotalCost();
//
//        LinkedHashMap<String, String> headMap = getGroupStaticsTitle();
//        List<Map<String, Object>> exportData = getGroupStaticsContent(dataList, totalCost);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//
//        fileName = export(headMap, exportData, searchBean.getExportFileType(), "部门费用统计", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    @RequestMapping(value = "/group_statistic_detail", method = RequestMethod.POST)
//    @ApiOperation(notes = "/group_statistic_detail", httpMethod = "POST", value = "部门费用明细")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回返回文件路径对象列表", response = UpVmCostResultBean.class)})
//    @ResponseBody
//    public ResponseBean groupStatisticDetail(@ApiParam(value = "查询条件") @RequestBody UpVmCostSearchBean searchBean) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//        UpVmCostResultBean rslt = upCostService.groupStatisticDetail(searchBean);
//        return ResponseBean.success(rslt);
//    }
//
//    @RequestMapping(value = "/group_statistic_detail/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/group_statistic_detail/export", httpMethod = "POST", value = "部门费用明细导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean groupStatisticDetailExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpVmCostSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = groupStatisticDetail(searchBean);
//        UpVmCostResultBean rslt = responseBean.getData(UpVmCostResultBean.class);
//        UpVmCostBean[] dataList = rslt.getDataList();
//        UpVmCostBean totalCost = rslt.getTotalCost();
//
//        LinkedHashMap<String, String> headMap = getGroupDetailTitle();
//        List<Map<String, Object>> exportData = getGroupDetailContent(dataList, totalCost);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//        String fileName = export(headMap, exportData, searchBean.getExportFileType(), "部门费用明细", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    @RequestMapping(value = "/app_system_statistic", method = RequestMethod.POST)
//    @ApiOperation(notes = "/app_system_statistic", httpMethod = "POST", value = "应用系统费用统计")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmCostResultBean.class)})
//    @ResponseBody
//    public ResponseBean appSystemStatistic(@ApiParam(value = "查询条件") @RequestBody UpVmCostSearchBean searchBean) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//        UpVmCostResultBean rslt = upCostService.appSystemStatistic(searchBean);
//        return ResponseBean.success(rslt);
//    }
//
//    @RequestMapping(value = "/app_system_statistic/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/app_system_statistic/export", httpMethod = "POST", value = "应用系统费用导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean appSystemStatisticExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpVmCostSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = appSystemStatistic(searchBean);
//        UpVmCostResultBean rslt = responseBean.getData(UpVmCostResultBean.class);
//        UpVmCostBean[] dataList = rslt.getDataList();
//        UpVmCostBean totalCost = rslt.getTotalCost();
//
//        LinkedHashMap<String, String> headMap = getAppSystemStaticsTitle();
//        List<Map<String, Object>> exportData = getAppSystemStaticsContent(dataList, totalCost);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//        String fileName = export(headMap, exportData, searchBean.getExportFileType(), "应用系统费用统计", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    @RequestMapping(value = "/app_system_statistic_detail", method = RequestMethod.POST)
//    @ApiOperation(notes = "/app_system_statistic_detail", httpMethod = "POST", value = "应用系统费用明细")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmCostResultBean.class)})
//    @ResponseBody
//    public ResponseBean appSystemStatisticDetail(@ApiParam(value = "查询条件") @RequestBody UpVmCostSearchBean searchBean) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//        UpVmCostResultBean rslt = upCostService.appSystemStatisticDetail(searchBean);
//        return ResponseBean.success(rslt);
//    }
//
//    @RequestMapping(value = "/app_system_statistic_detail/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/app_system_statistic_detail/export", httpMethod = "POST", value = "应用系统费用明细导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean appSystemStatisticDetailExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpVmCostSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = appSystemStatisticDetail(searchBean);
//        UpVmCostResultBean rslt = responseBean.getData(UpVmCostResultBean.class);
//        UpVmCostBean[] dataList = rslt.getDataList();
//        UpVmCostBean totalCost = rslt.getTotalCost();
//
//        LinkedHashMap<String, String> headMap = getAppSystemDetailTitle();
//        List<Map<String, Object>> exportData = getAppSystemDetailContent(dataList, totalCost);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//        String fileName = export(headMap, exportData, searchBean.getExportFileType(), "应用系统费用明细", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    @RequestMapping(value = "/yearly_start_date_set", method = RequestMethod.POST)
//    @ApiOperation(notes = "/yearly_start_date_set", httpMethod = "POST", value = "年度起始日期设置")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回任务对象", response = UpYearlyStartDateSetResultBean.class)})
//    @ResponseBody
//    public ResponseBean yearlyStartDateSet(@ApiParam(value = "年度起始日期") @RequestBody UpYearlyStartDateBean bean) {
//        UpYearlyStartDateSetResultBean rslt = upCostService.yearlyStartdateSet(bean);
//        return ResponseBean.success(rslt);
//    }
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "虚机费用明细查询")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpVmCostResultBean.class)})
//    @ResponseBody
//    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpVmCostSearchBean searchBean) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//        ResponseBean responseBean = queryEntity(searchBean);
//        UpVmCostResultBean resultBean = responseBean.getData(UpVmCostResultBean.class);
//        return ResponseBean.success(upCostService.query(resultBean, searchBean));
//    }
//
//    @RequestMapping(value = "/query/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query/export", httpMethod = "POST", value = "虚机费用明细导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean vmCostDetailExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpVmCostSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//        AssertUtil.check(null != searchBean.getMainMenuType(), "主菜单模块类型不能为空");
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = query(searchBean);
//        UpVmCostResultBean rslt = responseBean.getData(UpVmCostResultBean.class);
//        UpVmCostBean[] dataList = rslt.getDataList();
//        UpVmCostBean totalCost = rslt.getTotalCost();
//
//        LinkedHashMap<String, String> headMap = getVmCostDetailTitle();
//        List<Map<String, Object>> exportData = getVmCostDetailContent(dataList, totalCost);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//        String fileName = export(headMap, exportData, searchBean.getExportFileType(), "虚机费用明细", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpVmCostBean.class)})
//    @ResponseBody
//    public ResponseBean get(Integer id) throws SDKException {
//        return super.getEntity(id);
//    }
//
//    private LinkedHashMap<String, String> getGroupStaticsTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("0", "序号");
//        headMap.put("1", "年度");
//        headMap.put("2", "部门");
//        headMap.put("3", "虚机(台*天)");
//        headMap.put("4", "CPU(核)");
//        headMap.put("5", "CPU(元)");
//        headMap.put("6", "内存(G)");
//        headMap.put("7", "内存(元)");
//        headMap.put("8", "磁盘(G)");
//        headMap.put("9", "磁盘(元)");
//        headMap.put("10", "费用合计");
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getGroupStaticsContent(UpVmCostBean[] dataList, UpVmCostBean totalCost) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpVmCostBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("0", i + 1);
//            row.put("1", bean.getFiscalYear());
//            row.put("2", bean.getGroupName());
//            row.put("3", bean.getVmNum());
//            row.put("4", bean.getCpuNum());
//            row.put("5", FormatUtil.formatMoney(bean.getCpuMoney()));
//            row.put("6", bean.getMemoryGB());
//            row.put("7", FormatUtil.formatMoney(bean.getMemoryMoney()));
//            row.put("8", bean.getDiskGB());
//            row.put("9", FormatUtil.formatMoney(bean.getDiskMoney()));
//            row.put("10", FormatUtil.formatMoney(bean.getTotalMoney()));
//            exportData.add(row);
//        }
//        Map<String, Object> row = new LinkedHashMap<String, Object>();
//        row.put("0", "合计");
//        row.put("1", "");
//        row.put("2", "");
//        row.put("3", totalCost.getVmNum());
//        row.put("4", totalCost.getCpuNum());
//        row.put("5", FormatUtil.formatMoney(totalCost.getCpuMoney()));
//        row.put("6", totalCost.getMemoryGB());
//        row.put("7", FormatUtil.formatMoney(totalCost.getMemoryMoney()));
//        row.put("8", totalCost.getDiskGB());
//        row.put("9", FormatUtil.formatMoney(totalCost.getDiskMoney()));
//        row.put("10", FormatUtil.formatMoney(totalCost.getTotalMoney()));
//        exportData.add(row);
//        return exportData;
//    }
//
//    private LinkedHashMap<String, String> getGroupDetailTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("0", "序号");
//        headMap.put("1", "日期");
//        headMap.put("2", "虚机(台)");
//        headMap.put("3", "CPU(核)");
//        headMap.put("4", "CPU(元)");
//        headMap.put("5", "内存(G)");
//        headMap.put("6", "内存(元)");
//        headMap.put("7", "磁盘(G)");
//        headMap.put("8", "磁盘(元)");
//        headMap.put("9", "费用合计");
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getGroupDetailContent(UpVmCostBean[] dataList, UpVmCostBean totalCost) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpVmCostBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("0", i + 1);
//            row.put("1", FormatUtil.formatDate(bean.getDate()));
//            row.put("2", bean.getVmNum());
//            row.put("3", bean.getCpuNum());
//            row.put("4", FormatUtil.formatMoney(bean.getCpuMoney()));
//            row.put("5", bean.getMemoryGB());
//            row.put("6", FormatUtil.formatMoney(bean.getMemoryMoney()));
//            row.put("7", bean.getDiskGB());
//            row.put("8", FormatUtil.formatMoney(bean.getDiskMoney()));
//            row.put("9", FormatUtil.formatMoney(bean.getTotalMoney()));
//            exportData.add(row);
//        }
//        Map<String, Object> row = new LinkedHashMap<String, Object>();
//        row.put("0", "合计");
//        row.put("1", "");
//        row.put("2", totalCost.getVmNum());
//        row.put("3", totalCost.getCpuNum());
//        row.put("4", FormatUtil.formatMoney(totalCost.getCpuMoney()));
//        row.put("5", totalCost.getMemoryGB());
//        row.put("6", FormatUtil.formatMoney(totalCost.getMemoryMoney()));
//        row.put("7", totalCost.getDiskGB());
//        row.put("8", FormatUtil.formatMoney(totalCost.getDiskMoney()));
//        row.put("9", FormatUtil.formatMoney(totalCost.getTotalMoney()));
//        exportData.add(row);
//        return exportData;
//    }
//
//    private LinkedHashMap<String, String> getAppSystemStaticsTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("0", "序号");
//        headMap.put("1", "年度");
//        headMap.put("2", "部门");
//        headMap.put("3", "应用系统");
//        headMap.put("4", "虚机(台*天)");
//        headMap.put("5", "CPU(核)");
//        headMap.put("6", "CPU(元)");
//        headMap.put("7", "内存(G)");
//        headMap.put("8", "内存(元)");
//        headMap.put("9", "磁盘(G)");
//        headMap.put("10", "磁盘(元)");
//        headMap.put("11", "费用合计");
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getAppSystemStaticsContent(UpVmCostBean[] dataList, UpVmCostBean totalCost) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpVmCostBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("0", i + 1);
//            row.put("1", bean.getFiscalYear());
//            row.put("2", bean.getGroupName());
//            row.put("3", bean.getAppSystemName());
//            row.put("4", bean.getVmNum());
//            row.put("5", bean.getCpuNum());
//            row.put("6", FormatUtil.formatMoney(bean.getCpuMoney()));
//            row.put("7", bean.getMemoryGB());
//            row.put("8", FormatUtil.formatMoney(bean.getMemoryMoney()));
//            row.put("9", bean.getDiskGB());
//            row.put("10", FormatUtil.formatMoney(bean.getDiskMoney()));
//            row.put("11", FormatUtil.formatMoney(bean.getTotalMoney()));
//            exportData.add(row);
//        }
//        Map<String, Object> row = new LinkedHashMap<String, Object>();
//        row.put("0", "合计");
//        row.put("1", "");
//        row.put("2", "");
//        row.put("3", "");
//        row.put("4", totalCost.getVmNum());
//        row.put("5", totalCost.getCpuNum());
//        row.put("6", FormatUtil.formatMoney(totalCost.getCpuMoney()));
//        row.put("7", totalCost.getMemoryGB());
//        row.put("8", FormatUtil.formatMoney(totalCost.getMemoryMoney()));
//        row.put("9", totalCost.getDiskGB());
//        row.put("10", FormatUtil.formatMoney(totalCost.getDiskMoney()));
//        row.put("11", FormatUtil.formatMoney(totalCost.getTotalMoney()));
//        exportData.add(row);
//        return exportData;
//    }
//
//    private LinkedHashMap<String, String> getAppSystemDetailTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("0", "序号");
//        headMap.put("1", "日期");
//        headMap.put("2", "虚机(台)");
//        headMap.put("3", "CPU(核)");
//        headMap.put("4", "CPU(元)");
//        headMap.put("5", "内存(G)");
//        headMap.put("6", "内存(元)");
//        headMap.put("7", "磁盘(G)");
//        headMap.put("8", "磁盘(元)");
//        headMap.put("9", "费用合计");
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getAppSystemDetailContent(UpVmCostBean[] dataList, UpVmCostBean totalCost) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpVmCostBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("0", i + 1);
//            row.put("1", FormatUtil.formatDate(bean.getDate()));
//            row.put("2", bean.getVmNum());
//            row.put("3", bean.getCpuNum());
//            row.put("4", FormatUtil.formatMoney(bean.getCpuMoney()));
//            row.put("5", bean.getMemoryGB());
//            row.put("6", FormatUtil.formatMoney(bean.getMemoryMoney()));
//            row.put("7", bean.getDiskGB());
//            row.put("8", FormatUtil.formatMoney(bean.getDiskMoney()));
//            row.put("9", FormatUtil.formatMoney(bean.getTotalMoney()));
//            exportData.add(row);
//        }
//        Map<String, Object> row = new LinkedHashMap<String, Object>();
//        row.put("0", "合计");
//        row.put("1", "");
//        row.put("2", totalCost.getVmNum());
//        row.put("3", totalCost.getCpuNum());
//        row.put("4", FormatUtil.formatMoney(totalCost.getCpuMoney()));
//        row.put("5", totalCost.getMemoryGB());
//        row.put("6", FormatUtil.formatMoney(totalCost.getMemoryMoney()));
//        row.put("7", totalCost.getDiskGB());
//        row.put("8", FormatUtil.formatMoney(totalCost.getDiskMoney()));
//        row.put("9", FormatUtil.formatMoney(totalCost.getTotalMoney()));
//        exportData.add(row);
//        return exportData;
//    }
//
//    private LinkedHashMap<String, String> getVmCostDetailTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("0", "序号");
//        headMap.put("1", "日期");
//        headMap.put("2", "部门");
//        headMap.put("3", "应用系统");
//        headMap.put("4", "虚机名称");
//        headMap.put("5", "CPU(核)");
//        headMap.put("6", "CPU(元)");
//        headMap.put("7", "内存(G)");
//        headMap.put("8", "内存(元)");
//        headMap.put("9", "磁盘(G)");
//        headMap.put("10", "磁盘(元)");
//        headMap.put("11", "费用合计");
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getVmCostDetailContent(UpVmCostBean[] dataList, UpVmCostBean totalCost) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpVmCostBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("0", i + 1);
//            row.put("1", FormatUtil.formatDate(bean.getDate()));
//            row.put("2", bean.getGroupName());
//            row.put("3", bean.getAppSystemName());
//            row.put("4", bean.getVmName());
//            row.put("5", bean.getCpuNum());
//            row.put("6", FormatUtil.formatMoney(bean.getCpuMoney()));
//            row.put("7", bean.getMemoryGB());
//            row.put("8", FormatUtil.formatMoney(bean.getMemoryMoney()));
//            row.put("9", bean.getDiskGB());
//            row.put("10", FormatUtil.formatMoney(bean.getDiskMoney()));
//            row.put("11", FormatUtil.formatMoney(bean.getTotalMoney()));
//            exportData.add(row);
//        }
//        Map<String, Object> row = new LinkedHashMap<String, Object>();
//        row.put("0", "合计");
//        row.put("1", "");
//        row.put("2", "");
//        row.put("3", "");
//        row.put("4", "");
//        row.put("5", totalCost.getCpuNum());
//        row.put("6", FormatUtil.formatMoney(totalCost.getCpuMoney()));
//        row.put("7", totalCost.getMemoryGB());
//        row.put("8", FormatUtil.formatMoney(totalCost.getMemoryMoney()));
//        row.put("9", totalCost.getDiskGB());
//        row.put("10", FormatUtil.formatMoney(totalCost.getDiskMoney()));
//        row.put("11", FormatUtil.formatMoney(totalCost.getTotalMoney()));
//        exportData.add(row);
//        return exportData;
//    }
}
