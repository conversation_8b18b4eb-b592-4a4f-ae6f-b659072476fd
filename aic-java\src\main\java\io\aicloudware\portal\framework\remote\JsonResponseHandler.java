package io.aicloudware.portal.framework.remote;

import com.google.gson.JsonSyntaxException;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.exception.SDKException;
import io.aicloudware.portal.framework.utility.FileUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;

public class JsonResponseHandler extends BaseResponseHandler<ResponseBean> {
    private static final StringResponseHandler handler = new StringResponseHandler(FileUtil.UTF8);

    public JsonResponseHandler() {
    }

    @Override
    public ResponseBean handleResponse(HttpResponse response) {
        ResponseBean responseBean;
        final StatusLine statusLine = response.getStatusLine();
        HttpEntity entity = response.getEntity();
        if (statusLine.getStatusCode() >= 400) {
            String errorMsg = handler.handleEntity(entity);
            try {
                responseBean = RemoteUtil.gson.fromJson(errorMsg, ResponseBean.class);
            } catch (JsonSyntaxException e) {
                responseBean = ResponseBean.error(7, statusLine.getStatusCode() + ": 返回的不是Json数据格式", errorMsg);
            }
        } else if (statusLine.getStatusCode() >= 300) {
            String location = response.getFirstHeader("Location").getValue();
            responseBean = ResponseBean.error(8, statusLine.getStatusCode() + ": 服务端地址已跳转", location);
        } else if (entity != null) {
            responseBean = handleEntity(entity);
        } else {
            responseBean = ResponseBean.error(9, statusLine.getStatusCode() + ": 返回的数据对象为空", "ResponseBean is null.");
        }
        if (!responseBean.isSuccess()) {
            throw new SDKException(responseBean);
        }
        return responseBean;
    }

    @Override
    protected ResponseBean handleEntity(HttpEntity entity) {
        String body = handler.handleEntity(entity);
        return RemoteUtil.gson.fromJson(body, ResponseBean.class);
    }
}
