# 部门Controller模块需求文档

## 介绍

本文档定义了为现有部门模块创建REST API控制器的需求。该模块将基于已有的UpDepartment实体类、IUpDepartmentService接口和UpDepartmentService实现类，创建一个完整的部门管理REST API控制器，参考UpUserController的实现风格。

## 需求

### 需求1：基础部门管理API

**用户故事：** 作为系统管理员，我希望通过REST API管理部门信息，以便能够创建、查询、更新和删除部门。

#### 验收标准

1. WHEN 调用POST /department/addOrUpdate接口 THEN 系统应能够创建新部门或更新现有部门
2. WHEN 调用POST /department/query接口 THEN 系统应返回符合查询条件的部门列表
3. WHEN 调用GET /department/load/{id}接口 THEN 系统应返回指定ID的部门详细信息
4. WHEN 调用DELETE /department/delete/{id}接口 THEN 系统应删除指定ID的部门
5. WHEN 部门有子部门时调用删除接口 THEN 系统应返回错误信息阻止删除

### 需求2：部门树结构管理API

**用户故事：** 作为系统管理员，我希望通过API管理部门的层级结构，以便构建组织架构树。

#### 验收标准

1. WHEN 调用GET /department/tree接口 THEN 系统应返回完整的部门树结构
2. WHEN 调用GET /department/tree/{parentId}接口 THEN 系统应返回指定父部门下的子部门树
3. WHEN 创建子部门时 THEN 系统应正确设置父子关系
4. WHEN 查询部门树时 THEN 系统应按照sortOrder和id进行排序

### 需求3：部门用户关系管理API

**用户故事：** 作为系统管理员，我希望通过API管理部门与用户的关系，以便将用户分配到相应部门。

#### 验收标准

1. WHEN 调用POST /department/addUsers接口 THEN 系统应能够将用户添加到指定部门
2. WHEN 调用POST /department/removeUsers接口 THEN 系统应能够从部门中移除用户
3. WHEN 调用GET /department/users/{departmentId}接口 THEN 系统应返回部门内的所有用户关系
4. WHEN 调用GET /department/userDepartments/{userId}接口 THEN 系统应返回用户所属的所有部门
5. WHEN 调用POST /department/setManager接口 THEN 系统应能够设置部门管理员

### 需求4：API文档和错误处理

**用户故事：** 作为API使用者，我希望有完整的API文档和统一的错误处理，以便正确使用部门管理接口。

#### 验收标准

1. WHEN 访问Swagger文档时 THEN 所有部门API应有完整的注解说明
2. WHEN API调用发生错误时 THEN 系统应返回统一格式的错误响应
3. WHEN 传入无效参数时 THEN 系统应返回相应的参数验证错误信息
4. WHEN 操作不存在的部门时 THEN 系统应返回404错误

### 需求5：权限控制和操作日志

**用户故事：** 作为系统管理员，我希望部门操作有适当的权限控制和操作日志记录，以便确保系统安全性和可追溯性。

#### 验收标准

1. WHEN 执行部门操作时 THEN 系统应记录操作日志
2. WHEN 用户无权限访问时 THEN 系统应返回权限不足错误
3. WHEN 操作成功时 THEN 系统应记录操作类型、操作人、操作时间等信息
4. WHEN 查询操作日志时 THEN 系统应能够追溯部门相关的所有操作记录