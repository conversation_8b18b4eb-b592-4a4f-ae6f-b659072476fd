package io.aicloudware.portal.api_vcpp.service.order;

import java.util.List;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;

public interface IUpOrderCloudServerService {

	/**
	 * 密钥
	 * @param userId
	 * @return
	 */
	List<?> getKeyPairs();

	/**
	 * 保存
	 * @param bean
	 * @return
	 */
	Integer save(UpOrderCloudServerBean bean, UpUser applyUser);

	Integer update(UpOrderCloudServerBean bean, UpUser applyUser);

	List<?> getKeyPairs(Integer orgId);

}
