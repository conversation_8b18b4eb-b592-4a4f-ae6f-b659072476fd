package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机列表查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpVmSearchBean extends SearchBean<SpVmBean> {

    @ApiModelProperty(value = "虚机ID列表", hidden = true)
    private Integer[] vmIdList;

    @ApiModelProperty(value = "申请单ID列表")
    private Integer[] applicationIdList;

    @ApiModelProperty(value = "虚机类型")
    SpVmType searchType;

    private Boolean tenantAdmin;

    private Integer bindOwnerId;
    
    public Integer[] getVmIdList() {
        return vmIdList;
    }

    public void setVmIdList(Integer[] vmIdList) {
        this.vmIdList = vmIdList;
    }

    public Integer[] getApplicationIdList() {
        return applicationIdList;
    }

    public void setApplicationIdList(Integer[] applicationIdList) {
        this.applicationIdList = applicationIdList;
    }

    public SpVmType getSearchType() {
        return searchType;
    }

    public void setSearchType(SpVmType searchType) {
        this.searchType = searchType;
    }

    public Boolean getTenantAdmin() {
        return tenantAdmin;
    }

    public void setTenantAdmin(Boolean tenantAdmin) {
        this.tenantAdmin = tenantAdmin;
    }

    public Integer getBindOwnerId() {
        return bindOwnerId;
    }

    public void setBindOwnerId(Integer bindOwnerId) {
        this.bindOwnerId = bindOwnerId;
    }
}
