package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.VcRecordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "vCenter虚拟机磁盘")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VcVmDiskBean extends VcRecordBean {

    @ApiModelProperty(value = "虚机ID")
    private Integer vmId;

    @ApiModelProperty(value = "虚机名称")
    private String vmName;

    @ApiModelProperty(value = "存储")
    private String dataStoreName;

    @ApiModelProperty(value = "磁盘（GB）")
    private Integer diskGB;

    public Integer getVmId() {
        return vmId;
    }

    public void setVmId(Integer vmId) {
        this.vmId = vmId;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public String getDataStoreName() {
        return dataStoreName;
    }

    public void setDataStoreName(String dataStoreName) {
        this.dataStoreName = dataStoreName;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

}
