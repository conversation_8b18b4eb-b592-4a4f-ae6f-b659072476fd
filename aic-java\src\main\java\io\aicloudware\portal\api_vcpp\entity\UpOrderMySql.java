package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Table(name = "up_order_mysql")
@Access(AccessType.FIELD)
public class UpOrderMySql extends UpOrderProduct<UpOrderCloudServerBean> implements IOrderEntity {

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @EntityProperty(isCopyOnUpdate = false)
    @JoinColumn(name = "sp_vapp_id")
    @ManyToOne(fetch = FetchType.LAZY)
    @Where(clause = "status!='deleted'")
    private SpVapp spVapp;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    @Override
    public SpOrg getSpOrg() {
        return spOrg;
    }

    @Override
    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public SpVapp getSpVapp() {
        return spVapp;
    }

    public void setSpVapp(SpVapp spVapp) {
        this.spVapp = spVapp;
    }
}
