package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanRegionRelationBean;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

import javax.persistence.*;

@Entity
@Table(name = "up_service_plan_region_relation")
@Access(AccessType.FIELD)
public final class UpServicePlanRegionRelation extends BaseEntity<UpServicePlanRegionRelationBean> {

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan servicePlan;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "parent_service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpRegionEntity region;

	public UpServicePlan getServicePlan() {
		return servicePlan;
	}

	public void setServicePlan(UpServicePlan servicePlan) {
		this.servicePlan = servicePlan;
	}

	public SpRegionEntity getRegion() {
		return region;
	}

	public void setRegion(SpRegionEntity region) {
		this.region = region;
	}
}
