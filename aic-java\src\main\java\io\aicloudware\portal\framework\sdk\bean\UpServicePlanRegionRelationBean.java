package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "服务计划")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpServicePlanRegionRelationBean.class})
public class UpServicePlanRegionRelationBean extends SpRecordBean {

	private Integer servicePlanId;
	private String servicePlanName;
	private Integer regionId;
	private String regionName;

	public Integer getServicePlanId() {
		return servicePlanId;
	}

	public void setServicePlanId(Integer servicePlanId) {
		this.servicePlanId = servicePlanId;
	}

	public String getServicePlanName() {
		return servicePlanName;
	}

	public void setServicePlanName(String servicePlanName) {
		this.servicePlanName = servicePlanName;
	}

	public Integer getRegionId() {
		return regionId;
	}

	public void setRegionId(Integer regionId) {
		this.regionId = regionId;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}
}
