package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.UpDataListBean;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "应用系统用户关系操作对象")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpAppSystemUserRelationListBean extends UpDataListBean<UpAppSystemUserRelationBean> {
    private Integer appSystemId;

    @Override
    public Integer getAppSystemId() {
        return appSystemId;
    }

    @Override
    public void setAppSystemId(Integer appSystemId) {
        this.appSystemId = appSystemId;
    }
}
