package io.aicloudware.portal.framework.quartz;


import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import io.aicloudware.portal.api_rest.service.IRestMessageService;
import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.Logger;

@Component
public final class MessageJobBean extends QuartzJobBean {
    protected final Logger logger = Logger.getLogger(getClass());

    @Value("${message_job}")
	private String quartz;
    
    @Autowired
    protected ICommonService service;

    @Autowired
	private IUpSystemConfigService configService;
    
    @Autowired
	private IRestMessageService restMessageService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    	UpSystemConfigBean bean = configService.get(UpSystemConfigKey.operation_control);
    	if(bean!=null && StringUtils.isNotEmpty(bean.getValue()) && "yes".equals(bean.getValue())) {
    		return;
    	}
    	if(!"yes".equals(quartz)) {
    		return;
    	}
        List<JobExecutionContext> jobs;
        try {
            jobs = jobExecutionContext.getScheduler().getCurrentlyExecutingJobs();
            for (JobExecutionContext job : jobs) {
                if (job.getTrigger().equals(jobExecutionContext.getTrigger())
                        && !job.getFireInstanceId().equals(jobExecutionContext.getFireInstanceId())) {
//                    logger.debug("There's another instance running, so leaving " + this);
                    return;
                }
            }
        } catch (SchedulerException e) {
            logger.trace("MessageJobBean.executeInternal() error", e);
            return;
        }
        ThreadCache.setSystemAdminLogin();
        restMessageService.sendMessage();
    }
}
