package io.aicloudware.portal.api_rest.framework.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import io.aicloudware.portal.api_rest.framework.bean.RestMessageBean;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageStatus;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageType;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;

@Entity
@Table(name = "rest_monitor")
@Access(AccessType.FIELD)
public class RestMonitor extends BaseEntity<RestMessageBean>{


	/**
	 * 
	 */
	private static final long serialVersionUID = -87379462491834472L;

	@Column(name = "message_status")
	@Enumerated(EnumType.STRING)
	private RestMessageStatus messageStatus;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private RestMessageType type;
	
	// 必传 集团编码(即客户唯一标识)
	@Column(name = "custom_no")
	private String customNo;
	
	@Column(name = "inst_id")
	private String instId;
	
	@Column(name = "warn_title")
	private String warntitle;
	
	@Column(name = "warn_content")
	private String warnContent;
	
	@Column(name = "callback_content", length = ApiConstants.STRING_MAX_LENGTH)
	private String callbackContent;

	public RestMessageStatus getMessageStatus() {
		return messageStatus;
	}

	public void setMessageStatus(RestMessageStatus messageStatus) {
		this.messageStatus = messageStatus;
	}

	public RestMessageType getType() {
		return type;
	}

	public void setType(RestMessageType type) {
		this.type = type;
	}

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}

	public String getInstId() {
		return instId;
	}

	public void setInstId(String instId) {
		this.instId = instId;
	}

	public String getWarntitle() {
		return warntitle;
	}

	public void setWarntitle(String warntitle) {
		this.warntitle = warntitle;
	}

	public String getWarnContent() {
		return warnContent;
	}

	public void setWarnContent(String warnContent) {
		this.warnContent = warnContent;
	}

	public String getCallbackContent() {
		return callbackContent;
	}

	public void setCallbackContent(String callbackContent) {
		this.callbackContent = callbackContent;
	}
	
}
