package io.aicloudware.portal.framework.sdk.bean.profile;

import java.util.Date;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetChannel;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "工单明细")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class})
public final class UpWorkSheetDetailBean extends RecordBean{

	private static final long serialVersionUID = -8214372816000722400L;

	@ApiModelProperty(value = "答复人ID")
    private Integer userId;
	
	@ApiModelProperty(value = "答复人")
    private String userName;
	
	@ApiModelProperty(value = "来源渠道")
	private UpWorkSheetChannel channel;
	
	@ApiModelProperty(value = "发送时间")
	private Date sendTm;

	@ApiModelProperty(value = "内容")
	private String content;

	@ApiModelProperty(value = "工单")
    private UpWorkSheetBean workSheet;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Date getSendTm() {
		return sendTm;
	}

	public void setSendTm(Date sendTm) {
		this.sendTm = sendTm;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public UpWorkSheetBean getWorkSheet() {
		return workSheet;
	}

	public void setWorkSheet(UpWorkSheetBean workSheet) {
		this.workSheet = workSheet;
	}

	public UpWorkSheetChannel getChannel() {
		return channel;
	}

	public void setChannel(UpWorkSheetChannel channel) {
		this.channel = channel;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
}