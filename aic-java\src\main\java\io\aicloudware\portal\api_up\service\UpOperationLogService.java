package io.aicloudware.portal.api_up.service;

import java.util.Date;

import org.springframework.stereotype.Service;

import io.aicloudware.portal.api_up.entity.UpOperationLog;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOperationLevel;
import io.aicloudware.portal.framework.sdk.contants.UpOperationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.service.BaseService;

@Service
public class UpOperationLogService extends BaseService implements IUpOperationLogService {

    @Override
    public <E extends IEntity> void saveSuccess(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
            Integer targetId, String targetName, UpOrder order) {
        UpOperationLog operationLog = new UpOperationLog();

        operationLog.setName(tableName);
        operationLog.setOwner(null == user ? ThreadCache.getUser() : user);
        operationLog.setOperationType(operationType);

        if (UpOperationType.application_reject.equals(operationType)) {
            operationLog.setOperationLevel(UpOperationLevel.high);
        } else if (UpOperationType.application_approve.equals(operationType)
                || UpOperationType.application_deploy_result.equals(operationType)) {
            operationLog.setOperationLevel(UpOperationLevel.medium);
        } else {
            operationLog.setOperationLevel(UpOperationLevel.low);
        }
        operationLog.setOperationStatus(UpOperationStatus.success);
        operationLog.setTargetTable(targetTable == null ? null : targetTable.getSimpleName());
        operationLog.setTargetId(targetId);
        operationLog.setTargetName(targetName);
        operationLog.setOrder(order);

        if (operationLog.getRequestTm() == null) {
            operationLog.setRequestTm(new Date());
        }
        dao.insert(operationLog);
    }

    @Override
    public <E extends IEntity> void saveError(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                              Integer targetId, String targetName, String errorMsg, UpOrder order) {
        UpOperationLog operationLog = new UpOperationLog();

        operationLog.setName(tableName);
        operationLog.setOwner(user);
        operationLog.setOperationType(operationType);

        if (UpOperationType.user_login.equals(operationType)
                || UpOperationType.application_create.equals(operationType)
                || UpOperationType.application_deploy_start.equals(operationType)) {
            operationLog.setOperationLevel(UpOperationLevel.medium);
        } else {
            operationLog.setOperationLevel(UpOperationLevel.high);
        }
        operationLog.setOperationStatus(UpOperationStatus.error);
        operationLog.setTargetTable(targetTable == null ? null : targetTable.getSimpleName());
        operationLog.setTargetId(targetId);
        operationLog.setTargetName(targetName);
        operationLog.setErrorMsg(errorMsg);
        operationLog.setOrder(order);

        if (operationLog.getRequestTm() == null) {
            operationLog.setRequestTm(new Date());
        }
        dao.insert(operationLog);
    }

    @Override
    public <E extends IEntity> void saveOperationLog(String tableName, UpUser user, UpOperationType operationType, Class<E> targetTable,
                                                     Integer targetId, String targetName, UpOrder order) {

        if (null != ThreadCache.operationLogLocal.get()) {

            UpOperationLog operationLog = new UpOperationLog();

            operationLog.setRequestTm(ThreadCache.operationLogLocal.get().getRequestTm());
            operationLog.setRequestUri(ThreadCache.operationLogLocal.get().getRequestUri());
            operationLog.setRemoteIp(ThreadCache.operationLogLocal.get().getRemoteIp());

            operationLog.setName(tableName);
            operationLog.setOwner(null == user ? ThreadCache.getUser() : user);
            operationLog.setOperationType(operationType);
            operationLog.setOperationStatus(UpOperationStatus.success);
            operationLog.setTargetTable(null == targetTable ? null : targetTable.getSimpleName());
            operationLog.setTargetId(targetId);
            operationLog.setTargetName(targetName);
            operationLog.setOrder(order);

            operationLog.setCostMS((int) (System.currentTimeMillis() - ThreadCache.operationLogLocal.get().getRequestTm().getTime()));

            if (UpOperationType.application_reject.equals(operationType)) {
                operationLog.setOperationLevel(UpOperationLevel.high);
            } else if (UpOperationType.application_approve.equals(operationType)
                    || UpOperationType.application_deploy_result.equals(operationType)) {
                operationLog.setOperationLevel(UpOperationLevel.medium);
            } else {
                operationLog.setOperationLevel(UpOperationLevel.low);
            }

            if (operationLog.getRequestTm() == null) {
                operationLog.setRequestTm(new Date());
            }
            dao.insert(operationLog);
        }
    }

}
