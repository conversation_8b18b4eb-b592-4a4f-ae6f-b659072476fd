package io.aicloudware.portal.framework.sdk.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "负载均衡池成员")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpLoadBalancerMemberBean.class})
public class SpLoadBalancerMemberBean extends SpRecordBean {

    @ApiModelProperty(value = "负载均衡池ID")
    private Integer loadBalancerPoolId;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "端口")
    private String port;
    
    @ApiModelProperty(value = "算法")
    private String algorithm;
    
    @ApiModelProperty(value = "监视器端口")
    private String healthCheckPort;
    
    @ApiModelProperty(value = "权重")
    private String weight;
    
    @ApiModelProperty(value = "协议")
    private String protocol;
    
    @ApiModelProperty(value = "最大连接数")
    private Integer maxConnections;
    
    @ApiModelProperty(value = "最小连接数")
    private Integer minConnections;
    
    @ApiModelProperty(value = "云主机名称")
    private String vmName;
    
    @ApiModelProperty(value = "健康状态")
    private String healthStatus;
    
    @ApiModelProperty(value = "健康检查")
    private SpLoadBalancerHealthCheckBean[] loadBalancerHealthCheck;

    public Integer getLoadBalancerPoolId() {
		return loadBalancerPoolId;
	}

	public void setLoadBalancerPoolId(Integer loadBalancerPoolId) {
		this.loadBalancerPoolId = loadBalancerPoolId;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getAlgorithm() {
		return algorithm;
	}

	public void setAlgorithm(String algorithm) {
		this.algorithm = algorithm;
	}

	public String getHealthCheckPort() {
		return healthCheckPort;
	}

	public void setHealthCheckPort(String healthCheckPort) {
		this.healthCheckPort = healthCheckPort;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public Integer getMaxConnections() {
		return maxConnections;
	}

	public void setMaxConnections(Integer maxConnections) {
		this.maxConnections = maxConnections;
	}

	public Integer getMinConnections() {
		return minConnections;
	}

	public void setMinConnections(Integer minConnections) {
		this.minConnections = minConnections;
	}

	public SpLoadBalancerHealthCheckBean[] getLoadBalancerHealthCheck() {
		return loadBalancerHealthCheck;
	}

	public void setLoadBalancerHealthCheck(SpLoadBalancerHealthCheckBean[] loadBalancerHealthCheck) {
		this.loadBalancerHealthCheck = loadBalancerHealthCheck;
	}

	public String getVmName() {
		return vmName;
	}

	public void setVmName(String vmName) {
		this.vmName = vmName;
	}

	public String getHealthStatus() {
		return healthStatus;
	}

	public void setHealthStatus(String healthStatus) {
		this.healthStatus = healthStatus;
	}

	
	
	

}
