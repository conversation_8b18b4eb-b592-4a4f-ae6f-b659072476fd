package io.aicloudware.portal.framework.sdk.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.SpMonitorConditionType;
import io.aicloudware.portal.framework.sdk.contants.SpMonitorRuleType;
import io.aicloudware.portal.framework.sdk.contants.SpMonitorType;
import io.aicloudware.portal.framework.sdk.contants.SpNotificationType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "监控")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpMonitorBean extends SpRecordBean {

    @ApiModelProperty(value = "规则类型")
    private SpMonitorRuleType ruleType;
    
    @ApiModelProperty(value = "条件")
    private SpMonitorConditionType condition;
    
    @ApiModelProperty(value = "监控类型")
    private SpMonitorType monitorType;
    
    @ApiModelProperty(value = "通知类型")
    @Enumerated(EnumType.STRING)
    private SpNotificationType notificationType;

    @ApiModelProperty(value = "阈值1")
    private BigDecimal value1;

    @ApiModelProperty(value = "阈值2")
    private BigDecimal value2;
    
    @ApiModelProperty(value = "虚拟机ID")
    private Integer vmId;
    
    @ApiModelProperty(value = "虚拟机名称")
    private String vmName;
    
    @ApiModelProperty(value = "虚拟机显示名称")
    private String vmDisplayName;
    
    @ApiModelProperty(value = "上次告警时间")
    private Date alarmTm;
    
    @ApiModelProperty(value = "弹性伸缩策略")
    private SpAutoScalingPolicyBean spAutoScalingPolicy;

	public SpMonitorRuleType getRuleType() {
		return ruleType;
	}

	public void setRuleType(SpMonitorRuleType ruleType) {
		this.ruleType = ruleType;
	}

	public SpMonitorConditionType getCondition() {
		return condition;
	}

	public void setCondition(SpMonitorConditionType condition) {
		this.condition = condition;
	}

	public SpNotificationType getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(SpNotificationType notificationType) {
		this.notificationType = notificationType;
	}

	public BigDecimal getValue1() {
		return value1;
	}

	public void setValue1(BigDecimal value1) {
		this.value1 = value1;
	}

	public BigDecimal getValue2() {
		return value2;
	}

	public void setValue2(BigDecimal value2) {
		this.value2 = value2;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public String getVmName() {
		return vmName;
	}

	public void setVmName(String vmName) {
		this.vmName = vmName;
	}

	public String getVmDisplayName() {
		return vmDisplayName;
	}

	public void setVmDisplayName(String vmDisplayName) {
		this.vmDisplayName = vmDisplayName;
	}

	public SpMonitorType getMonitorType() {
		return monitorType;
	}

	public void setMonitorType(SpMonitorType monitorType) {
		this.monitorType = monitorType;
	}

	public Date getAlarmTm() {
		return alarmTm;
	}

	public void setAlarmTm(Date alarmTm) {
		this.alarmTm = alarmTm;
	}

	public SpAutoScalingPolicyBean getSpAutoScalingPolicy() {
		return spAutoScalingPolicy;
	}

	public void setSpAutoScalingPolicy(SpAutoScalingPolicyBean spAutoScalingPolicy) {
		this.spAutoScalingPolicy = spAutoScalingPolicy;
	}

}
