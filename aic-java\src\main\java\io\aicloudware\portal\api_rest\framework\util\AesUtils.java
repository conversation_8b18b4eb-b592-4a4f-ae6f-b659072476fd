package io.aicloudware.portal.api_rest.framework.util;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

import net.sf.json.JSONObject;

/**
 * @Author: zheng.mingdong
 * @Date: Created in 14:50 2020/9/4
 */
public class AesUtils {
    private static final String KEY_ALGORITHM = "AES";

    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";// 默认的加密算法

    private static final String password = "4z@bqu7m$8@iQtyX";
    
    /**
     * 加密
     *
     * @param content
     * @param password
     * @return
     */
    public static String encrypt(String content) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);// 创建密码器

            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8.name());

            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(password));// 初始化为加密模式的密码器

            byte[] result = cipher.doFinal(byteContent);// 加密

            return Base64.encodeBase64String(result);// 通过Base64转码返回
        }
        catch (Exception ex) {
            throw new RuntimeException("加密异常:{}"+ex.toString());
        }

    }

    /**
     * AES 解密操作
     *
     * @param content
     * @param password
     * @return
     */
    public static String decrypt(String content) {
    	
    	try {
            // 实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);

            // 使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(password));

            // 执行操作
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));

            return new String(result, StandardCharsets.UTF_8.name());
        }catch (Exception ex) {
        	throw new RuntimeException("解密异常:{}"+ex.toString());
        }
    }

    /**
     * @description: 生成加密秘钥
     * @author: HWY
     * @date: 2020/10/23 10:36
     * @param: password
     * @return: javax.crypto.spec.SecretKeySpec
     */
    private static SecretKeySpec getSecretKey(final String password) {
    	try {
            KeyGenerator kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG", "SUN");
            random.setSeed(password.getBytes());
            kg.init(128, random);
            // 生成一个密钥
            SecretKey secretKey = kg.generateKey();
            // 转换为AES专用密钥
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        }
        catch (NoSuchAlgorithmException | NoSuchProviderException e) {
        	throw new RuntimeException("生成加密密钥异常:{}"+e.toString());
        }
    }

    public static void main(String[] args) {
    	JSONObject json = new JSONObject();
    	json.put("customNo", "18656201336");
    	json.put("userName", "张三");
    	json.put("userId", "15613365620");
    	
    	System.out.println("catalog01:" + json.toString());
    	System.out.println("encrypt01:" + encrypt(json.toString()));
    	
    	System.out.println("");
    	
    	String beforeEncrypt02 = "{\"customNo\":\"18656201336\",\"userId\":\"15613365620\",\"userName\":\"张三\"}";
    	System.out.println("catalog02:" + beforeEncrypt02);
        System.out.println("encrypt02:" + encrypt(beforeEncrypt02));
        
        System.out.println("");
    	
    	String beforeEncrypt03 = "{\"customNo\":\"18656201336\",\"userName\":\"张三\",\"userId\":\"15613365620\"}";
    	System.out.println("catalog03:" + beforeEncrypt03);
        System.out.println("encrypt03:" + encrypt(beforeEncrypt03));
        
        System.out.println("");
        JSONObject json04 = new JSONObject();
        json04.put("userId", "15613365620");
    	json04.put("customNo", "18656201336");
    	json04.put("userName", "张三");
    	
    	System.out.println("catalog04:" + json04.toString());
    	System.out.println("encrypt04:" + encrypt(json04.toString()));
    	
    	System.out.println(decrypt("aWnAKP05/y9MpX3WBAXOjJ/lv0KzgR+Gu1i+fF/ZX6nPgVySi7PRsFJYvuiHZX5V2s6GnhrGo74ZZzaJKBUPsGD1xg59ox52ut9Lg="));
    }
}
