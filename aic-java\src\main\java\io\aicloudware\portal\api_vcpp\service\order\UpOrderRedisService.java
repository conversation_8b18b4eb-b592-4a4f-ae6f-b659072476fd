package io.aicloudware.portal.api_vcpp.service.order;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudDisk;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudServer;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudServerNetwork;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.entity.UpOrderRedis;
import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.api_vcpp.service.finance.IUpFinanceRechargeService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.OrderUtil;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpDeployStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.SpVmPowerStatus;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudStorageChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.SpCatalog;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpRedis;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplateMachine;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.entity.SpVmDisk;

@Service
@Transactional
public class UpOrderRedisService extends BaseService implements IUpOrderRedisService {

    @Autowired
    private IUpFinanceRechargeService financeRechargeService;

//     @Autowired
//     private IUpOrderMQService orderMQService;

    @Autowired
    private IUpOrderService orderService;

    @Autowired
	private IUpOrderQuotaService quotaService;
    
    @Override
    public Integer save(UpOrderCloudServerBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getRedisType(), "请选择架构类型！");
		AssertUtil.check(bean.getRedisNodeType(), "请选择节点类型！");
		if(bean.getRedisType().equals(RedisType.cluster)) {
			AssertUtil.check(bean.getSharding() != null, "请选择分片数量！");
		}
//        AssertUtil.check(bean.getPaymentType(), "请选择付费方式！");
        AssertUtil.check(StringUtils.isNotEmpty(bean.getName()), "请输入实例名！");
        AssertUtil.check(bean.getServerConfigId(), "请选择Redis规格！");
        // AssertUtil.check(StringUtils.isNotEmpty(bean.getHostname()), "请输入主机名！");
        AssertUtil.check(bean.getVpcId(), "请选择专有网络！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
		AssertUtil.check(bean.getNetworkId(), "请选择子网！");

        SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
 		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
 		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");
 		AssertUtil.check(bean.getAmount() != null && bean.getAmount() >= 1, "请输入购买数量！");
 		
 		bean.setName(bean.getName()+"-"+System.currentTimeMillis());
 		
        UpOrder order = new UpOrder();
        order.setRegion(ThreadCache.getRegion());
        order.setType(OrderType.new_redis);
        order.setName("[" + OrderType.new_redis + "]" + bean.getName());
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setApplyUser(applyUser);
        order.setOwner(user);
        order.setPaymentType(bean.getPaymentType());
        order.setSpOrg(user.getOrg());
        order.setNumber(bean.getAmount());
        if(bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId()) && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
        List<SpCatalog> catalogs = this.dao.list(SpCatalog.class, "name", "redis");
        AssertUtil.check(catalogs != null && catalogs.size() > 0, "Redis服务目录未找到！");
        List<SpVappTemplate> templates = this.dao.list(SpVappTemplate.class, "catalog", catalogs.get(0));
        AssertUtil.check(templates != null && templates.size() > 0, "Redis服务模板未找到！");
        
        SpVappTemplate template = null;
        
        for(SpVappTemplate vappTemplate : templates) {
        	int size = vappTemplate.getVmList().size();
//        	if(bean.getRedisType().equals(RedisType.standard) && size == 1){
//        		template = vappTemplate;
//        		break;
//        	}
        	if(bean.getRedisType().equals(RedisType.cluster) && size > 1) {
        		template = vappTemplate;
        		break;
        	}
        }
        

        AssertUtil.check(template != null, "Redis服务模板未找到！");
        
        AssertUtil.check(financeRechargeService.checkBalance(user.getId()), "账户余额不足！");
        AssertUtil.check(orderService.queryActiveOrder(OrderType.new_redis, user.getId()) == 0, "您有未完成的Redis申请！");

//        bean.setName("redis-"+System.currentTimeMillis());
        
//        SpRedisSearchBean searchBean = new SpRedisSearchBean();
//        SpRedis redisEntity = new SpRedis();
//        redisEntity.setName(bean.getName());
//        redisEntity.setSpOrg(user.getOrg());
//        List<SpRedis> duplicates = dao.query(searchBean, redisEntity);
//        AssertUtil.check(duplicates == null || duplicates.size() == 0, "同名Redis服务已存在！");
        
//        UpProductRedisSet redisSet = this.dao.load(UpProductRedisSet.class, bean.getServerConfigId());
//        AssertUtil.check(redisSet != null && redisSet.getType().equals(bean.getRedisType()), "Redis配置信息异常！");
        UpProductVmSet redisSet = this.dao.load(UpProductVmSet.class, bean.getServerConfigId());
        AssertUtil.check(redisSet != null && redisSet.getType().equals(ProductVmSetType.redis), "Redis配置信息异常！");
        
        Integer cpu = redisSet.getCpuUnit();
//        Integer memory = redisSet.getMemoryUnit();
        Long storage = template.getDiskSize() / 1024L / 1024L / 1024L;

        order.setVmSet(redisSet);
		order.setCpuNum(redisSet.getCpuUnit());
		order.setMemoryNum(bean.getMemory());
		order.setCpuPrice(redisSet.getCpuPrice());
		order.setMemoryPrice(redisSet.getMemoryPrice());
        
        
		List<UpOrderCloudServer> entitys = new ArrayList<>();

        SpVapp spVapp = new SpVapp();
        spVapp.setRegion(order.getRegion());
        spVapp.setName(bean.getName() + "-" + System.currentTimeMillis()
                + String.format("%04d", (int) (Math.random() * 1000)));
        // spVapp.setOrderCloudServer(cloudServer);
        spVapp.setOwner(user);
        spVapp.setSpOrg(user.getOrg());
        spVapp.setDeployStatus(SpDeployStatus.INIT);
        spVapp.setVappTemplate(template);
        spVapp.setVmType(SpVmType.redis);
        List<SpVm> vms = new ArrayList<SpVm>();

        int random1 = 97 + (int) (Math.random() * (26)); // a-z:97-122
        int random2 = 65 + (int) (Math.random() * (26)); // A-Z:65-90
        for (int i = 0; i < template.getVmList().size();) {
            SpVappTemplateMachine machine = template.getVmList().get(i);
            String name = bean.getName() + "_" + String.format("%03d", ++i);
            // String random = System.currentTimeMillis() + String.format("%02d", (int)
            // (Math.random() * 100));
            String random = String.valueOf((char) random1) + String.valueOf((char) random2)
                    + System.currentTimeMillis();
            try {Thread.sleep(1);} catch (InterruptedException e) {}

            UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
            entity.setRegion(order.getRegion());
            entity.setTaskSequence(i * 100);
            entity.setOwner(user);
            entity.setName(name + "-" + random);
            entity.setHostname(random);
            entity.setOrder(order);
            entity.setSpOrg(user.getOrg());
            entity.setChargeType(CloudServerChargeType.count);
            entity.setNetworkId(bean.getNetworkId());
            entity.setImageId(template.getId());
            entity.setTemplateMachine(machine);
            
            entity.setCpu(cpu);
            entity.setMemory(bean.getMemory());
            
            // 处理磁盘
            List<UpOrderCloudDisk> disks = new ArrayList<>();
            int j = 0;

            UpOrderCloudDisk disk = new UpOrderCloudDisk();
            disk.setRegion(order.getRegion());
            disk.setTaskSequence(i * 100 + j * 10);
            disk.setChargeType(CloudStorageChargeType.hour);
            disk.setOrderCloudServer(entity);
            disk.setType(SpVmDiskType.system);
            disk.setName(name + "-" + random + "-" + j);
            disk.setDiskNumber(j);
            disk.setOwner(user);
            disk.setPaymentType(bean.getPaymentType());
            disk.setOrder(order);
            disk.setSpOrg(user.getOrg());
            disk.setDiskGB(storage.intValue());
            disk.setDiskNumber(0);
            disks.add(disk);
            order.setSystemDiskNum(disk.getDiskGB());
            entity.setCloudDiskList(disks);
            
            Integer network1Id = null;
    		List<Integer> networkIdList = queryDao.querySql("select id from sp_ovdc_network where name='vmware-shared-network' and status='active' and sp_org_id=:orgId and region_id=:region", MapUtil.of("orgId", user.getOrg().getId(), "region", entity.getRegion().getId()));
    		AssertUtil.check(networkIdList, "未找到Redis网络1");
    		network1Id = networkIdList.get(0);
    		entity.setNetworkId(network1Id);
    		Integer externalNetworkId = bean.getNetworkId();
            entity.setExternalNetworkId(externalNetworkId);

            List<UpOrderCloudServerNetwork> orderCloudServerNetworkList = new ArrayList<>();
            UpOrderCloudServerNetwork net1 = new UpOrderCloudServerNetwork();
            net1.setRegion(order.getRegion());
            net1.setNicNumber(0);
            net1.setNetworkId(networkIdList.get(0));
            net1.setName(entity.getName()+"-net0");
            net1.setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType.VMXNET3);
            net1.setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode.POOL);
            net1.setFenceMode(UpOrderSystemEnums.FenceMode.bridged);
            net1.setConnected(true);
            net1.setOwner(user);
            net1.setOrderCloudServer(entity);
            net1.setOrder(order);
            orderCloudServerNetworkList.add(net1);

            UpOrderCloudServerNetwork net2 = new UpOrderCloudServerNetwork();
            net2.setRegion(order.getRegion());
            net2.setNicNumber(1);
            net2.setName(entity.getName()+"-net1");
            net2.setNetworkId(externalNetworkId);
            net2.setNetworkAdapterType(UpOrderSystemEnums.NetworkAdapterType.VMXNET3);
            net2.setIpAddressAllocationMode(UpOrderSystemEnums.IpAddressAllocationMode.POOL);
            net2.setFenceMode(UpOrderSystemEnums.FenceMode.bridged);
            net2.setConnected(true);
            net2.setOwner(user);
            net2.setOrder(order);
            net2.setOrderCloudServer(entity);
            orderCloudServerNetworkList.add(net2);
            
            entity.setOrderCloudServerNetworkList(orderCloudServerNetworkList);

            SpVm vm = this.addVm(entity, template);
            vm.setTemplateMachine(machine);
            vms.add(vm);
            entity.setVm(vm);
            entitys.add(entity);
        }
        spVapp.setVmList(vms);

        UpOrderRedis redisOrder = new UpOrderRedis();
        redisOrder.setRegion(order.getRegion());
//        redisOrder.setTemplate(template);
//        redisOrder.setTemplateMachine(machine);
        redisOrder.setRedisType(bean.getRedisType());
        redisOrder.setRedisNodeType(bean.getRedisNodeType());
        redisOrder.setSharding(bean.getSharding());
        redisOrder.setOwner(user);
        redisOrder.setName(spVapp.getName());
        redisOrder.setOrder(order);
        redisOrder.setSpOrg(user.getOrg());
        redisOrder.setSpVapp(spVapp);

        spVapp.setOrder(order);
        this.dao.insert(order);
        this.dao.insert(spVapp);
        this.dao.insert(entitys);
        this.dao.insert(redisOrder);

        SpRedis redis = new SpRedis();
        redis.setRegion(order.getRegion());
        redis.setName(bean.getName());
        redis.setOrder(order);
        redis.setOvdcNetwork(ovdcNetwork);
        redis.setOwner(user);
        redis.setPassword(bean.getPassword());
        redis.setPaymentType(bean.getPaymentType());
        redis.setSharding(bean.getSharding());
        redis.setRedisNodeType(bean.getRedisNodeType());
        redis.setRedisStatus(RedisStatus.init);
        redis.setRedisType(bean.getRedisType());
        redis.setSpOrg(user.getOrg());
        redis.setSpVapp(spVapp);
        
        redis.setSpUuid("urn:vcloud:redis:vapp:"+spVapp.getId());
        this.dao.insert(redis);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setSpOrg(user.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);
//         orderMQService.createOrderMQ(order, entitys);
        quotaService.deployQuotaDetail(bean.getQuotaDetailId());
        return order.getId();
    }

    private SpVm addVm(UpOrderCloudServer cloudServer, SpVappTemplate template) {
        SpVm spVm = new SpVm();
        spVm.setRegion(cloudServer.getRegion());
        spVm.setName(cloudServer.getName());
        spVm.setHostName(cloudServer.getHostname());
        spVm.setSpOrg(cloudServer.getSpOrg());
        spVm.setCpuNum(cloudServer.getCpu());
        spVm.setMemoryGB(cloudServer.getMemory());
        spVm.setVappTemplate(template);
        spVm.setPowerStatus(SpVmPowerStatus.power_on);
        spVm.setOwner(cloudServer.getOwner());
        spVm.setOrder(cloudServer.getOrder());
        spVm.setDeployStatus(SpDeployStatus.INIT);
        //spVm.setVmType(VcdOperationCommon.getVmType(cloudServer.getOrder()));
        spVm.setVmType(OrderUtil.getVmType(cloudServer.getOrder()!=null?cloudServer.getOrder().getType():null));

        List<SpVmDisk> spvmDiskList = new ArrayList<SpVmDisk>();
        int number = 0;
        int diskGB = 0;

        for (UpOrderCloudDisk disk : cloudServer.getCloudDiskList()) {
            SpVmDisk spvmDisk = new SpVmDisk();
            spvmDisk.setRegion(cloudServer.getRegion());
            spvmDisk.setSpOrg(disk.getSpOrg());
            spvmDisk.setName(disk.getName());
            spvmDisk.setDiskGB(disk.getDiskGB());
            spvmDisk.setDiskNumber(number++);
            spvmDisk.setDiskLabel(disk.getName());
            spvmDisk.setDiskPath("");
            spvmDisk.setType(disk.getType());
            spvmDisk.setOrder(disk.getOrder());
            // String spUuid = "urn:vcloud:disk:" +
            // MD5Util.format(MD5Util.encode(disk.getName()));
            // String spUuid = spVm.getSpUuid() + VcdCloudService.SEPERATOR +
            // spvmDisk.getDiskNumber();
            // spvmDisk.setSpUuid(spUuid);
            // spvmDisk.setThinProvisioned(thinProvisioned);
            spvmDiskList.add(spvmDisk);
            diskGB += spvmDisk.getDiskGB();
        }
        spVm.setDiskList(spvmDiskList);
        spVm.setDiskGB(diskGB);
        return spVm;
    }
    
    @Override
	public Integer update(UpOrderCloudServerBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null , "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项！");
		AssertUtil.check(bean.getUpdateVappId(), "请选择变更的REDIS服务器！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_rds_mysql, user.getId()) == 0, "您有未完成的RDS服务器变更单！");
		
		UpOrderQuotaDetail quotaDetail = dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId());
		AssertUtil.check(quotaDetail.getQuota().getOwner().getId().equals(ThreadCache.getUserId()) && quotaDetail.getSpOrg().getId().equals(ThreadCache.getOrgId()),"订单项无操作权限");
		
		SpVapp vapp = this.dao.load(SpVapp.class, bean.getUpdateVappId());
		AssertUtil.check(vapp.getSpOrg().getId().equals(ThreadCache.getOrgId()),"无操作权限！");
//		AssertUtil.check(vapp.getUpdateOrderQuotaDetail().getId().equals(quotaDetail.getId()),"变更操作异常！");
		UpProductVmSet vmSet = this.dao.load(UpProductVmSet.class, bean.getServerConfigId());
		AssertUtil.check(vmSet != null && vmSet.getEnabled() && vmSet.getProductCode().equals(quotaDetail.getProductCode()), "产品编码异常！");
		
		UpOrder order = new UpOrder();
		order.setType(OrderType.redis_update);
		order.setName("[" + OrderType.redis_update + "]" + vapp.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		order.setQuota(quotaDetail.getQuota());
		order.setQuotaDetail(quotaDetail);
		order.setVmSet(vmSet);
		order.setCpuNum(vmSet.getCpuUnit());
		order.setMemoryNum(bean.getMemory());
		order.setCpuPrice(vmSet.getCpuPrice());
		order.setMemoryPrice(vmSet.getMemoryPrice());
		order.setNumber(1);
		
		List<UpOrderCloudServer> datas = new ArrayList<>();
		for(int i = 0 ; i < vapp.getVmList().size() ; i ++) {
			UpOrderCloudServer entity = BeanCopyUtil.copy(bean, UpOrderCloudServer.class);
			entity.setTaskSequence(100);
			entity.setOwner(user);
			entity.setName("update-" + vapp.getVmList().get(i).getName() + "-" + quotaDetail.getSubCode());
			entity.setOrder(order);
			entity.setSpOrg(user.getOrg());
			entity.setCpu(vmSet.getCpuUnit());
			entity.setMemory(bean.getMemory());
			entity.setServerType(bean.getServerType());
			entity.setVm(vapp.getVmList().get(i));
			entity.setUpdateVapp(vapp);
			datas.add(entity);
		}
		
		
		this.dao.insert(order);
		this.dao.insert(datas);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        dao.insert(task);
        quotaService.deployQuotaDetail(quotaDetail.getId());
		return order.getId();
	}

}
