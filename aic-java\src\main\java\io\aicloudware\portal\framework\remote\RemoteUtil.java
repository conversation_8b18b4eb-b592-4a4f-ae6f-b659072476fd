package io.aicloudware.portal.framework.remote;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.fluent.Executor;
import org.apache.http.client.fluent.Form;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.fluent.Response;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.event.Level;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.exception.SystemException;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;

public final class RemoteUtil {
    private static final Logger logger = Logger.getLogger(RemoteUtil.class);

    public static final Gson gson = new GsonBuilder().setDateFormat(FormatUtil.DateTimePattern).create();

    private static final int Retry_Count = 1;
    private static final int Retry_Wait = 3;
    private static HttpHost proxyHost = null;

    static HttpClient CLIENT = null;
    private static Credentials proxyCredentials = null;

    static {
        try {
            HttpClientBuilder b = HttpClientBuilder.create();
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                    return true;
                }
            }).build();
            b.setSslcontext(sslContext);
            HostnameVerifier hostnameVerifier = SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER;
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
            PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager(RegistryBuilder.<ConnectionSocketFactory> create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory()).register("https", sslSocketFactory).build());
            b.setConnectionManager(connMgr);
            CLIENT = b.build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }
    }

    public static String buildUrl(RemoteHost remoteHost, String uri) {
        if (uri.contains("://")) {
            return uri;
        }
        return remoteHost.getHost() + uri;
    }

    public static HttpHost buildHost(String url) {
        if (Utility.isEmpty(url)) {
            return null;
        }
        String schema = url.substring(0, url.indexOf(":"));
        String hostname = url.substring(url.indexOf(":") + 3);
        if (hostname.contains("/")) {
            hostname = hostname.substring(0, hostname.indexOf("/"));
        }
        int port = -1;
        if (hostname.contains(":")) {
            port = Integer.parseInt(hostname.substring(hostname.indexOf(":") + 1));
            hostname = hostname.substring(0, hostname.indexOf(":"));
//        } else if ("http".equals(schema)) {
//            port = 80;
//        } else if ("https".equals(schema)) {
//            port = 443;
        }
        return new HttpHost(hostname, port, schema);
    }

    private static <T> T executeRequest(Request request, RemoteHost host, ResponseHandler<T> handler) {
        if (Utility.isNotEmpty(host.getHeaderList())) {
            for (Header header : host.getHeaderList()) {
                request.addHeader(header);
            }
        }
        for (int i = 0; i < Retry_Count; i++) {
            try {
                Executor executor = Executor.newInstance();
                if (proxyHost != null) {
                    request.viaProxy(proxyHost);
                    if (proxyCredentials != null) {
                        executor.auth(proxyHost, proxyCredentials);
                    }
                }
                if (host.getCredentials() != null) {
                    executor.auth(buildHost(host.getHost()), host.getCredentials());
                }
                if (host.getCookieStore() != null) {
                    executor.cookieStore(host.getCookieStore());
                }
                return executor.execute(request).handleResponse(handler);
            } catch (IOException e) {
                logger.error(request.toString(), e);
                if (i != Retry_Count - 1) {
                    Utility.sleep(Retry_Wait);
                }
            }
        }
        return null;
    }

    public static void setProxyHost(String scheme, String host, String port) {
        if (Utility.isNotEmpty(host)) {
            proxyHost = new HttpHost(host, Utility.isEmpty(port) ? -1 : Integer.parseInt(port), Utility.toNull(scheme));
        } else {
            proxyHost = null;
        }
    }

    public static void setProxyCredentials(String username, String password, String workstation, String domain) {
        if (Utility.isNotEmpty(username) && Utility.isNotEmpty(password)) {
            if (Utility.isNotEmpty(domain)) {
                proxyCredentials = new NTCredentials(username, password, Utility.toNull(workstation), domain);
            } else {
                proxyCredentials = new UsernamePasswordCredentials(username, password);
            }
        } else {
            proxyCredentials = null;
        }
    }

    public static <T> T get(RemoteHost host, String uri, ResponseHandler<T> handler) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        return executeRequest(Request.Get(url), host, handler);
    }

    public static <T> T post(RemoteHost host, String uri, Map<String, String> formDataMap, Map<String, File> uploadFileMap,
                             ResponseHandler<T> handler) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        if (Utility.isNotEmpty(formDataMap)) {
            RemoteUtil.outputLog("= Request ==", formDataMap.toString(), false);
        }
        if (Utility.isNotEmpty(uploadFileMap)) {
            RemoteUtil.outputLog("=== File ===", uploadFileMap.keySet().toString(), false);
        }
        Request request = Request.Post(url);
        if (Utility.isNotEmpty(uploadFileMap)) {
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(Charset.forName(host.getCharset()));
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            ContentType contentType = ContentType.MULTIPART_FORM_DATA.withCharset(host.getCharset());
            for (Map.Entry<String, File> entry : uploadFileMap.entrySet()) {
                if (Utility.isNotEmpty(entry.getValue())) {
                    builder.addBinaryBody(entry.getKey(), entry.getValue(), contentType, entry.getValue().getName());
                }
            }
            if (Utility.isNotEmpty(formDataMap)) {
                contentType = ContentType.create(URLEncodedUtils.CONTENT_TYPE, host.getCharset());
                for (Map.Entry<String, String> entry : formDataMap.entrySet()) {
                    builder.addTextBody(entry.getKey(), entry.getValue(), contentType);
                }
            }
            request.body(builder.build());
        } else if (Utility.isNotEmpty(formDataMap)) {
            Form form = Form.form();
            for (Map.Entry<String, String> entry : formDataMap.entrySet()) {
                form.add(entry.getKey(), entry.getValue());
            }
            if (Utility.isNotEmpty(host.getCharset())) {
                request.bodyForm(form.build(), Charset.forName(host.getCharset()));
            } else {
                request.bodyForm(form.build());
            }
        }
        return executeRequest(request, host, handler);
    }

    private static Response executeRequest(Request request, RemoteHost host) {
        if (Utility.isNotEmpty(host.getHeaderList())) {
            for (Header header : host.getHeaderList()) {
                request.addHeader(header);
            }
        }
        Executor executor = Executor.newInstance(CLIENT);

        if (proxyHost != null) {
            request.viaProxy(proxyHost);
            if (proxyCredentials != null) {
                executor.auth(proxyHost, proxyCredentials);
            }
        }
        if (host.getCredentials() != null) {
            executor.auth(buildHost(host.getHost()), host.getCredentials());
        }
        if (host.getCookieStore() != null) {
            executor.cookieStore(host.getCookieStore());
        }
        try {
            return executor.execute(request);
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }

    private static ResponseBean executeJsonRequest(Request request, RemoteHost host) {
        try {
            return executeRequest(request, host).handleResponse(new JsonResponseHandler());
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }
    
    private static String executeStringJsonRequest(Request request, RemoteHost host) {
        try {
            return executeRequest(request, host).handleResponse(new StringJsonResponseHandler());
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }
    
    private static String executeStringRequest(Request request, RemoteHost host) {
        try {
            return executeRequest(request, host).handleResponse(new StringHandler());
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }

    private static String executeBodyRequest(Request request, RemoteHost host, String charset) {
        try {
            return executeRequest(request, host).handleResponse(new NsxStringResponseHandler(charset));
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }

    public static String stringGet(RemoteHost host, String uri) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        Request request = Request.Get(url);
        return executeStringRequest(request, host);
    }
    
    public static ResponseBean jsonGet(RemoteHost host, String uri) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        Request request = Request.Get(url);
        return executeJsonRequest(request, host);
    }

    public static ResponseBean jsonGet(RemoteHost host, String uri, Object param) {
        String url = buildUrl(host, uri);
        url += "?" + Utility.map2url(Utility.object2map(param));
        RemoteUtil.outputLog("=== URL ====", url, true);
        Request request = Request.Get(url);
        return executeJsonRequest(request, host);
    }

    public static ResponseBean jsonPost(RemoteHost host, String uri, Object param) {
        String url = buildUrl(host, uri);
        Request request = Request.Post(url);
        String jsonBody = null;
        if (param instanceof String) {
            jsonBody = (String) param;
        } else {
            jsonBody = gson.toJson(param);
        }
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", jsonBody, false);
        request.bodyString(jsonBody, ContentType.APPLICATION_JSON);
        return executeJsonRequest(request, host);
    }
    
    public static String stringJsonPost(RemoteHost host, String uri, Object param) {
        String url = buildUrl(host, uri);
        Request request = Request.Post(url);
        String jsonBody = null;
        if(param instanceof String) {
            jsonBody = (String)param;
        } else {
            jsonBody = gson.toJson(param);
        }
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", jsonBody, false);
        request.bodyString(jsonBody, ContentType.APPLICATION_JSON);
        return executeStringJsonRequest(request, host);
    }

    public static String bodyPost(RemoteHost host, String uri, String body, String charset) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", body, false);
        Request request = Request.Post(url);
        request.bodyString(body, ContentType.APPLICATION_JSON.withCharset(charset));
        return executeBodyRequest(request, host, charset);
    }

    public static String bodyPostNsx(RemoteHost host, String uri, String body, String charset) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", body, false);
        Request request = Request.Post(url);
        request.bodyString(body, ContentType.APPLICATION_JSON.withCharset(charset));
        return executeBodyRequestNsx(request, host, charset, uri);
    }

    public static <T> T delete(RemoteHost host, String uri, ResponseHandler<T> handler) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        return executeRequest(Request.Delete(url), host, handler);
    }

    public static ResponseBean jsonPut(RemoteHost host, String uri, Object param) {
        String url = buildUrl(host, uri);
        Request request = Request.Put(url);
        String jsonBody = gson.toJson(param);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", jsonBody, false);
        request.bodyString(jsonBody, ContentType.APPLICATION_JSON);
        return executeJsonRequest(request, host);
    }

    public static ResponseBean jsonDelete(RemoteHost host, String uri) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        Request request = Request.Delete(url);
        return executeJsonRequest(request, host);
    }

    private static long global_count = 10000L;
    private static final ThreadLocal<Long> local_count = new ThreadLocal<>();

    public static void outputLog(String type, String content, boolean init) {
        if (Logger.checkLevel(Level.TRACE)) {
            if (init || local_count.get() == null) {
                global_count++;
                if (global_count >= 100000) {
                    global_count -= 90000;
                }
                local_count.set(global_count);
            }
            System.out.println(type + " " + String.valueOf(local_count.get()).substring(1) + " : " + content);
        }
    }

    public static String bodyPut(RemoteHost host, String uri, String body, String charset) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", body, false);
        Request request = Request.Put(url);
        request.bodyString(body, ContentType.APPLICATION_JSON.withCharset(charset));
        return executeBodyRequest(request, host, charset);
    }

    public static String bodyPutNsx(RemoteHost host, String uri, String body, String charset) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", body, false);
        Request request = Request.Put(url);
        request.bodyString(body, ContentType.APPLICATION_JSON.withCharset(charset));
        return executeBodyRequestNsx(request, host, charset, uri);
    }

    public static String bodyPatchNsx(RemoteHost host, String uri, String body, String charset) {
        String url = buildUrl(host, uri);
        RemoteUtil.outputLog("=== URL ====", url, true);
        RemoteUtil.outputLog("= Request ==", body, false);
        Request request = Request.Patch(url);
        request.bodyString(body, ContentType.APPLICATION_JSON.withCharset(charset));
        return executeBodyRequestNsx(request, host, charset, uri);
    }

    private static String executeBodyRequestNsx(Request request, RemoteHost host, String charset, String uri) {
        try {
            return executeRequest(request, host).handleResponse(new NsxStringResponseHandler(charset));
        } catch (IOException e) {
            throw new SystemException(host.getHost() + " -> " + request.toString(), e);
        }
    }

}
