package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterNodePoolBean;

public interface IUpOrderK8sClusterService {

	Integer save(UpOrderK8sClusterBean bean, UpUser applyUser);

	Integer update(UpOrderK8sClusterBean bean, UpUser applyUser);

	Integer saveNodePool(UpOrderK8sClusterNodePoolBean bean, UpUser user);
}
