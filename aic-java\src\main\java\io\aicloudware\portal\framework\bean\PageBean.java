package io.aicloudware.portal.framework.bean;

import io.swagger.annotations.ApiModelProperty;

public abstract class PageBean extends BaseBean {

    @ApiModelProperty(value = "当前页码")
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize = 20;
    @ApiModelProperty(value = "总页数")
    private Integer pageCount;
    @ApiModelProperty(value = "总记录数")
    private Integer recordCount;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageCount() {
        return pageCount;
    }

    public void setPageCount(Integer pageCount) {
        this.pageCount = pageCount;
    }

    public Integer getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Integer recordCount) {
        this.recordCount = recordCount;
    }
}
