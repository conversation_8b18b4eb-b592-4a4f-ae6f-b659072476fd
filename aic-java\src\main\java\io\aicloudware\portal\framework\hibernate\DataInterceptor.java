package io.aicloudware.portal.framework.hibernate;

import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.utility.Utility;
import org.hibernate.EmptyInterceptor;
import org.hibernate.type.Type;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

@Component
public final class DataInterceptor extends EmptyInterceptor {
    @Override
    public boolean onFlushDirty(Object entity, Serializable id, Object[] currentState, Object[] previousState,
                                String[] propertyNames, Type[] types) {
        int updateBy = getIndex(propertyNames, "updateBy");
        if (updateBy >= 0 && (currentState[updateBy] == null || Utility.isNotZero(ThreadCache.getUserId()))) {
            currentState[updateBy] = Utility.toZero(ThreadCache.getUserId());
        }
        int updateTm = getIndex(propertyNames, "updateTm");
        if (updateTm >= 0) {
            currentState[updateTm] = new Date();
        }
        return true;
    }

    @Override
    public boolean onSave(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types) {
        int createBy = getIndex(propertyNames, "createBy");
        if (createBy >= 0 && state[createBy] == null) {
            state[createBy] = Utility.toZero(ThreadCache.getUserId());
        }
        int createTm = getIndex(propertyNames, "createTm");
        if (createTm >= 0 && state[createTm] == null) {
            state[createTm] = new Date();
        }
        int status = getIndex(propertyNames, "status");
        if (status >= 0 && state[status] == null) {
            state[status] = RecordStatus.active;
        }
        onFlushDirty(entity, id, state, state, propertyNames, types);
        return true;
    }

    private int getIndex(String[] propertyNames, String propertyToSet) {
        return Arrays.asList(propertyNames).indexOf(propertyToSet);
    }
}
