package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_vcpp.entity.UpFormDefinition;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpFormDefinitionBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class UpFormDefinitionService extends BaseService implements IUpFormDefinitionService {

	@Override
	public void add(UpFormDefinitionBean bean) {
		// 将Bean转换为Entity
		UpFormDefinition entity = new UpFormDefinition();
		entity.setName(bean.getName());
		entity.setDescription(bean.getDescription());
		entity.setVersion(1);
		entity.setUuid(UUID.randomUUID().toString());
		// 设置schema字符串
		entity.setSchema(bean.getSchemaString());
		// 插入数据库
		dao.insert(entity);
	}

	@Override
	public void update(UpFormDefinitionBean bean) {
		// 加载实体并校验是否存在
		UpFormDefinition entity = dao.load(UpFormDefinition.class, bean.getId());
		if (entity == null) {
			throw new IllegalArgumentException("表单定义不存在，ID: " + bean.getId());
		}

		// 更新基础信息
		entity.setName(bean.getName());
		entity.setDescription(bean.getDescription());
		entity.setSchema(bean.getSchemaString());

		// 更新数据库
		dao.update(entity);
	}

	@Override
	public void delete(Integer id) {
		UpFormDefinition entity = dao.load(UpFormDefinition.class, id);
		if (entity == null) {
			throw new IllegalArgumentException("表单定义不存在，ID: " + id);
		}
		entity.setStatus(RecordStatus.inactive);
		dao.update(entity);
	}

	@Override
	public UpFormDefinitionBean[] list(UpFormDefinitionBean params) {
		List<UpFormDefinition> entityList = dao.list(UpFormDefinition.class);
		UpFormDefinitionBean[] beanArray = BeanCopyUtil.copy2BeanList(entityList, UpFormDefinitionBean.class);

		// 为每个Bean设置schema字符串，触发JSON转换
		for (int i = 0; i < beanArray.length; i++) {
			beanArray[i].setSchemaString(entityList.get(i).getSchema());
		}

		return beanArray;
	}

	@Override
	public UpFormDefinitionBean getById(Integer id) {
		UpFormDefinition entity = dao.load(UpFormDefinition.class, id);
		if (entity == null || entity.getStatus() != RecordStatus.active) {
			return null;
		}
		UpFormDefinitionBean bean = BeanCopyUtil.copy2Bean(entity, UpFormDefinitionBean.class);
		// 设置schema字符串，触发JSON转换
		bean.setSchemaString(entity.getSchema());
		return bean;
	}

	@Override
	public UpFormDefinitionBean[] query(SearchBean<UpFormDefinitionBean> searchBean) {
		UpFormDefinitionBean searchBeanData = searchBean.getBean();

		if (searchBeanData != null && StringUtils.isNotEmpty(searchBeanData.getName())) {
			UpFormDefinitionBean fuzzyBean = new UpFormDefinitionBean();
			fuzzyBean.setName(searchBeanData.getName());
			searchBean.setFuzzyBean(fuzzyBean);
			searchBeanData.setName(null);
		}

		return dao.query(searchBean, new UpFormDefinition()).stream().map(this::entityToBean).toArray(UpFormDefinitionBean[]::new);
	}

	private UpFormDefinitionBean entityToBean(UpFormDefinition entity) {
		UpFormDefinitionBean bean = new UpFormDefinitionBean();
		bean.setId(entity.getId());
		bean.setName(entity.getName());
		bean.setDescription(entity.getDescription());
		bean.setSchemaString(entity.getSchema());
		bean.setVersion(entity.getVersion());
		bean.setUuid(entity.getUuid());
		return bean;
	}
}
