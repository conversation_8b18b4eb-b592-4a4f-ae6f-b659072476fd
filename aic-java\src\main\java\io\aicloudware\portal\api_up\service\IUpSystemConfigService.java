package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
@Transactional
public interface IUpSystemConfigService {

    public UpSystemConfigBean get(UpSystemConfigKey key);

    public UpSystemConfigListBean query(UpSystemConfigListBean bean);

    public UpSystemConfigListBean save(UpSystemConfigListBean bean);

    public void clearCache();

	public void refreshConfig(UpSystemConfigKey key);

    List<String> logoList();

    /**
     * 上传完整版LOGO
     *
     * @param file 上传的文件
     * @return 上传结果消息
     * @throws Exception 上传过程中的异常
     */
    String uploadFullLogo(MultipartFile file) throws Exception;

    /**
     * 上传迷你版LOGO
     *
     * @param file 上传的文件
     * @return 上传结果消息
     * @throws Exception 上传过程中的异常
     */
    String uploadMiniLogo(MultipartFile file) throws Exception;
}
