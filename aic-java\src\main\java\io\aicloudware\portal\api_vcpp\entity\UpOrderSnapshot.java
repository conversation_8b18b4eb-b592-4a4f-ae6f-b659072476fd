package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.*;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpSnapshot;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Entity
@Table(name = "up_order_snapshot")
@Access(AccessType.FIELD)
public class UpOrderSnapshot extends UpOrderProduct implements IOrderEntity {

    @Column(name = "snapshot_name")
    private String snapshotName;

    @Column(name = "snapshot_comments")
    private String snapshotComments;

    @JoinColumn(name = "snapshot_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpSnapshot snapshot;

    @JoinColumn(name = "cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    public SpSnapshot getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(SpSnapshot snapshot) {
        this.snapshot = snapshot;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
    }

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public String getSnapshotName() {
        return snapshotName;
    }

    public void setSnapshotName(String snapshotName) {
        this.snapshotName = snapshotName;
    }

    public String getSnapshotComments() {
        return snapshotComments;
    }

    public void setSnapshotComments(String snapshotComments) {
        this.snapshotComments = snapshotComments;
    }
}
