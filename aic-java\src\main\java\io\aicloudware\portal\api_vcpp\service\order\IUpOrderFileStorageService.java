package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.SpFileStorageBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderFileStorageBean;

public interface IUpOrderFileStorageService {

	SpFileStorageBean[] queryFileStorage(Integer userId);
	/**
	 * 保存
	 * @param bean
	 * @return
	 */
	Integer save(UpOrderFileStorageBean bean, UpUser applyUser);
	
	Integer change(UpOrderFileStorageBean bean, UpUser applyUser);
	
	Integer quotaSave(UpOrderFileStorageBean bean, UpUser applyUser);

}
