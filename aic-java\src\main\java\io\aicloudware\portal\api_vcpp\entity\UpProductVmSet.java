package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.contants.SpSecurityVersion;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

@Entity
@Table(name = "up_product_vm_set")
@Access(AccessType.FIELD)
public class UpProductVmSet extends BaseUpEntity<UpProductVmSetBean> {

	private static final long serialVersionUID = 2037634668993200385L;

	@Column(name = "product_code")
	private String productCode;

	@Column(name = "payment_type")
	private String paymentType;

	@JoinColumn(name = "cpu_item_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UpProductItem cpuProductItem;
	
	@Column(name = "cpu_unit")
	private Integer cpuUnit;

	@Column(name = "cpu_price")
	private BigDecimal cpuPrice;

	@JoinColumn(name = "memory_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem memoryProductItem;

	@Column(name = "memory_unit")
	private Integer memoryUnit;

	@Column(name = "memory_price")
	private BigDecimal memoryPrice;

	@JoinColumn(name = "disk_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem diskProductItem;

	@Column(name = "unit")
	private Integer diskUnit;

	@Column(name = "price")
	private BigDecimal diskPrice;

	@Column(name = "enabled")
	private Boolean enabled;

	@Column(name = "server_type")
	@Enumerated(EnumType.STRING)
	private ServerType serverType;

	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private ProductVmSetType type;

	@Column(name = "info", length = ApiConstants.STRING_MAX_LENGTH)
	private String info;

	@Column(name = "security_code")
	private String securityCode;

	@JoinColumn(name = "template_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private SpVappTemplate template;

	@Column(name = "dssp_config_id")
	private Integer dsspConfigId;

	@Column(name = "security_version")
	@Enumerated(EnumType.STRING)
	private SpSecurityVersion securityVersion;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public UpProductItem getCpuProductItem() {
		return cpuProductItem;
	}

	public void setCpuProductItem(UpProductItem cpuProductItem) {
		this.cpuProductItem = cpuProductItem;
	}

	public Integer getCpuUnit() {
		return cpuUnit;
	}

	public void setCpuUnit(Integer cpuUnit) {
		this.cpuUnit = cpuUnit;
	}

	public BigDecimal getCpuPrice() {
		return cpuPrice;
	}

	public void setCpuPrice(BigDecimal cpuPrice) {
		this.cpuPrice = cpuPrice;
	}

	public UpProductItem getMemoryProductItem() {
		return memoryProductItem;
	}

	public void setMemoryProductItem(UpProductItem memoryProductItem) {
		this.memoryProductItem = memoryProductItem;
	}

	public Integer getMemoryUnit() {
		return memoryUnit;
	}

	public void setMemoryUnit(Integer memoryUnit) {
		this.memoryUnit = memoryUnit;
	}

	public BigDecimal getMemoryPrice() {
		return memoryPrice;
	}

	public void setMemoryPrice(BigDecimal memoryPrice) {
		this.memoryPrice = memoryPrice;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public ServerType getServerType() {
		return serverType;
	}

	public void setServerType(ServerType serverType) {
		this.serverType = serverType;
	}

	public ProductVmSetType getType() {
		return type;
	}

	public void setType(ProductVmSetType type) {
		this.type = type;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public UpProductItem getDiskProductItem() {
		return diskProductItem;
	}

	public void setDiskProductItem(UpProductItem diskProductItem) {
		this.diskProductItem = diskProductItem;
	}

	public Integer getDiskUnit() {
		return diskUnit;
	}

	public void setDiskUnit(Integer diskUnit) {
		this.diskUnit = diskUnit;
	}

	public BigDecimal getDiskPrice() {
		return diskPrice;
	}

	public void setDiskPrice(BigDecimal diskPrice) {
		this.diskPrice = diskPrice;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public SpVappTemplate getTemplate() {
		return template;
	}

	public void setTemplate(SpVappTemplate template) {
		this.template = template;
	}

	public Integer getDsspConfigId() {
		return dsspConfigId;
	}

	public void setDsspConfigId(Integer dsspConfigId) {
		this.dsspConfigId = dsspConfigId;
	}

	public String getSecurityCode() {
		return securityCode;
	}

	public void setSecurityCode(String securityCode) {
		this.securityCode = securityCode;
	}

	public SpSecurityVersion getSecurityVersion() {
		return securityVersion;
	}

	public void setSecurityVersion(SpSecurityVersion securityVersion) {
		this.securityVersion = securityVersion;
	}
}
