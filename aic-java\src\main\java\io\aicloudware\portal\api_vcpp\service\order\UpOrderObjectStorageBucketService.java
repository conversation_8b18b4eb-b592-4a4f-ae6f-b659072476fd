package io.aicloudware.portal.api_vcpp.service.order;

import java.math.BigDecimal;
import java.net.URI;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderObjectStorageBucket;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.entity.UpProductDiskSet;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderObjectStorageBucketBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ObjectStorageType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpObjectStorageBucket;

import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CreateBucketRequest;
import software.amazon.awssdk.services.s3.model.HeadBucketRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

@Service
@Transactional
public class UpOrderObjectStorageBucketService extends BaseService implements IUpOrderObjectStorageBucketService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService quotaService;

	@Override
	public SpObjectStorageBucketBean[] queryObjectStorageBucket(Integer userId) {
		return BeanCopyUtil.copy2BeanList(
				dao.list(SpObjectStorageBucket.class, "spOrg.id", dao.load(UpUser.class, userId).getOrg().getId()),
				SpObjectStorageBucketBean.class);
	}

	@Override
	public Integer save(UpOrderObjectStorageBucketBean bean, UpUser applyUser) {
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getOwnerId() != null, "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");

		UpOrderObjectStorageBucket entity = BeanCopyUtil.copy(bean, UpOrderObjectStorageBucket.class);
//		AssertUtil.check(orderService.queryActiveOrder(OrderType.object_storage_bucket, user.getId()) == 0, "您有未完成的文件存储申请！");

		entity.setName(bean.getName() + "-" + System.currentTimeMillis());
		UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
		AssertUtil.check(diskSet != null && diskSet.getEnabled()
				&& diskSet.getType().equals(ProductDiskSetType.object_storage_bucket), "云盘配置信息异常！");
//		AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMinValue() <= bean.getSizeG(), "数据盘最小值必须大于"+ diskSet.getMinValue() + "GB");
//		AssertUtil.check(diskSet.getMinValue()!= null && diskSet.getMaxValue() >= bean.getSizeG(), "数据盘最大值必须小于"+ diskSet.getMaxValue() + "GB");
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.object_storage_bucket);
		order.setName("[" + OrderType.object_storage_bucket + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSystemDiskNum(0);
		order.setDiskNum(entity.getSizeG().intValue());
//		order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(entity.getSizeG())));
		order.setDiskPrice(BigDecimal.ZERO);
		order.setDiskSet(diskSet);
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		if (bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
					&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId()), "无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()), "地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		quotaService.deployQuotaDetail(bean.getQuotaDetailId());
		return order.getId();
	}

	@Override
	public Integer change(UpOrderObjectStorageBucketBean bean, UpUser applyUser) {
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getOwnerId() != null, "请选择用户！");

		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getSpObjectStorageBucketId(), "请选择对象存储！");

		SpObjectStorageBucket objectStorageBucket = dao.load(SpObjectStorageBucket.class,
				bean.getSpObjectStorageBucketId());
		AssertUtil.check(user.getOrg().getId().equals(objectStorageBucket.getSpOrg().getId()), "对象存储所属租户信息异常！");

		AssertUtil.check(bean.getSizeG(), "请输入容量！");

		AssertUtil.check(orderService.queryActiveOrder(OrderType.object_storage_bucket_change, user.getId()) == 0,
				"您有未完成的文件存储变更申请！");

		UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
		AssertUtil.check(diskSet != null && diskSet.getEnabled()
				&& diskSet.getType().equals(ProductDiskSetType.object_storage_bucket), "云盘配置信息异常！");
		AssertUtil.check(diskSet.getMinValue() != null && diskSet.getMinValue() <= bean.getSizeG(),
				"数据盘最小值必须大于" + diskSet.getMinValue() + "GB");
		AssertUtil.check(diskSet.getMinValue() != null && diskSet.getMaxValue() >= bean.getSizeG(),
				"数据盘最大值必须小于" + diskSet.getMaxValue() + "GB");
		UpOrder order = new UpOrder();
		order.setRegion(objectStorageBucket.getRegion());
		order.setType(OrderType.object_storage_bucket_change);
		order.setName("[" + OrderType.object_storage_bucket_change + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
		order.setSystemDiskNum(0);
		order.setDiskNum(bean.getSizeG().intValue());
		order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(bean.getSizeG())));
		order.setDiskSet(diskSet);
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		this.dao.insert(order);

		UpOrderObjectStorageBucket entity = new UpOrderObjectStorageBucket();
		entity.setName(objectStorageBucket.getName());
		entity.setSizeG(bean.getSizeG());
		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}

	@Override
	public Integer quotaSave(UpOrderObjectStorageBucketBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null, "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");
//		AssertUtil.check(user.getOrg() != null && bean.getSpOrgId() != null && user.getOrg().getId().equals(bean.getSpOrgId()), "用户信息异常！");

		AssertUtil.check(bean.getName(), "请输入名称！");
		AssertUtil.check(bean.getDiskConfigId(), "缺少存储产品配置！");
//		AssertUtil.check(financeRechargeService.checkBalance(user.getId()),"账户余额不足！");
		bean.setOwnerId(user.getId());
		bean.setType(ObjectStorageType.standard);
		UpOrderObjectStorageBucket entity = BeanCopyUtil.copy(bean, UpOrderObjectStorageBucket.class);

		AssertUtil.check(orderService.queryActiveOrder(OrderType.object_storage_bucket, user.getId()) == 0,
				"您有未完成的对象存储申请！");

		entity.setName(user.getOrg().getName() + "-" + System.currentTimeMillis()
				+ String.format("%04d", (int) (Math.random() * 1000)));

		UpProductDiskSet diskSet = this.dao.load(UpProductDiskSet.class, bean.getDiskConfigId());
		AssertUtil.check(diskSet != null && diskSet.getEnabled(), "存储配置信息异常！");
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.object_storage_bucket);
		order.setName("[" + OrderType.object_storage_bucket + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(applyUser);
//		order.setPaymentType(bean.getPaymentType());
		order.setDiskNum(entity.getSizeG().intValue());
//		order.setDiskPrice(diskSet.getPrice().multiply(BigDecimal.valueOf(entity.getDiskGB())));
		order.setDiskPrice(BigDecimal.ZERO);
		order.setDiskSet(diskSet);
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		if (bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
					&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId()), "无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()), "地市信息异常！");
			order.setQuota(quota);
			order.setQuotaDetail(dao.load(UpOrderQuotaDetail.class, bean.getQuotaDetailId()));
		}
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		quotaService.deployQuotaDetail(bean.getQuotaDetailId());
//		orderMQService.createOrderMQ(order, Lists.newArrayList(entity));
		return order.getId();
	}

//	throws IOException, URISyntaxException
	@Override
	public String createBucket(UpOrderObjectStorageBucketBean bean, UpUser applyUser) {
		AssertUtil.check(bean.getOwnerId() != null, "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");

		if (bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
					&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId()), "无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()), "地市信息异常！");
		}

		Region region = Region.US_EAST_1;
		S3Client s3;
		try {
			s3 = S3Client.builder().region(region).endpointOverride(new URI("http://*************:7480")).build();
			String bucket = "bucket" + System.currentTimeMillis();
			tutorialSetup(s3, bucket, region);
			return "success";
		} catch (Exception e) {
			AssertUtil.check(false, "创建桶异常！");
			return "error";
		}
	}

	public static void tutorialSetup(S3Client s3Client, String bucketName, Region region) {
		try {
			s3Client.createBucket(CreateBucketRequest.builder().bucket(bucketName).build());
			System.out.println("Creating bucket: " + bucketName);
			s3Client.waiter().waitUntilBucketExists(HeadBucketRequest.builder().bucket(bucketName).build());
			System.out.println(bucketName + " is ready.");
			System.out.printf("%n");
		} catch (S3Exception e) {
			System.err.println(e.awsErrorDetails().errorMessage());
			System.exit(1);
		}
	}

	@Override
	public String uploadBucketFile(UpOrderObjectStorageBucketBean bean, UpUser applyUser,MultipartFile file) {
		
		AssertUtil.check(bean.getOwnerId() != null, "请选择用户！");
		UpUser user = dao.load(UpUser.class, bean.getOwnerId());
		AssertUtil.check(user, "所选用户不存在！");

		if (bean.getQuotaId() != null) {
			UpOrderQuota quota = dao.load(UpOrderQuota.class, bean.getQuotaId());
			AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())  && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()), "无操作权限！");
			AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()), "地市信息异常！");
		}
		
		Region region = Region.US_EAST_1;
		S3Client s3;
		try {
			s3 = S3Client.builder().region(region).endpointOverride(new URI("http://*************:7480")).build();
			String bucket = "bucket1614135937391";
			// 保存文件名称
			String name = file.getOriginalFilename();
			String suffixName = name.substring(name.lastIndexOf("."));//获取后缀名
			String fileName="file_"+Utility.getBillno()+suffixName;
			byte [] data = file.getBytes();
	        s3.putObject(PutObjectRequest.builder().bucket(bucket).key(fileName).build(), RequestBody.fromBytes(data));
			return "success";
		} catch (Exception e) {
			System.out.println(e);
//			AssertUtil.check(false, "创建桶异常！");
			return "error";
		}
	}

	
}
