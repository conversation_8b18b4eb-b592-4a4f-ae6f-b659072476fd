package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.GroupSequence;

@ApiModel(value = "计算资源")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpPVDCBean.class})
public class SpPVDCBean extends SpRecordBean {

    @ApiModelProperty(value = "端点ID")
    private Integer pvdcId;

    @ApiModelProperty(value = "端点名称")
    @Length(min = 0, max = 255, message = "{validation.length}", groups = V02.class)
    private String pvdcName;

    @ApiModelProperty(value = "端点显示名称")
    private String pvdcDisplayName;

    @ApiModelProperty(value = "计算资源存储列表")
    private SpPVDCStorageBean[] computeResourceStorageList;
    
    @ApiModelProperty(value = "计算资源网络列表")
    private SpPVDCNetworkBean[] pvdcNetworkList;

    @ApiModelProperty(value = "CPU总计")
    private Long cpuTotal;
    
    @ApiModelProperty(value = "CPU已用")
    private Long cpuUsed;
    
    @ApiModelProperty(value = "内存总计(MB)")
    private Long memoryTotalMb;
    
    @ApiModelProperty(value = "内存已用(MB)")
    private Long memoryUsedMb;
    
    @ApiModelProperty(value = "存储总计(GB)")
    private Long storageTotalGb;
    
    @ApiModelProperty(value = "存储已用(GB)")
    private Long storageUsedGb;
    
    @ApiModelProperty(value = "区域")
    private String regionName;
    
    @ApiModelProperty(value = "云主机数量")
    private Integer numberOfCloudServers;
    
    public Integer getPvdcId() {
		return pvdcId;
	}

	public void setPvdcId(Integer pvdcId) {
		this.pvdcId = pvdcId;
	}

	public String getPvdcName() {
		return pvdcName;
	}

	public void setPvdcName(String pvdcName) {
		this.pvdcName = pvdcName;
	}

	public String getPvdcDisplayName() {
		return pvdcDisplayName;
	}

	public void setPvdcDisplayName(String pvdcDisplayName) {
		this.pvdcDisplayName = pvdcDisplayName;
	}

	public SpPVDCStorageBean[] getComputeResourceStorageList() {
        return computeResourceStorageList;
    }

    public void setComputeResourceStorageList(SpPVDCStorageBean[] storageList) {
        this.computeResourceStorageList = storageList;
    }

	public SpPVDCNetworkBean[] getPvdcNetworkList() {
		return pvdcNetworkList;
	}

	public void setPvdcNetworkList(SpPVDCNetworkBean[] pvdcNetworkList) {
		this.pvdcNetworkList = pvdcNetworkList;
	}

	public Long getCpuTotal() {
		return cpuTotal;
	}

	public void setCpuTotal(Long cpuTotal) {
		this.cpuTotal = cpuTotal;
	}

	public Long getCpuUsed() {
		return cpuUsed;
	}

	public void setCpuUsed(Long cpuUsed) {
		this.cpuUsed = cpuUsed;
	}

	public Long getMemoryTotalMb() {
		return memoryTotalMb;
	}

	public void setMemoryTotalMb(Long memoryTotalMb) {
		this.memoryTotalMb = memoryTotalMb;
	}

	public Long getMemoryUsedMb() {
		return memoryUsedMb;
	}

	public void setMemoryUsedMb(Long memoryUsedMb) {
		this.memoryUsedMb = memoryUsedMb;
	}

	public Long getStorageTotalGb() {
		return storageTotalGb;
	}

	public void setStorageTotalGb(Long storageTotalGb) {
		this.storageTotalGb = storageTotalGb;
	}

	public Long getStorageUsedGb() {
		return storageUsedGb;
	}

	public void setStorageUsedGb(Long storageUsedGb) {
		this.storageUsedGb = storageUsedGb;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public Integer getNumberOfCloudServers() {
		return numberOfCloudServers;
	}

	public void setNumberOfCloudServers(Integer numberOfCloudServers) {
		this.numberOfCloudServers = numberOfCloudServers;
	}
    
    

}
