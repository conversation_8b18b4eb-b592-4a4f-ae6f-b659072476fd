package io.aicloudware.portal.api_rest.framework.bean;

import javax.persistence.MappedSuperclass;

import io.aicloudware.portal.framework.bean.RecordBean;

import io.swagger.annotations.ApiModelProperty;

@MappedSuperclass
public abstract class BaseRestBean extends RecordBean {

	@ApiModelProperty(value = "集团编码")
    private String customNo;

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}
}
