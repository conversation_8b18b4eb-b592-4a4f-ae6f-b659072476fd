package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "操作日志查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpOperationLogSearchBean extends SearchBean<UpOperationLogBean> {

    @ApiModelProperty(value = "操作日志类型列表")
    private UpOperationType[] operationTypeList;

    public UpOperationType[] getOperationTypeList() {
        return operationTypeList;
    }

    public void setOperationTypeList(UpOperationType[] operationTypeList) {
        this.operationTypeList = operationTypeList;
    }

}
