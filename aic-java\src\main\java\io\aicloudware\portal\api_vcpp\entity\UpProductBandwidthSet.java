package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBandwidthSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductBandwidthSetType;

@Entity
@Table(name = "up_product_bandwidth_set")
@Access(AccessType.FIELD)
public class UpProductBandwidthSet extends BaseUpEntity<UpProductBandwidthSetBean> {

	private static final long serialVersionUID = 4759008273976190760L;

	@Column(name = "product_code")
	private String productCode;
	
	@Column(name = "payment_type")
	private String paymentType;

	@JoinColumn(name = "bandwidth_item_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpProductItem bandwidthProductItem;

	@Column(name = "unit")
	private Integer unit;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private ProductBandwidthSetType type;
	
	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "max_value")
	private Integer maxValue;
	
	@Column(name = "min_value")
	private Integer minValue;
	
	@Column(name = "step")
	private Integer step;
	
	@Column(name = "enabled")
	private Boolean enabled;
	
	@Column(name = "ip_number")
	private Integer ipNumber;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public UpProductItem getBandwidthProductItem() {
		return bandwidthProductItem;
	}

	public void setBandwidthProductItem(UpProductItem bandwidthProductItem) {
		this.bandwidthProductItem = bandwidthProductItem;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public Integer getIpNumber() {
		return ipNumber;
	}

	public void setIpNumber(Integer ipNumber) {
		this.ipNumber = ipNumber;
	}

	public ProductBandwidthSetType getType() {
		return type;
	}

	public void setType(ProductBandwidthSetType type) {
		this.type = type;
	}
}
