package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalSceneBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_approval_scene")
@Access(AccessType.FIELD)
public class UpApprovalScene extends BaseUpEntity<UpApprovalSceneBean> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "app_system_type_id")
    private UpCodeMapping appSystemType;

    @Column(name = "application_type")
    @Enumerated(EnumType.STRING)
    private UpApplicationType applicationType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approval_process_id", nullable = false)
    private UpApprovalProcess approvalProcess;

    @Column(name = "priority", nullable = false)
    private Integer priority;

    public UpCodeMapping getAppSystemType() {
        return appSystemType;
    }

    public void setAppSystemType(UpCodeMapping appSystemType) {
        this.appSystemType = appSystemType;
    }

    public UpApplicationType getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(UpApplicationType applicationType) {
        this.applicationType = applicationType;
    }

    public UpApprovalProcess getApprovalProcess() {
        return approvalProcess;
    }

    public void setApprovalProcess(UpApprovalProcess approvalProcess) {
        this.approvalProcess = approvalProcess;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}
