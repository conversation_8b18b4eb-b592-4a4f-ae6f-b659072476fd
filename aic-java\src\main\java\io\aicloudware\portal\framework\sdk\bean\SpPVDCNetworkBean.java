package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "计算资源网络")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpPVDCNetworkBean.class})
public class SpPVDCNetworkBean extends SpRecordBean {

    @ApiModelProperty(value = "计算资源ID")
    private Integer pvdcId;

    @ApiModelProperty(value = "计算资源名称")
    private String pvdcName;

    @ApiModelProperty(value = "计算资源显示名称")
    private String pvdcDisplayName;


}
