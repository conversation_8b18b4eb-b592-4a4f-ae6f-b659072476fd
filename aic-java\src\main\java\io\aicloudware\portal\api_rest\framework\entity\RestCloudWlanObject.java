package io.aicloudware.portal.api_rest.framework.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_rest.framework.bean.RestCloudWlanObjectBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;

@Entity
@Table(name = "rest_cloud_wlan_object")
@Access(AccessType.FIELD)
public class RestCloudWlanObject extends BaseUpEntity<RestCloudWlanObjectBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4650680375905553428L;

	@Column(name = "tenant_id")
	private String doorOrderItemId;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "cloudWlanObject")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<RestVpc> vpcs;

	public String getDoorOrderItemId() {
		return doorOrderItemId;
	}

	public void setDoorOrderItemId(String doorOrderItemId) {
		this.doorOrderItemId = doorOrderItemId;
	}

	public List<RestVpc> getVpcs() {
		return vpcs;
	}

	public void setVpcs(List<RestVpc> vpcs) {
		this.vpcs = vpcs;
	}

}
	