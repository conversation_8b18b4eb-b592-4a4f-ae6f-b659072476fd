package io.aicloudware.portal.api_rest.framework.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_rest.framework.bean.RestMessageBean;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageStatus;
import io.aicloudware.portal.api_rest.framework.enums.RestMessageType;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;

@Entity
@Table(name = "rest_message")
@Access(AccessType.FIELD)
public class RestMessage extends BaseEntity<RestMessageBean>{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5432916534517011516L;

	@Column(name = "message_status")
	@Enumerated(EnumType.STRING)
	private RestMessageStatus messageStatus;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private RestMessageType type;
	
	// 必传 集团编码(即客户唯一标识)
	@Column(name = "custom_no")
	private String customNo;
	
	@Column(name = "inst_id")
	private String instId;
	
	@Column(name = "callback_content", length = ApiConstants.STRING_MAX_LENGTH)
	private String callbackContent;
	
	// 必传 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
	@Column(name = "message_type")
	private String messageType;
	
	// 必传 1=成功 -1 =失败
	@Column(name = "result_code")
	private Integer resultCode;
	
	// 选填 失败原因
	@Column(name = "fail_reason", length = ApiConstants.STRING_MAX_LENGTH)
	private String failReason;
	
	// messageType=1必传 资源操作结果
	@JoinColumn(name = "resource_message_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestResourceMessage resourceMessage;
	
	// messageType=2必传 创建租户消息
	@JoinColumn(name = "create_tenant_message_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestCreateTenantMessage createTenantMessage;
	
	// messageType=4必传 云侧入云结果
	@JoinColumn(name = "cloud_wlan_object_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestCloudWlanObject cloudWlanObject;

	@Column(name = "channel")
	@Enumerated(EnumType.STRING)
	private UpProductSystemEnums.QuotaDetailChannel channel;

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public Integer getResultCode() {
		return resultCode;
	}

	public void setResultCode(Integer resultCode) {
		this.resultCode = resultCode;
	}

	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public RestResourceMessage getResourceMessage() {
		return resourceMessage;
	}

	public void setResourceMessage(RestResourceMessage resourceMessage) {
		this.resourceMessage = resourceMessage;
	}

	public RestCreateTenantMessage getCreateTenantMessage() {
		return createTenantMessage;
	}

	public void setCreateTenantMessage(RestCreateTenantMessage createTenantMessage) {
		this.createTenantMessage = createTenantMessage;
	}

	public RestCloudWlanObject getCloudWlanObject() {
		return cloudWlanObject;
	}

	public void setCloudWlanObject(RestCloudWlanObject cloudWlanObject) {
		this.cloudWlanObject = cloudWlanObject;
	}

	public RestMessageStatus getMessageStatus() {
		return messageStatus;
	}

	public void setMessageStatus(RestMessageStatus messageStatus) {
		this.messageStatus = messageStatus;
	}

	public String getCallbackContent() {
		return callbackContent;
	}

	public void setCallbackContent(String callbackContent) {
		this.callbackContent = callbackContent;
	}

	public String getInstId() {
		return instId;
	}

	public void setInstId(String instId) {
		this.instId = instId;
	}

	public RestMessageType getType() {
		return type;
	}

	public void setType(RestMessageType type) {
		this.type = type;
	}

	public UpProductSystemEnums.QuotaDetailChannel getChannel() {
		return channel;
	}

	public void setChannel(UpProductSystemEnums.QuotaDetailChannel channel) {
		this.channel = channel;
	}
}
