package io.aicloudware.portal.framework.sdk.bean;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "虚机费用统计条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpVmCostSearchBean extends SearchBean<UpVmCostBean> {

    @ApiModelProperty(value = "开始日期（查询条件）")
    private String startDt;

    @ApiModelProperty(value = "结束日期（查询条件）")
    private String endDt;

    @ApiModelProperty(value = "年度（查询条件）")
    private Integer queryYear;

    @ApiModelProperty(value = "应用系统（查询条件）")
    private Integer queryAppSystemId;

    @ApiModelProperty(value = "部门（查询条件）")
    private Integer queryGroupId;

    @ApiModelProperty(value = "虚机名称（查询条件）")
    private String querySpVmName;

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public Integer getQueryYear() {
        return queryYear;
    }

    public void setQueryYear(Integer queryYear) {
        this.queryYear = queryYear;
    }

    public Integer getQueryGroupId() {
        return queryGroupId;
    }

    public void setQueryGroupId(Integer queryGroupId) {
        this.queryGroupId = queryGroupId;
    }

    public Integer getQueryAppSystemId() {
        return queryAppSystemId;
    }

    public void setQueryAppSystemId(Integer queryAppSystemId) {
        this.queryAppSystemId = queryAppSystemId;
    }

    public String getQuerySpVmName() {
        return querySpVmName;
    }

    public void setQuerySpVmName(String querySpVmName) {
        this.querySpVmName = querySpVmName;
    }

}
