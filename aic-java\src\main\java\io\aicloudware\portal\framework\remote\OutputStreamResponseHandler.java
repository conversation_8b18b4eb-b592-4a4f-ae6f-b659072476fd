package io.aicloudware.portal.framework.remote;

import org.apache.http.HttpEntity;

import java.io.IOException;
import java.io.OutputStream;

public final class OutputStreamResponseHandler extends BaseResponseHandler<Void> {
    private final OutputStream outputStream;

    public OutputStreamResponseHandler(OutputStream outputStream) {
        this.outputStream = outputStream;
    }

    @Override
    protected Void handleEntity(HttpEntity entity) throws IOException {
        if (outputStream instanceof MonitorOutputStream) {
            ((MonitorOutputStream) outputStream).setLength(entity.getContentLength());
        }
        entity.writeTo(outputStream);
        outputStream.close();
        return null;
    }
}
