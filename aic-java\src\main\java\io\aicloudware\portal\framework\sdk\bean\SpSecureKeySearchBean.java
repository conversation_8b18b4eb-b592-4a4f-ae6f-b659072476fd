package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "密钥对列表查询条件")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpSecureKeySearchBean extends SearchBean<SpSecureKeyBean> {

    @ApiModelProperty(value = "密钥对ID列表", hidden = true)
    private String[] secureKeyUuidList;

    public String[] getSecureKeyUuidList() {
        return secureKeyUuidList;
    }

    public void setSecureKeyUuidList(String[] secureKeyUuidList) {
        this.secureKeyUuidList = secureKeyUuidList;
    }
}
