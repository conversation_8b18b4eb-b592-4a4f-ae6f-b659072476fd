package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name = "up_system_config")
@Access(AccessType.FIELD)
public class UpSystemConfig extends BaseUpEntity<UpSystemConfigBean> {

    @Column(name = "key", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpSystemConfigKey key;

    @Column(name = "value", nullable = false, length = ApiConstants.STRING_MAX_LENGTH)
    private String value;

    public UpSystemConfigKey getKey() {
        return key;
    }

    public void setKey(UpSystemConfigKey key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
