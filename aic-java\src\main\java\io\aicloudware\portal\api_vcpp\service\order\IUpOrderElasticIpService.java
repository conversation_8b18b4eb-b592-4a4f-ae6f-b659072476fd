package io.aicloudware.portal.api_vcpp.service.order;

import java.util.List;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

public interface IUpOrderElasticIpService {

	public Integer save(UpOrderElasticIpBean bean, UpUser applyUser);
	
	public 	List<String> availableIps(SpOrg entity);

}
