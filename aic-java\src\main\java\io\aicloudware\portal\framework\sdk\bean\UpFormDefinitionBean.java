package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "form配置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpFormDefinitionBean.class})
public class UpFormDefinitionBean extends SpRecordBean {

	@ApiModelProperty(value = "表单描述")
	private String description;

	@ApiModelProperty(value = "表单结构定义")
	@JsonProperty("schema")
	private Object schema;

	// 内部存储字符串格式的schema，用于与Entity交互
	@JsonIgnore
	private String schemaString;

	private Integer version;

	private String uuid;

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Object getSchema() {
		if (schema == null && schemaString != null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				schema = objectMapper.readValue(schemaString, Object.class);
			} catch (Exception e) {
				// 忽略转换错误
			}
		}
		return schema;
	}

	public void setSchema(Object schema) {
		this.schema = schema;
		if (schema != null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				this.schemaString = objectMapper.writeValueAsString(schema);
			} catch (Exception e) {
				// 忽略转换错误
			}
		}
	}

	// 用于与Entity交互的方法
	public String getSchemaString() {
		if (schemaString == null && schema != null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				schemaString = objectMapper.writeValueAsString(schema);
			} catch (Exception e) {
				// 忽略转换错误
			}
		}
		return schemaString;
	}

	public void setSchemaString(String schemaString) {
		this.schemaString = schemaString;
		if (schemaString != null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				this.schema = objectMapper.readValue(schemaString, Object.class);
			} catch (Exception e) {
				// 忽略转换错误
			}
		}
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
}
