package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "K8s集群节点池列表查询结果")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SpK8sClusterNodePoolResultBean extends ResultListBean<SpK8sClusterNodePoolBean> {
}
