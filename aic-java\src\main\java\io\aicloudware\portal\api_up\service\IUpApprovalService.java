package io.aicloudware.portal.api_up.service;


import io.aicloudware.portal.api_up.entity.UpApplication;
import io.aicloudware.portal.api_up.entity.UpApprovalProcessNode;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserListBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Transactional
public interface IUpApprovalService {

    public UpUserListBean toApprove(UpApprovalHistoryBean bean);

    public void doApprove(UpApprovalHistoryBean bean);

    public void doReject(UpApprovalHistoryBean bean);

    public UpApprovalHistoryResultBean getApplicationDetail(Integer applicationId);

    public List<UpUserBean> getApprovers(UpApprovalProcessNode node, UpApplication application);

    public void checkIsUsed(UpApprovalProcessResultBean rsltBean);

    public void checkIsUsed(UpApprovalProcessBean apBean);

    public void initDefaultDataForTenant();

}
