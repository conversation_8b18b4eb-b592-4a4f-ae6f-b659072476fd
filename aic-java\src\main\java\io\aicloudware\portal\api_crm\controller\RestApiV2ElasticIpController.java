package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestElasticIpService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpElasticIpBean;
import io.aicloudware.portal.framework.sdk.bean.SpElasticIpResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpElasticIpSearchBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.framework.sdk.contants.SpIpStatus;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpElasticIp;
import io.aicloudware.portal.platform_vcd.entity.SpIpBinding;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.service.ISpElasticIpService;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Controller
@RequestMapping("/api/v2/elastic_ip")
@Api(value = "/api/v2/elastic_ip", description = "云安全", position = 140)
public class RestApiV2ElasticIpController extends BaseEntityController<SpElasticIp, SpElasticIpBean, SpElasticIpResultBean> {

    @Autowired
    private IRestElasticIpService restElasticIpService;

    @Autowired
    private ISpElasticIpService spElasticIpService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = String.class)})
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "CSSP访问")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody UpOrderElasticIpBean bean) {
        return ResponseBean.success(restElasticIpService.save(bean));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpElasticIpResultBean.class)})
    @ResponseBody
    public ResponseBean queryIpUsage(@ApiParam(value = "查询条件") @RequestBody SpElasticIpSearchBean searchBean) {
        SpElasticIp entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setIpStatus(SpIpStatus.assigned);
        if (entity.getRegion() == null) {
            entity.setRegion(ThreadCache.getRegion());
        }
        if(StringUtils.isNotEmpty(entity.getName())) {
            SpElasticIpBean fuzzyBean = new SpElasticIpBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
        SpElasticIpBean[] entityList = doQuery(searchBean, entity);
        ResultListBean<SpElasticIpBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);

        result.setDataList(entityList);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "解绑并删除公网IP")
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        //check org
        commonService.load(SpElasticIp.class, SpElasticIpBean.class, id, ThreadCache.getOrgId());
        Integer orgId = ThreadCache.getOrgId();
        SpOrg spOrg = new SpOrg();
        spOrg.setId(orgId);

        SpIpBinding outBinding = spElasticIpService.unbindIp(spOrg, id);
        spElasticIpService.delete(spOrg, id);
        return ResponseBean.success(true);
    }
    
}
